classDiagram
    class TransferOrder {
        id: int(PK)
        stNo: varchar(32)
        orderNo: varchar(32)
        refNo: varchar(32)
        status: int + enum
        type: char(64) + enum
        bizType: char(64) + enum
        salesChannelId: int
        storeId: int
        ownerId: int
        shippingWarehouse: varchar(100)
        destCountryCode: varchar(100)
        destWarehouseId: int
        destWarehouseCode: varchar(64)
        destAddress: text
        tradeTerms: char(64) + enum
        paymentTerms: char(64) + enum
        isBorrowed: boolean
        isRelabel: boolean
        estimatedDeliveryTime: datetime
        estimatedArrivalTime: datetime
        actualDeliveryTime: datetime
        qty: int
        boxQty: decimal
        grossWeight: decimal
        netWeight: decimal
        volume: decimal
        billOfLadingType: char(64) + enum(nullable)
        invoiceNo: varchar(64)
        isAllowedShipping: boolean
        closedTime: datetime
        salesStaffId: int
        planerStaffId: int
        shippingStaffId: int
        isDeleted: boolean
        createdBy: int
        createdAt: datetime
        updatedBy: int
        updatedAt: datetime
        TransferOrderItem: TransferOrderItem
        TransferOrderCustomer: TransferOrderCustomer(1..1)
        TransferOrderInbound: TransferOrderInbound
        TransferOrderOutbound: TransferOrderOutbound
        TransportOrderComment: TransportOrderComment
    }

    class TransferOrderItem {
        <<Entity>>
        id: int(PK)
        tsId: int(FK)
        tsNo: varchar(32)(FK)
        psku: varchar(64)
        fnsku: varchar(64)
        salesChannelId: int
        storeId: int
        ownerId: int
        outboundNo: varchar(64)
        qty: int
        relabelFinishTime: datetime
        saleAmount: decimal
        cartonLabelFileId: varchar(64)
        asnLabelFileId: varchar(64)
        borrowedPsku: varchar(64)
        borrowedFnsku: varchar(64)
        newProductLabelFileId: varchar(64)
        borrowedStoreId: int
        borrowedOwnerId: int
        receivedQty: int
        receiptDiscrepancy: int
        putawayQty: int
        putawayDiscrepancy: int
        dimensionUnit: char(32)
        boxLength: decimal
        boxWidth: decimal
        boxHeight: decimal
        weightUnit: char(32)
        weight: decimal
        grossWeight: decimal
        boxGrossWeight: decimal
        quantityPerBox: int
        isDeleted: boolean
        createdBy: int
        createdAt: datetime
        updatedBy: int
        updatedAt: datetime
        TransferOrder: TransferOrder(1..1)
        TransferOrderInbound: TransferOrderInbound(1..*)
        TransferOrderOutbound: TransferOrderOutbound(1..*)
    }

    class TransferOrderInbound {
        <<Entity>>
        id: int(PK)
        tsId: int(FK)
        tsNo: varchar(32)(FK)
        psku: varchar(64)
        fnsku: varchar(64)
        inboundNo: varchar(64)
        qty: int
        expectedPutawayTime: datetime
        receivedStartTime: datetime
        receivedEndTime: datetime
        putawayStartTime: datetime
        putawayEndTime: datetime
        isDeleted: boolean
        createdBy: int
        createdAt: datetime
        updatedBy: int
        updatedAt: datetime
        TransferOrder: TransferOrder(1..1)
        TransferOrderItem: TransferOrderItem(1..*)
    }

    class TransferOrderOutbound {
        <<Entity>>
        id: int(PK)
        tsId: int(FK)
        tsNo: varchar(32)(FK)
        psku: varchar(64)
        fnsku: varchar(64)
        outboundNo: varchar(64)
        qty: int
        departureTime: datetime
        logisticsModeId: int
        isPalletized: boolean
        deliveryRequirement: text
        isBorrowed: boolean
        isRelabel: boolean
        isDeleted: boolean
        createdBy: int
        createdAt: datetime
        updatedBy: int
        updatedAt: datetime
        TransferOrder: TransferOrder(1..1)
        LogisticsMode: LogisticsMode(1..1)
    }

    class TransferOrderCustomer {
        id: int(PK) // could be optional if derived from Parent
        tsId: int(FK)
        tsNo: varchar(32)(FK)
        customerCompanyName: varchar(64)
        customerCountryCode: varchar(64)
        customerContact: varchar(64)
        customerPhone: varchar(64)
        customerEmail: varchar(64)
        isDeleted: boolean
        createdBy: int
        createdAt: datetime
        updatedBy: int
        updatedAt: datetime
        TransferOrder: TransferOrder(1..1)
    }

    class LogisticsMode {
        id: int(PK)
        code: varchar(64)
        name: varchar(64)
        isDeleted: boolean
        createdBy: int
        createdAt: datetime
        updatedBy: int
        updatedAt: datetime
    }

    class TransportOrderComment {
        id: int(PK)
        tsId: int(FK)
        tsNo: varchar(32)(FK)
        comment: varchar
        isDeleted: boolean
        createBy: int
        createTime: datetime
        updateBy: int
        updateTime: datetime
        TransferOrder: TransferOrder(1..*)
    }

    TransferOrder "1" -- "*" TransferOrderItem
    TransferOrder "1" -- "1" TransferOrderCustomer
    TransferOrder "1" -- "*" TransportOrderComment
    TransferOrder "1" -- "*" TransferOrderInbound
    TransferOrder "1" -- "*" TransferOrderOutbound
    TransferOrderOutbound "1" -- "1" LogisticsMode
