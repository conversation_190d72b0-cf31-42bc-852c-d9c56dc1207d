erDiagram
    tms_transfer_order["调拨单"] {
        int id PK
        varchar(32) ts_no "调拨单单号"
        varchar(32) order_no "OMS的单号"
        varchar(32) ref_no "OMS的订单参考号"
        int status "调拨单状态, 字典: transfer_order_status"
        char(64) type "调拨单类型, 根据发货仓和目的仓判断类型, 字典: transfer_order_type: 三方仓调拨至平台仓, 自建仓调拨至平台仓, 三方仓调拨至三方仓, 三方仓调拨至自建仓, 自建仓调拨至三方仓, 三方仓发至客户, 自建仓发至客户"
        char(64) biz_type "业务类型, 字典: transfer_order_biz_type: B2C补货、VC(DO)、B2B客户、换仓"
        char(64) data_source "数据来源, 判断是TMS创建还是上游传入"
        int sales_channel_id "销售渠道ID"
        int store_id "店铺ID"
        int owner_id "货主ID"
        varchar(100) shipping_warehouse "发货仓"
        varchar(100) dest_country_code "目的国/地区"
        int dest_warehouse_id "目的仓库ID"
        varchar(64) dest_warehouse_code "目的仓Code"
        text dest_address "目的地"
        char(64) trade_terms "贸易条款, 字典: trade_terms, B2C/换仓-PMS/OMS的, B2B客户-客户的PO, VC-DI是FOB, DO是DDP"
        char(64) payment_terms "付款条款, 字典: payment_terms"
        boolean is_borrowed "是否借货, 0-否, 1-是"
        boolean is_relabel "是否换标, 0-否, 1-是"
        datetime estimated_delivery_time "预估交货时间"
        datetime estimated_arrival_time "预估交货时间"
        datetime actual_delivery_time "实际交货时间"
        int qty "总数量"
        decimal box_qty "总箱数"
        decimal gross_weight "总毛重"
        decimal net_weight "总净重"
        decimal volume "总体积"
        char(64) bill_of_lading_type "提单类型, 仅B2B客户有,OMS/PMS, 字典: bill_of_lading_type, 无要求, 正本, 电放, 海运"
        varchar(64) invoice_no "发票号"
        boolean is_allowed_shipping "允许发货, 0-不允许, 1-允许"
        datetime closed_time "关闭时间"
        int sales_staff_id "运营人员ID"
        int planer_staff_id "计划人员ID"
        int shipping_staff_id "船务人员ID"
        int is_deleted "是否删除, 0-未删除, 1-删除"
        int created_by "创建人ID"
        datetime created_time "创建时间"
        int updated_by "更新人ID"
        datetime updated_time "更新时间"
    }

    tms_transfer_order_item["调拨单商品"] {
        int id PK
        int ts_id FK "调拨单ID"
        varchar(32) ts_no FK "调拨单号"
        varchar(64) psku "PSKU"
        varchar(64) fnsku "FNSKU"
        int sales_channel_id "销售渠道ID"
        int store_id "店铺ID"
        int owner_id "货主ID"
        varchar(64) outbound_no
        int qty "发货数量"
        datetime relabel_finish_time
        decimal sale_amount "销售金额"
        varchar(64) carton_label_file_id "箱唛文件ID"
        varchar(64) asn_label_file_id "ASN标签文件ID"
        varchar(64) borrowed_psku "出借方PSKU"
        varchar(64) borrowed_fnsku "出借方FNSKU"
        varchar(64) new_product_label_file_id "新产品标签文件ID"
        int borrowed_store_id "出借方店铺ID"
        int borrowed_owner_id "出借方货主ID"
        int received_qty "签收数量"
        int receipt_discrepancy "签收差异"
        int putaway_qty "上架数量"
        int putaway_discrepancy "上架差异"
        char(32) dimension_unit "尺寸单位"
        decimal box_length "外箱尺寸-长"
        decimal box_width "外箱尺寸-宽"
        decimal box_height "外箱尺寸-高"
        char(32) weight_unit "重量单位"
        decimal weight "单品净重"
        decimal gross_weight "单品毛重"
        decimal box_gross_weight "整箱毛重"
        int quantity_per_box "装箱数量"
        int is_deleted "是否删除, 0-未删除, 1-删除"
        int created_by "创建人ID"
        datetime created_time "创建时间"
        int updated_by "更新人ID"
        datetime updated_time "更新时间"
    }

    tms_transfer_order_inbound["调拨单入库"] {
        int id PK
        int ts_id FK "调拨单ID"
        varchar(32) ts_no FK "调拨单号"
        varchar(64) psku "PSKU"
        varchar(64) fnsku "FNSKU"
        varchar(64) inbound_no "入库单号"
        int qty "数量"
        datetime expected_putaway_time "期望上架时间"
        datetime received_start_time "签收开始时间"
        datetime received_end_time "签收结束时间"
        datetime putaway_start_time "上架开始时间"
        datetime putaway_end_time "上架结束时间"
        int is_deleted "是否删除, 0-未删除, 1-删除"
        int created_by "创建人ID"
        datetime created_time "创建时间"
        int updated_by "更新人ID"
        datetime updated_time "更新时间"
    }

    tms_transfer_order_outbound["调拨单出库"] {
        int id PK
        int ts_id FK "调拨单ID"
        varchar(32) ts_no FK "调拨单号"
        varchar(64) psku "PSKU"
        varchar(64) fnsku "FNSKU"
        varchar(64) outbound_no "出库单号"
        int qty "数量"
        datetime departure_time "发货时间"
        int logistics_mode_id "物流方式ID"
        boolean is_palletized "是否打托, 0-否, 1-是"
        text delivery_requirement "发货要求"
        boolean is_borrowed "是否借货, 0-否, 1-是"
        boolean is_relabel "是否换标, 0-否, 1-是"
        int is_deleted "是否删除, 0-未删除, 1-删除"
        int created_by "创建人ID"
        datetime created_time "创建时间"
        int updated_by "更新人ID"
        datetime updated_time "更新时间"
    }

    tms_transfer_order_customer["调拨单客户信息"] {
        int id PK
        int ts_id FK "调拨单ID"
        varchar(32) ts_no FK "调拨单号"
        varchar(64) customer_company_name "客户公司名"
        varchar(64) customer_country_code "客户国家/地区"
        varchar(64) customer_contact "客户联系人"
        varchar(64) customer_phone "客户电话"
        varchar(64) customer_email "客户邮箱"
        int is_deleted "是否删除, 0-未删除, 1-删除"
        int created_by "创建人ID"
        datetime created_time "创建时间"
        int updated_by "更新人ID"
        datetime updated_time "更新时间"
    }

    tms_logistics_mode["物流方式表"] {
        int id PK
        varchar(64) code "物流方式编码"
        varchar(64) name "物流方式名称"
        int is_deleted "是否删除, 0-未删除, 1-删除"
        int created_by "创建人ID"
        datetime created_time "创建时间"
        int updated_by "updated_by"
        datetime updated_time "更新时间"
    }

    tms_transport_order_comment["调拨单批注表"] {
        int id PK "批注ID"
        int ts_id "TS单ID"
        varchar ts_no "TS单号"
        varchar comment "批注内容"
        int is_deleted "是否删除,0=未删除,1=删除"
        int create_by "创建的用户"
        datetime create_time "创建时间"
        int update_by "更新的用户"
        datetime update_time "更新时间"
    }

    tms_inventory_transfer["库存转让单"] {
        int id PK "主键"
        int biz_id "业务单ID"
        varchar(32) biz_no "业务单号"
        char(64) biz_type "业务单类型, 字典: inventory_transfer_biz_type"
        varchar(32) transfer_no "库存转让单号"
        boolean is_relabel "是否换标, 0-否, 1-是"
        text remark "备注"
        varchar(64) from_psku "转出PSKU"
        varchar(64) from_fnsku "转出FNSKU"
        int from_store_id "转出店铺ID"
        int from_owner_id "转出货主ID"
        int from_warehouse_id "转出仓库ID"
        int transfer_quantity "转让数量"
        varchar(64) to_psku "转入PSKU"
        varchar(64) to_fnsku "转入FNSKU"
        int to_store_id "转入店铺ID"
        int to_owner_id "转入货主ID"
        int to_warehouse_id "转入仓库ID"
        datetime relabel_finish_time "换标完成时间"
        int transfer_result "转让结果, 字典: inventory_transfer_result"
        text fail_reason "失败原因"
        int is_deleted "是否删除, 0-未删除, 1-删除"
        int created_by "创建人ID"
        datetime created_time "创建时间"
        int updated_by "更新人ID"
        datetime updated_time "更新时间"
    }

    tms_inbound_record["签收记录表（三期已存在）"]
    tms_push_task["推送任务表（三期已存在）"]

    tms_transfer_order ||--|{ tms_transfer_order_item: "contains"
    tms_transfer_order ||--|| tms_transfer_order_customer: "use"
    tms_transfer_order ||--|| tms_transport_order_comment: "related"
    tms_transfer_order ||--|{ tms_transfer_order_inbound: "related"
    tms_transfer_order ||--|{ tms_transfer_order_outbound: "related"
    tms_transfer_order_item ||--|{ tms_transfer_order_inbound: "related"
    tms_transfer_order_item ||--|{ tms_transfer_order_outbound: "related"
    tms_transfer_order_outbound }|--|| tms_logistics_mode: "use"
    tms_transfer_order_item ||--||  tms_inventory_transfer: "related"
    tms_transfer_order_item ||--|{ tms_inbound_record: ""
    tms_transfer_order_item ||--|{ tms_push_task: ""