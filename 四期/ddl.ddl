-- 调拨单主表
CREATE TABLE IF NOT EXISTS `tms_transfer_order`
(
    `id`                      INT            NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `ts_no`                   VARCHAR(32)    NOT NULL COMMENT '调拨单单号',
    `order_no`                VARCHAR(32)             DEFAULT NULL COMMENT 'OMS的单号',
    `ref_no`                  VARCHAR(32)             DEFAULT NULL COMMENT 'OMS的订单参考号',
    `status`                  INT            NOT NULL COMMENT '调拨单状态, 字典: transfer_order_status',
    `type`                    CHAR(64)                DEFAULT NULL COMMENT '调拨单类型, 根据发货仓和目的仓判断类型, 字典: transfer_order_type: 三方仓调拨至平台仓, 自建仓调拨至平台仓, 三方仓调拨至三方仓, 三方仓调拨至自建仓, 自建仓调拨至三方仓, 三方仓发至客户, 自建仓发至客户',
    `biz_type`                CHAR(64)                DEFAULT NULL COMMENT '业务类型, 字典: transfer_order_biz_type: B2C补货、VC(DO)、B2B客户、换仓',
    `data_source`             CHAR(64)                DEFAULT NULL COMMENT '数据来源, 判断是TMS创建还是上游传入',
    `sales_channel_id`        INT            NOT NULL COMMENT '销售渠道ID',
    `store_id`                INT            NOT NULL COMMENT '店铺ID',
    `owner_id`                INT            NOT NULL COMMENT '货主ID',
    `shipping_warehouse`      VARCHAR(100)            DEFAULT NULL COMMENT '发货仓',
    `dest_country_code`       VARCHAR(100)            DEFAULT NULL COMMENT '目的国/地区',
    `dest_warehouse_id`       INT                     DEFAULT NULL COMMENT '目的仓库ID',
    `dest_warehouse_code`     VARCHAR(64)             DEFAULT NULL COMMENT '目的仓Code',
    `dest_address`            TEXT                    DEFAULT NULL COMMENT '目的地',
    `trade_terms`             CHAR(64)                DEFAULT NULL COMMENT '贸易条款, 字典: trade_terms, B2C/换仓-PMS/OMS的, B2B客户-客户的PO, VC-DI是FOB, DO是DDP',
    `payment_terms`           CHAR(64)                DEFAULT NULL COMMENT '付款条款, 字典: payment_terms',
    `is_borrowed`             TINYINT(1)     NOT NULL DEFAULT 0 COMMENT '是否借货, 0-否, 1-是',
    `is_relabel`              TINYINT(1)     NOT NULL DEFAULT 0 COMMENT '是否换标, 0-否, 1-是',
    `estimated_delivery_time` DATETIME                DEFAULT NULL COMMENT '预估交货时间',
    `estimated_arrival_time`  DATETIME                DEFAULT NULL COMMENT '预估到达时间',
    `actual_delivery_time`    DATETIME                DEFAULT NULL COMMENT '实际交货时间',
    `qty`                     INT            NOT NULL DEFAULT 0 COMMENT '总数量',
    `box_qty`                 DECIMAL(12, 4) NOT NULL DEFAULT 0.00 COMMENT '总箱数',
    `gross_weight`            DECIMAL(12, 4) NOT NULL DEFAULT 0.000 COMMENT '总毛重',
    `net_weight`              DECIMAL(12, 4) NOT NULL DEFAULT 0.000 COMMENT '总净重',
    `volume`                  DECIMAL(12, 4) NOT NULL DEFAULT 0.000 COMMENT '总体积',
    `bill_of_lading_type`     CHAR(64)                DEFAULT NULL COMMENT '提单类型, 仅B2B客户有,OMS/PMS, 字典: bill_of_lading_type, 无要求, 正本, 电放, 海运',
    `invoice_no`              VARCHAR(64)             DEFAULT NULL COMMENT '发票号',
    `is_allowed_shipping`     TINYINT(1)     NOT NULL DEFAULT 1 COMMENT '允许发货, 0-不允许, 1-允许',
    `closed_time`             DATETIME                DEFAULT NULL COMMENT '关闭时间',
    `sales_staff_id`          INT                     DEFAULT NULL COMMENT '运营人员ID',
    `planer_staff_id`         INT                     DEFAULT NULL COMMENT '计划人员ID',
    `shipping_staff_id`       INT                     DEFAULT NULL COMMENT '船务人员ID',
    `is_deleted`              INT            NOT NULL DEFAULT 0 COMMENT '是否删除, 0-未删除, 1-删除',
    `create_by`               INT            NOT NULL COMMENT '创建人ID',
    `create_time`             DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`               INT            NOT NULL COMMENT '更新人ID',
    `update_time`             DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_order_no` (`order_no`),
    KEY `idx_ref_no` (`ref_no`),
    KEY `idx_status` (`status`),
    KEY `idx_type` (`type`),
    KEY `idx_biz_type` (`biz_type`),
    KEY `idx_sales_channel_id` (`sales_channel_id`),
    KEY `idx_store_id` (`store_id`),
    KEY `idx_owner_id` (`owner_id`),
    KEY `idx_dest_warehouse_id` (`dest_warehouse_id`),
    KEY `idx_dest_country_code` (`dest_country_code`),
    KEY `idx_crated_time` (`create_time`),
    KEY `idx_update_time` (`update_time`)
) COMMENT ='调拨单';

-- 调拨单商品表
CREATE TABLE IF NOT EXISTS `tms_transfer_order_item`
(
    `id`                        INT         NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `ts_id`                     INT         NOT NULL COMMENT '调拨单ID',
    `ts_no`                     VARCHAR(32) NOT NULL COMMENT '调拨单号',
    `psku`                      VARCHAR(64) NOT NULL COMMENT 'PSKU',
    `fnsku`                     VARCHAR(64)          DEFAULT NULL COMMENT 'FNSKU',
    `sales_channel_id`          INT         NOT NULL COMMENT '销售渠道ID',
    `store_id`                  INT         NOT NULL COMMENT '店铺ID',
    `owner_id`                  INT         NOT NULL COMMENT '货主ID',
    `outbound_no`               VARCHAR(64)          DEFAULT NULL COMMENT '出库单号',
    `qty`                       INT         NOT NULL DEFAULT 0 COMMENT '发货数量',
    `relabel_finish_time`       DATETIME             DEFAULT NULL COMMENT '换标完成时间',
    `sale_amount`               DECIMAL(12, 4)       DEFAULT NULL COMMENT '销售金额',
    `carton_label_file_id`      VARCHAR(64)          DEFAULT NULL COMMENT '箱唛文件ID',
    `asn_label_file_id`         VARCHAR(64)          DEFAULT NULL COMMENT 'ASN标签文件ID',
    `borrowed_psku`             VARCHAR(64)          DEFAULT NULL COMMENT '出借方PSKU',
    `borrowed_fnsku`            VARCHAR(64)          DEFAULT NULL COMMENT '出借方FNSKU',
    `new_product_label_file_id` VARCHAR(64)          DEFAULT NULL COMMENT '新产品标签文件ID',
    `borrowed_store_id`         INT                  DEFAULT NULL COMMENT '出借方店铺ID',
    `borrowed_owner_id`         INT                  DEFAULT NULL COMMENT '出借方货主ID',
    `received_qty`              INT         NOT NULL DEFAULT 0 COMMENT '签收数量',
    `receipt_discrepancy`       INT         NOT NULL DEFAULT 0 COMMENT '签收差异',
    `putaway_qty`               INT         NOT NULL DEFAULT 0 COMMENT '上架数量',
    `putaway_discrepancy`       INT         NOT NULL DEFAULT 0 COMMENT '上架差异',
    `dimension_unit`            CHAR(32)             DEFAULT NULL COMMENT '尺寸单位',
    `box_length`                DECIMAL(12, 4)       DEFAULT NULL COMMENT '外箱尺寸-长',
    `box_width`                 DECIMAL(12, 4)       DEFAULT NULL COMMENT '外箱尺寸-宽',
    `box_height`                DECIMAL(12, 4)       DEFAULT NULL COMMENT '外箱尺寸-高',
    `weight_unit`               CHAR(32)             DEFAULT NULL COMMENT '重量单位',
    `weight`                    DECIMAL(12, 4)       DEFAULT NULL COMMENT '单品净重',
    `gross_weight`              DECIMAL(12, 4)       DEFAULT NULL COMMENT '单品毛重',
    `box_gross_weight`          DECIMAL(12, 4)       DEFAULT NULL COMMENT '整箱毛重',
    `quantity_per_box`          INT                  DEFAULT NULL COMMENT '装箱数量',
    `is_deleted`                INT         NOT NULL DEFAULT 0 COMMENT '是否删除, 0-未删除, 1-删除',
    `create_by`                 INT         NOT NULL COMMENT '创建人ID',
    `create_time`               DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`                 INT         NOT NULL COMMENT '更新人ID',
    `update_time`               DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_ts_id` (`ts_id`),
    KEY `idx_ts_no` (`ts_no`),
    KEY `idx_psku` (`psku`),
    KEY `idx_fnsku` (`fnsku`),
    KEY `idx_sales_channel_id` (`sales_channel_id`),
    KEY `idx_store_id` (`store_id`),
    KEY `idx_owner_id` (`owner_id`),
    KEY `idx_outbound_no` (`outbound_no`),
    KEY `idx_borrowed_psku` (`borrowed_psku`),
    KEY `idx_borrowed_store_id` (`borrowed_store_id`),
    KEY `idx_borrowed_owner_id` (`borrowed_owner_id`)
) COMMENT ='调拨单商品表';

-- MySQL DDL for TMS (Transportation Management System) Database
-- Generated from Mermaid ER Diagram

-- Table: tms_transfer_order (调拨单)
CREATE TABLE IF NOT EXISTS tms_transfer_order
(
    id                      INT            NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    ts_no                   VARCHAR(32)    NOT NULL COMMENT '调拨单单号',
    order_no                VARCHAR(32) COMMENT 'OMS的单号',
    ref_no                  VARCHAR(32) COMMENT 'OMS的订单参考号',
    status                  INT            NOT NULL COMMENT '调拨单状态, 字典: transfer_order_status',
    type                    CHAR(64)       NOT NULL COMMENT '调拨单类型, 字典: transfer_order_type',
    biz_type                CHAR(64)       NOT NULL COMMENT '业务类型, 字典: transfer_order_biz_type',
    data_source             CHAR(64)       NOT NULL COMMENT '数据来源, 判断是TMS创建还是上游传入',
    sales_channel_id        INT            NOT NULL COMMENT '销售渠道ID',
    store_id                INT            NOT NULL COMMENT '店铺ID',
    owner_id                INT            NOT NULL COMMENT '货主ID',
    shipping_warehouse      VARCHAR(100)   NOT NULL COMMENT '发货仓',
    dest_country_code       VARCHAR(100)   NOT NULL COMMENT '目的国/地区',
    dest_warehouse_id       INT            NOT NULL COMMENT '目的仓库ID',
    dest_warehouse_code     VARCHAR(64)    NOT NULL COMMENT '目的仓Code',
    dest_address            TEXT           NOT NULL COMMENT '目的地',
    trade_terms             CHAR(64)       NOT NULL COMMENT '贸易条款, 字典: trade_terms',
    payment_terms           CHAR(64)       NOT NULL COMMENT '付款条款, 字典: payment_terms',
    is_borrowed             INT            NOT NULL COMMENT '是否借货, 0-否, 1-是',
    is_relabel              INT            NOT NULL COMMENT '是否换标, 0-否, 1-是',
    estimated_delivery_time DATETIME COMMENT '预估交货时间',
    estimated_arrival_time  DATETIME COMMENT '预估到达时间',
    actual_delivery_time    DATETIME COMMENT '实际交货时间',
    qty                     INT            NOT NULL COMMENT '总数量',
    box_qty                 DECIMAL(10, 2) NOT NULL COMMENT '总箱数',
    gross_weight            DECIMAL(10, 2) NOT NULL COMMENT '总毛重',
    net_weight              DECIMAL(10, 2) NOT NULL COMMENT '总净重',
    volume                  DECIMAL(10, 2) NOT NULL COMMENT '总体积',
    bill_of_lading_type     CHAR(64) COMMENT '提单类型, 字典: bill_of_lading_type',
    invoice_no              VARCHAR(64) COMMENT '发票号',
    is_allowed_shipping     INT            NOT NULL DEFAULT 0 COMMENT '允许发货, 0-不允许, 1-允许',
    closed_time             DATETIME COMMENT '关闭时间',
    sales_staff_id          INT            NOT NULL COMMENT '运营人员ID',
    planer_staff_id         INT            NOT NULL COMMENT '计划人员ID',
    shipping_staff_id       INT            NOT NULL COMMENT '船务人员ID',
    is_deleted              TINYINT        NOT NULL DEFAULT 0 COMMENT '是否删除, 0-未删除, 1-删除',
    create_by               INT            NOT NULL COMMENT '创建人ID',
    create_time             DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by               INT            NOT NULL COMMENT '更新人ID',
    update_time             DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_ts_no (ts_no),
    INDEX idx_order_no (order_no),
    INDEX idx_ref_no (ref_no),
    INDEX idx_status (status),
    INDEX idx_dest_warehouse_id (dest_warehouse_id),
    INDEX idx_crated_time (create_time)
) COMMENT ='调拨单主表';

-- Table: tms_transfer_order_item (调拨单商品)
CREATE TABLE IF NOT EXISTS tms_transfer_order_item
(
    id                        INT            NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    ts_id                     INT            NOT NULL COMMENT '调拨单ID',
    ts_no                     VARCHAR(32)    NOT NULL COMMENT '调拨单号',
    psku                      VARCHAR(64)    NOT NULL COMMENT 'PSKU',
    fnsku                     VARCHAR(64)    NOT NULL COMMENT 'FNSKU',
    sales_channel_id          INT            NOT NULL COMMENT '销售渠道ID',
    store_id                  INT            NOT NULL COMMENT '店铺ID',
    owner_id                  INT            NOT NULL COMMENT '货主ID',
    outbound_no               VARCHAR(64) COMMENT '出库单号',
    qty                       INT            NOT NULL COMMENT '发货数量',
    relabel_finish_time       DATETIME COMMENT '换标完成时间',
    sale_amount               DECIMAL(10, 2) NOT NULL COMMENT '销售金额',
    carton_label_file_id      VARCHAR(64) COMMENT '箱唛文件ID',
    asn_label_file_id         VARCHAR(64) COMMENT 'ASN标签文件ID',
    borrowed_psku             VARCHAR(64) COMMENT '出借方PSKU',
    borrowed_fnsku            VARCHAR(64) COMMENT '出借方FNSKU',
    new_product_label_file_id VARCHAR(64) COMMENT '新产品标签文件ID',
    borrowed_store_id         INT COMMENT '出借方店铺ID',
    borrowed_owner_id         INT COMMENT '出借方货主ID',
    received_qty              INT            NOT NULL DEFAULT 0 COMMENT '签收数量',
    receipt_discrepancy       INT            NOT NULL DEFAULT 0 COMMENT '签收差异',
    putaway_qty               INT            NOT NULL DEFAULT 0 COMMENT '上架数量',
    putaway_discrepancy       INT            NOT NULL DEFAULT 0 COMMENT '上架差异',
    dimension_unit            CHAR(32)       NOT NULL COMMENT '尺寸单位',
    box_length                DECIMAL(10, 2) NOT NULL COMMENT '外箱尺寸-长',
    box_width                 DECIMAL(10, 2) NOT NULL COMMENT '外箱尺寸-宽',
    box_height                DECIMAL(10, 2) NOT NULL COMMENT '外箱尺寸-高',
    weight_unit               CHAR(32)       NOT NULL COMMENT '重量单位',
    weight                    DECIMAL(10, 2) NOT NULL COMMENT '单品净重',
    gross_weight              DECIMAL(10, 2) NOT NULL COMMENT '单品毛重',
    box_gross_weight          DECIMAL(10, 2) NOT NULL COMMENT '整箱毛重',
    quantity_per_box          INT            NOT NULL COMMENT '装箱数量',
    is_deleted                TINYINT        NOT NULL DEFAULT 0 COMMENT '是否删除, 0-未删除, 1-删除',
    create_by                 INT            NOT NULL COMMENT '创建人ID',
    create_time               DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by                 INT            NOT NULL COMMENT '更新人ID',
    update_time               DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_ts_id (ts_id),
    INDEX idx_ts_no (ts_no),
    INDEX idx_psku (psku),
    INDEX idx_fnsku (fnsku),
    INDEX idx_outbound_no (outbound_no)
) COMMENT ='调拨单商品明细表';

-- Table: tms_transfer_order_inbound (调拨单入库)
CREATE TABLE IF NOT EXISTS tms_transfer_order_inbound
(
    id                    INT         NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    ts_id                 INT         NOT NULL COMMENT '调拨单ID',
    ts_no                 VARCHAR(32) NOT NULL COMMENT '调拨单号',
    psku                  VARCHAR(64) NOT NULL COMMENT 'PSKU',
    fnsku                 VARCHAR(64) NOT NULL COMMENT 'FNSKU',
    inbound_no            VARCHAR(64) NOT NULL COMMENT '入库单号',
    qty                   INT         NOT NULL COMMENT '数量',
    expected_putaway_time DATETIME COMMENT '期望上架时间',
    received_start_time   DATETIME COMMENT '签收开始时间',
    received_end_time     DATETIME COMMENT '签收结束时间',
    putaway_start_time    DATETIME COMMENT '上架开始时间',
    putaway_end_time      DATETIME COMMENT '上架结束时间',
    is_deleted            TINYINT     NOT NULL DEFAULT 0 COMMENT '是否删除, 0-未删除, 1-删除',
    create_by             INT         NOT NULL COMMENT '创建人ID',
    create_time           DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by             INT         NOT NULL COMMENT '更新人ID',
    update_time           DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_ts_id (ts_id),
    INDEX idx_ts_no (ts_no),
    INDEX idx_inbound_no (inbound_no),
    INDEX idx_psku (psku),
    INDEX idx_fnsku (fnsku)
) COMMENT ='调拨单入库信息表';

-- Table: tms_transfer_order_outbound (调拨单出库)
CREATE TABLE IF NOT EXISTS tms_transfer_order_outbound
(
    id                   INT         NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    ts_id                INT         NOT NULL COMMENT '调拨单ID',
    ts_no                VARCHAR(32) NOT NULL COMMENT '调拨单号',
    psku                 VARCHAR(64) NOT NULL COMMENT 'PSKU',
    fnsku                VARCHAR(64) NOT NULL COMMENT 'FNSKU',
    outbound_no          VARCHAR(64) NOT NULL COMMENT '出库单号',
    qty                  INT         NOT NULL COMMENT '数量',
    departure_time       DATETIME COMMENT '发货时间',
    logistics_mode_id    INT         NOT NULL COMMENT '物流方式ID',
    is_palletized        INT         NOT NULL DEFAULT 0 COMMENT '是否打托, 0-否, 1-是',
    delivery_requirement TEXT COMMENT '发货要求',
    is_borrowed          INT         NOT NULL DEFAULT 0 COMMENT '是否借货, 0-否, 1-是',
    is_relabel           INT         NOT NULL DEFAULT 0 COMMENT '是否换标, 0-否, 1-是',
    is_deleted           TINYINT     NOT NULL DEFAULT 0 COMMENT '是否删除, 0-未删除, 1-删除',
    create_by            INT         NOT NULL COMMENT '创建人ID',
    create_time          DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by            INT         NOT NULL COMMENT '更新人ID',
    update_time          DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_ts_id (ts_id),
    INDEX idx_ts_no (ts_no),
    INDEX idx_outbound_no (outbound_no),
    INDEX idx_logistics_mode_id (logistics_mode_id),
    INDEX idx_departure_time (departure_time)
) COMMENT ='调拨单出库信息表';

-- Table: tms_transfer_order_customer (调拨单客户信息)
CREATE TABLE IF NOT EXISTS tms_transfer_order_customer
(
    id                    INT         NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    ts_id                 INT         NOT NULL COMMENT '调拨单ID',
    ts_no                 VARCHAR(32) NOT NULL COMMENT '调拨单号',
    customer_company_name VARCHAR(64) NOT NULL COMMENT '客户公司名',
    customer_country_code VARCHAR(64) NOT NULL COMMENT '客户国家/地区',
    customer_contact      VARCHAR(64) NOT NULL COMMENT '客户联系人',
    customer_phone        VARCHAR(64) NOT NULL COMMENT '客户电话',
    customer_email        VARCHAR(64) NOT NULL COMMENT '客户邮箱',
    is_deleted            TINYINT     NOT NULL DEFAULT 0 COMMENT '是否删除, 0-未删除, 1-删除',
    create_by             INT         NOT NULL COMMENT '创建人ID',
    create_time           DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by             INT         NOT NULL COMMENT '更新人ID',
    update_time           DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_ts_id (ts_id),
    INDEX idx_ts_no (ts_no)
) COMMENT ='调拨单客户信息表';

-- Table: tms_logistics_mode (物流方式表)
CREATE TABLE IF NOT EXISTS tms_logistics_mode
(
    id          INT         NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    code        VARCHAR(64) NOT NULL COMMENT '物流方式编码',
    name        VARCHAR(64) NOT NULL COMMENT '物流方式名称',
    is_deleted  TINYINT     NOT NULL DEFAULT 0 COMMENT '是否删除, 0-未删除, 1-删除',
    create_by   INT         NOT NULL COMMENT '创建人ID',
    create_time DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by   INT         NOT NULL COMMENT '更新人ID',
    update_time DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_code (code),
    INDEX idx_name (name)
) COMMENT ='物流方式字典表';

-- Table: tms_transport_order_comment (调拨单批注表)
CREATE TABLE IF NOT EXISTS tms_transport_order_comment
(
    id          INT         NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '批注ID',
    ts_id       INT         NOT NULL COMMENT 'TS单ID',
    ts_no       VARCHAR(32) NOT NULL COMMENT 'TS单号',
    content     TEXT        NOT NULL COMMENT '批注内容',
    is_deleted  TINYINT     NOT NULL DEFAULT 0 COMMENT '是否删除,0=未删除,1=删除',
    create_by   INT         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建的用户',
    create_time DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by   INT         NOT NULL COMMENT '更新的用户',
    update_time DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_ts_id (ts_id),
    INDEX idx_ts_no (ts_no),
    INDEX idx_create_time (create_time)
) COMMENT ='调拨单批注表';

-- Table: tms_inventory_transfer (库存转让单)
CREATE TABLE IF NOT EXISTS tms_inventory_transfer
(
    id                  INT         NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    biz_id              INT         NOT NULL COMMENT '业务单ID',
    biz_no              VARCHAR(32) NOT NULL COMMENT '业务单号',
    biz_type            CHAR(64)    NOT NULL COMMENT '业务单类型, 字典: inventory_transfer_biz_type',
    transfer_no         VARCHAR(32) NOT NULL COMMENT '库存转让单号',
    is_relabel          INT         NOT NULL COMMENT '是否换标, 0-否, 1-是',
    remark              TEXT COMMENT '备注',
    from_psku           VARCHAR(64) NOT NULL COMMENT '转出PSKU',
    from_fnsku          VARCHAR(64) NOT NULL COMMENT '转出FNSKU',
    from_store_id       INT         NOT NULL COMMENT '转出店铺ID',
    from_owner_id       INT         NOT NULL COMMENT '转出货主ID',
    from_warehouse_id   INT         NOT NULL COMMENT '转出仓库ID',
    transfer_quantity   INT         NOT NULL COMMENT '转让数量',
    to_psku             VARCHAR(64) NOT NULL COMMENT '转入PSKU',
    to_fnsku            VARCHAR(64) NOT NULL COMMENT '转入FNSKU',
    to_store_id         INT         NOT NULL COMMENT '转入店铺ID',
    to_owner_id         INT         NOT NULL COMMENT '转入货主ID',
    to_warehouse_id     INT         NOT NULL COMMENT '转入仓库ID',
    relabel_finish_time DATETIME COMMENT '换标完成时间',
    transfer_result     INT         NOT NULL COMMENT '转让结果, 字典: inventory_transfer_result',
    fail_reason         TEXT COMMENT '失败原因',
    is_deleted          TINYINT     NOT NULL DEFAULT 0 COMMENT '是否删除, 0-未删除, 1-删除',
    create_by           INT         NOT NULL COMMENT '创建人ID',
    create_time         DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by           INT         NOT NULL COMMENT '更新人ID',
    update_time         DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_biz (biz_no, biz_id),
    INDEX idx_transfer_no (transfer_no),
    INDEX idx_from_psku (from_psku),
    INDEX idx_to_psku (to_psku),
    INDEX idx_transfer_result (transfer_result)
) COMMENT ='库存转让单表';
