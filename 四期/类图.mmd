classDiagram
    class TransferOrderStatus
    <<enumeration>> TransferOrderStatus
    TransferOrderStatus: PENDING_CONFIRMATION "待确认"
TransferOrderStatus: CONFIRMED "已确认"
TransferOrderStatus: SHIPPED "已发货"
TransferOrderStatus: COMPLETED "已完成"
TransferOrderStatus: CLOSED "已关闭"

class TransferOrderType
<<enumeration>> TransferOrderType
TransferOrderType: THIRD_PARTY_TO_PLATFORM "三方仓调拨至平台仓"
TransferOrderType: SELF_BUILT_TO_PLATFORM "自建仓调拨至平台仓"
TransferOrderType: THIRD_PARTY_TO_THIRD_PARTY "三方仓调拨至三方仓"
TransferOrderType: THIRD_PARTY_TO_SELF_BUILT "三方仓调拨至自建仓"
TransferOrderType: SELF_BUILT_TO_THIRD_PARTY "自建仓调拨至三方仓"
TransferOrderType: THIRD_PARTY_TO_CUSTOMER "三方仓发至客户"
TransferOrderType: SELF_BUILT_TO_CUSTOMER "自建仓发至客户"

class TransferOrderBizType
<<enumeration>> TransferOrderBizType
TransferOrderBizType: B2C_REPLENISHMENT "B2C补货"
TransferOrderBizType: VC_DO "VC(DO)"
TransferOrderBizType: B2B_CUSTOMER "B2B客户"
TransferOrderBizType : WAREHOUSE_CHANGE "换仓"

class TradeTerms
<<enumeration>> TradeTerms
TradeTerms: FOB "FOB"
TradeTerms: CIF "CIF"
TradeTerms : DDP "DDP"
TradeTerms: OTHER "其他"

class PaymentTerms
<<enumeration>> PaymentTerms
PaymentTerms: PREPAID "预付"
PaymentTerms : COLLECT "到付"
PaymentTerms: MONTHLY "月结"

class BillOfLadingType
<<enumeration>> BillOfLadingType
BillOfLadingType: NO_REQUIREMENT "无要求"
BillOfLadingType : ORIGINAL "正本"
BillOfLadingType: SURRENDER "电放"
BillOfLadingType: SEA_FREIGHT "海运"

class TransferOrder {
+int id "主键"
+String stNo "物流计划单号"
+String orderNo "订单号: B2C/换仓-无此单号, B2B客户-BO单号, OMS的单号, VC-DO-VC单号"
+String refNo "参考号: B2C/换仓-PMS/OMS的, B2B客户-客户的PO, VC-DO-VCPO"
+TransferOrderStatus status "调拨单状态"
+TransferOrderType type "调拨单类型, 根据发货仓和目的仓判断类型"
+TransferOrderBizType bizType "业务类型"
+int salesChannelId "销售渠道ID"
+int storeId "店铺ID"
+int ownerId "货主ID"
+String shippingWarehouse "发货仓"
+String destCountryCode "目的国/地区代码"
+int destWarehouseId "目的仓库ID"
+String destWarehouseCode "目的仓代码"
+String destAddress "目的地地址"
+TradeTerms tradeTerms "贸易条款"
+PaymentTerms paymentTerms "付款条款"
+boolean isBorrowed "是否借货: 0-否, 1-是"
+boolean isRelabel "是否换标: 0-否, 1-是"
+Date estimatedDeliveryTime "预估交货时间"
+Date estimatedArrivalTime "预估到达时间"
+Date actualDeliveryTime "实际交货时间"
+int qty "总数量"
+BigDecimal boxQty "总箱数"
+BigDecimal grossWeight "总毛重"
+BigDecimal netWeight "总净重"
+BigDecimal volume "总体积"
+BillOfLadingType billOfLadingType "提单类型, 仅B2B客户有"
+String invoiceNo "发票号"
+boolean isAllowedShipping "允许发货: 0-不允许, 1-允许"
+Date closedTime "关闭时间"
+int salesStaffId "运营人员ID"
+int planerStaffId "计划人员ID"
+int shippingStaffId "船务人员ID"
+boolean isDeleted "是否删除: 0-未删除, 1-删除"
+int createdBy "创建人ID"
+Date createdTime "创建时间"
+int updatedBy "更新人ID"
+Date updatedTime "更新时间"
}

class TransferOrderItem {
+int id "主键"
+int tsId "调拨单ID (外键)"
+String tsNo "调拨单号 (外键)"
+String psku "PSKU"
+String fnsku "FNSKU"
+int salesChannelId "销售渠道ID"
+int storeId "店铺ID"
+int ownerId "货主ID"
+String outboundNo "出库单号"
+int qty "发货数量"
+Date relabelFinishTime "换标完成时间"
+BigDecimal saleAmount "销售金额"
+String cartonLabelFileId "箱唛文件ID"
+String asnLabelFileId "ASN标签文件ID"
+String borrowedPsku "出借方PSKU"
+String borrowedFnsku "出借方FNSKU"
+String newProductLabelFileId "新产品标签文件ID"
+int borrowedStoreId "出借方店铺ID"
+int borrowedOwnerId "出借方货主ID"
+int receivedQty "签收数量"
+int receiptDiscrepancy "签收差异"
+int putawayQty "上架数量"
+int putawayDiscrepancy "上架差异"
+String dimensionUnit "尺寸单位"
+BigDecimal boxLength "外箱尺寸-长"
+BigDecimal boxWidth "外箱尺寸-宽"
+BigDecimal boxHeight "外箱尺寸-高"
+String weightUnit "重量单位"
+BigDecimal weight "单品净重"
+BigDecimal grossWeight "单品毛重"
+BigDecimal boxGrossWeight "整箱毛重"
+int quantityPerBox "装箱数量"
+boolean isDeleted "是否删除: 0-未删除, 1-删除"
+int createdBy "创建人ID"
+Date createdTime "创建时间"
+int updatedBy "更新人ID"
+Date updatedTime "更新时间"
}

class TransferOrderInbound {
+int id "主键"
+int tsId "调拨单ID (外键)"
+String tsNo "调拨单号 (外键)"
+String psku "PSKU"
+String fnsku "FNSKU"
+String inboundNo "入库单号"
+int qty "数量"
+Date expectedPutawayTime "期望上架时间"
+Date receivedStartTime "签收开始时间"
+Date receivedEndTime "签收结束时间"
+Date putawayStartTime "上架开始时间"
+Date putawayEndTime "上架结束时间"
+boolean isDeleted "是否删除: 0-未删除, 1-删除"
+int createdBy "创建人ID"
+Date createdTime "创建时间"
+int updatedBy "更新人ID"
+Date updatedTime "更新时间"
}

class TransferOrderOutbound {
+int id "主键"
+int tsId "调拨单ID (外键)"
+String tsNo "调拨单号 (外键)"
+String psku "PSKU"
+String fnsku "FNSKU"
+String outboundNo "出库单号"
+int qty "数量"
+Date departureTime "发货时间"
+int logisticsModeId "物流方式ID"
+boolean isPalletized "是否打托: 0-否, 1-是"
+String deliveryRequirement "发货要求"
+boolean isBorrowed "是否借货: 0-否, 1-是"
+boolean isRelabel "是否换标: 0-否, 1-是"
+boolean isDeleted "是否删除: 0-未删除, 1-删除"
+int createdBy "创建人ID"
+Date createdTime "创建时间"
+int updatedBy "更新人ID"
+Date updatedTime "更新时间"
}

class TransferOrderCustomer {
+int id "主键"
+int tsId "调拨单ID (外键)"
+String tsNo "调拨单号 (外键)"
+String customerCompanyName "客户公司名"
+String customerCountryCode "客户国家/地区代码"
+String customerContact "客户联系人"
+String customerPhone "客户电话"
+String customerEmail "客户邮箱"
+boolean isDeleted "是否删除: 0-未删除, 1-删除"
+int createdBy "创建人ID"
+Date createdTime "创建时间"
+int updatedBy "更新人ID"
+Date updatedTime "更新时间"
}

class LogisticsMode {
+int id "主键"
+String code "物流方式编码"
+String name "物流方式名称"
+boolean isDeleted "是否删除: 0-未删除, 1-删除"
+int createdBy "创建人ID"
+Date createdTime "创建时间"
+int updatedBy "更新人ID"
+Date updatedTime "更新时间"
}

class TransferOrderComment {
+int id "主键, 批注ID"
+int tsId "TS单ID"
+String tsNo "TS单号"
+String comment "批注内容"
+boolean isDeleted "是否删除: 0-未删除, 1-删除"
+int createBy "创建的用户"
+Date createTime "创建时间"
+int updateBy "更新的用户"
+Date updateTime "更新时间"
}

TransferOrder "1" -- "*" TransferOrderItem: contains
TransferOrder "1" -- "1" TransferOrderCustomer: has
TransferOrder "1" -- "*" TransferOrderComment: has
TransferOrder "1" -- "*" TransferOrderInbound: has
TransferOrder "1" -- "*" TransferOrderOutbound: has
TransferOrderItem "1" -- "*" TransferOrderInbound: related
TransferOrderItem "1" -- "*" TransferOrderOutbound: related
TransferOrderOutbound "1" -- "1" LogisticsMode: uses