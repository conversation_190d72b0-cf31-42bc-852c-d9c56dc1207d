package com.renpho.erp.tms.adapter.web.controller.transferorder.cmd;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.renpho.erp.tms.adapter.web.controller.command.container.CountryCodeContainer;
import com.renpho.erp.tms.adapter.web.controller.command.container.ItemsContainer;
import com.renpho.erp.tms.adapter.web.controller.command.container.StoreIdContainer;
import com.renpho.erp.tms.domain.regiontimezone.CountryTimeZone;
import com.renpho.erp.tms.domain.site.CountryRegion;
import com.renpho.erp.tms.domain.store.Store;
import com.renpho.erp.tms.domain.transferorder.TransferOrderBizType;
import com.renpho.erp.tms.domain.transferorder.TransferOrderDataSource;
import com.renpho.erp.tms.domain.transferorder.TransferReason;
import com.renpho.karma.dto.Command;
import groovy.lang.Tuple;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 创建调拨单入参
 *
 * <AUTHOR>
 * @since 2025/8/23
 */
@Getter
@Setter
public class AddCmd extends Command implements Serializable, StoreIdContainer, CountryCodeContainer, ItemsContainer<List<AddItemCmd>, AddItemCmd> {

    @Serial
    private static final long serialVersionUID = -2900403144528539L;

    @NotNull
    private TransferOrderDataSource dataSource;

    /**
     * OMS的单号
     */
    private String orderNo;

    /**
     * OMS的订单参考号
     */
    private String refNo;

    /**
     * 业务类型, 字典: transfer_order_biz_type
     */
    @NotNull
    private TransferOrderBizType businessType;

    /**
     * 贸易条款, 字典: trade_terms
     */
    private String tradeTerms;

    /**
     * 付款条款, 字典: payment_terms
     */
    private String paymentTerms;

    /**
     * 店铺ID, 通过店铺ID查询店铺所属渠道以及货主
     */
    @NotNull
    private Integer storeId;

    @JsonIgnore
    private Store store;

    /**
     * 发货仓ID
     */
    @NotNull
    private Integer shippingWarehouseId;

    /**
     * 目的国/地区
     */
    @NotBlank
    private String destCountryCode;

    @JsonIgnore
    private CountryRegion destCountryRegion;

    /**
     * 目的仓库ID
     */
    @NotNull
    private Integer destWarehouseId;

    /**
     * 目的仓Code
     */
    @NotBlank
    private String destWarehouseCode;

    /**
     * 目的地
     */
    @NotBlank
    private String destAddress;

    /**
     * 调拨原因
     */
    @NotNull
    private TransferReason transferReason;

    /**
     * 调拨原因描述
     */
    private String transferReasonDesc;

    /**
     * 是否打托
     */
    @NotNull
    private Boolean isPalletized;

    /**
     * 预估发货时间
     */
    @NotNull
    private LocalDateTime estimatedDepartureTime;

    /**
     * 预估交货时间
     */
    @NotNull
    private LocalDateTime estimatedDeliveryTime;

    /**
     * 期望上架时间
     */
    @NotNull
    private LocalDateTime expectedPutawayTime;

    /**
     * 提单类型, 字典: bill_of_lading_type
     */
    private String billOfLadingType;

    /**
     * 发票号
     */
    private String invoiceNo;

    /**
     * 允许发货, 0-不允许, 1-允许
     */
    private Boolean isAllowedShipping;

    /**
     * 批注
     */
    private String comment;

    /**
     * 调拨单明细
     */
    @NotEmpty
    private List<@NotNull AddItemCmd> items;

    /**
     * 运营
     */
    private Integer salesStaffId;

    /**
     * 创建人, 同时赋值到创建人和计划人员
     */
    private Integer createdBy;

    /**
     * 客户信息
     */
    private AddCustomerCmd customer;

    @Override
    public String getCountryCode() {
        return destCountryCode;
    }

    @Override
    public void setCountryRegion(CountryRegion countryRegion) {
        setDestCountryRegion(countryRegion);
    }

    @Override
    public void setCountryTimeZone(CountryTimeZone countryTimeZone) {
        Optional.ofNullable(this.destCountryRegion).ifPresent(c -> c.setTimezone(countryTimeZone));
    }

    @AssertTrue
    @SuppressWarnings("unused")
    public boolean isTransferReasonIsOtherAndTransferReasonDescIsNotBlank() {
        return !TransferReason.OTHER.equals(this.transferReason) || StringUtils.isNotBlank(transferReasonDesc);
    }

    @AssertTrue
    @SuppressWarnings("unused")
    public boolean isItemsNotDuplicated() {
        return CollectionUtils.emptyIfNull(items)
                .stream()
                .filter(Objects::nonNull)
                .map(i -> Tuple.tuple(i.getPsku(), i.getFnsku()))
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
                .entrySet()
                .stream()
                .noneMatch(c -> c.getValue() > 1);
    }

    @AssertTrue
    @SuppressWarnings("unused")
    public boolean isBorrowedAndBorrowedPskuNotBlank() {
        return CollectionUtils.emptyIfNull(items)
                .stream()
                .filter(Objects::nonNull)
                .allMatch(i -> !i.isBorrowed() || StringUtils.isNotBlank(i.getBorrowedPsku()));
    }

    @AssertTrue
    @SuppressWarnings("unused")
    public boolean isDataSourceNotDefaultAndCreateByNotNull() {
        return TransferOrderDataSource.DEFAULT.equals(this.dataSource) || Objects.nonNull(this.createdBy);
    }

    @AssertTrue
    @SuppressWarnings("unused")
    public boolean isDataSourceNotDefaultAndTradeTermsNotBlank() {
        return TransferOrderDataSource.DEFAULT.equals(this.dataSource) || StringUtils.isNotBlank(this.tradeTerms);
    }

    @AssertTrue
    @SuppressWarnings("unused")
    public boolean isDataSourceNotDefaultAndPaymentTermsNotBlank() {
        return TransferOrderDataSource.DEFAULT.equals(this.dataSource) || StringUtils.isNotBlank(this.paymentTerms);
    }

    @AssertTrue
    @SuppressWarnings("unused")
    public boolean isDataSourceNotDefaultAndCustomerNotNull() {
        if (TransferOrderDataSource.DEFAULT.equals(this.dataSource)) {
            return true;
        }
        return Optional.ofNullable(this.customer).filter(AddCustomerCmd::isValid).isPresent();
    }

}
