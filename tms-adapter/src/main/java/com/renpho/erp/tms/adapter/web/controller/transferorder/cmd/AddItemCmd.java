package com.renpho.erp.tms.adapter.web.controller.transferorder.cmd;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.renpho.erp.tms.adapter.web.controller.command.container.PskuContainer;
import com.renpho.erp.tms.domain.product.Product;
import com.renpho.karma.dto.Command;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @since 2025/8/23
 */
@Getter
@Setter
public class AddItemCmd extends Command implements Serializable, PskuContainer {

    @Serial
    private static final long serialVersionUID = -3697961580505787480L;

    @NotBlank
    private String psku;

    @NotBlank
    private String fnsku;

    @JsonIgnore
    private Product product;

    /**
     * 数量
     */
    @NotNull
    private Integer qty;

    /**
     * 是否借货
     */
    private boolean isBorrowed;

    /**
     * 是否换标
     */
    private boolean isRelabel;

    /**
     * 出借方PSKU
     */
    private String borrowedPsku;

    /**
     * 出借方FNSKU
     */
    private String borrowedFnsku;

    /**
     * 出借方店铺
     */
    private String borrowedStore;

    /**
     * 出借方货主
     */
    private String borrowedOwner;

    /**
     * 产品标签
     */
    private List<String> newProductLabelFileIds;

    /**
     * 箱唛文件
     */
    private List<String> cartonLabelFileIds;

    /**
     * ASN标签文件, 仅 VC 类型有此文件, 展示时合并入箱唛文件
     */
    private List<String> asnLabelFileIds;

    /**
     * 条码文件
     */
    private List<String> barcodeFileIds;

    /**
     * 备注
     */
    private String remark;

}
