<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.renpho.erp</groupId>
        <artifactId>pds-parent</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>pds-application</artifactId>
    <name>${project.artifactId}</name>
    <description>应用层、模块</description>

    <dependencies>
        <!-- ==================================== -->
        <!-- 内部依赖 -->
        <!-- ==================================== -->
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>pds-infrastructure</artifactId>
        </dependency>

        <!-- ==================================== -->
        <!-- 店铺接口 -->
        <!-- ==================================== -->
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>mdm-client</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.renpho.erp</groupId>
            <artifactId>ftm-client</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.renpho.karma</groupId>
            <artifactId>karma-timezone</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
