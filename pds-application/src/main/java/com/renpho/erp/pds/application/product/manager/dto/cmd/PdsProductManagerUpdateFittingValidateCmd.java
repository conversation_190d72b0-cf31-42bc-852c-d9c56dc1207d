package com.renpho.erp.pds.application.product.manager.dto.cmd;

import com.renpho.erp.pds.domain.common.LanguageEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 产品管理-配件信息
 *
 * <AUTHOR>
 * @since 2024/11/04
 */
@Data
public class PdsProductManagerUpdateFittingValidateCmd {

    /**
     * 产品管理-配件信息-主键ID, 注意: 更新操作时候，如果ID为空则表示新增，如果ID不为空则表示更新
     */
    private Integer id;

    /**
     * 配件图片，最大20M，支持png、jpg、jpeg格式 ; 可以为空
     */
    private String imageId;

    /**
     * 配件描述，支持中文英文，最大1000字符 ; 非空
     */
    private String description;

    /**
     * 数量，范围1-9999 ; 非空
     */
    private Integer quantity;

    /**
     * 单位，字符限制32 ; 可以为空
     */
    private String unit;

    /**
     * 适用国家/地区 ID集合，多个地区用逗号隔开 ; 可以为空
     */
    private String applicableRegionsIds;

    /**
     * 参数校验
     *
     * @param language 语言类型
     * @param isStrict 是否启用严格模式
     */
    public void validateFields(LanguageEnum language, boolean isStrict) {
        // 校验配件描述
        validateStringField(description, 1000, language, isStrict,
                "配件描述不能为空", "Description cannot be empty",
                "配件描述不能超过1000个字符", "Description cannot exceed 1000 characters");

        // 校验数量
        validateRangeField(quantity, 1, 9999, language, isStrict,
                "数量不能为空", "Quantity cannot be null",
                "数量范围必须在1到9999之间", "Quantity must be between 1 and 9999");

        // 校验单位
        validateStringField(unit, 32, language, false,
                null, null,
                "单位不能超过32个字符", "Unit cannot exceed 32 characters");
    }

    private void validateStringField(String field, int maxLength, LanguageEnum language, boolean isStrict,
                                     String nullMessageZh, String nullMessageEn,
                                     String lengthMessageZh, String lengthMessageEn) {
        if (StringUtils.isBlank(field)) {
            if (isStrict && nullMessageZh != null && nullMessageEn != null) {
                throwValidationException(language, nullMessageZh, nullMessageEn);
            }
        } else if (field.length() > maxLength) {
            throwValidationException(language, lengthMessageZh, lengthMessageEn);
        }
    }

    private void validateRangeField(Integer field, int min, int max, LanguageEnum language, boolean isStrict,
                                    String nullMessageZh, String nullMessageEn,
                                    String rangeMessageZh, String rangeMessageEn) {
        if (field == null) {
            if (isStrict) {
                throwValidationException(language, nullMessageZh, nullMessageEn);
            }
        } else if (field < min || field > max) {
            throwValidationException(language, rangeMessageZh, rangeMessageEn);
        }
    }

    private void throwValidationException(LanguageEnum language, String chineseMessage, String englishMessage) {
        if (language == LanguageEnum.China) {
            throw new IllegalArgumentException(chineseMessage);
        } else {
            throw new IllegalArgumentException(englishMessage);
        }
    }
}