package com.renpho.erp.pds.application.product.manager.certification.dto;

import lombok.Data;

import java.util.List;

/**
 * 产品管理-认证-基础表.
 *
 * <AUTHOR>
 * @since 2024.11.18
 */
@Data
public class PdsProductManagerUpdateCertificationBasicRequest {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 关联的产品管理主键ID
     */
    private Integer productManagerId;

    /**
     * 产品UDI: 1、非必填项；2、输入仅支持数字，14位字符
     */
    private String productUdi;

    /**
     * 外箱UDI: 1、非必填项；2、输入仅支持数字，14位字符
     */
    private String cartonUdi;

    /**
     * 备注
     */
    private String remark;

    /**
     *  认证-部件表 集合
     */
    private List<PdsProductManagerUpdateCertificationComponentRequest> componentList;

    /**
     *  认证-整机表 集合
     */
    private List<PdsProductManagerUpdateCertificationMachineRequest> machineList;

}

