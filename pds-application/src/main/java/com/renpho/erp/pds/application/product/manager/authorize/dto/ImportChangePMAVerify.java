package com.renpho.erp.pds.application.product.manager.authorize.dto;

import com.renpho.erp.pds.infrastructure.persistence.po.PdsProductManagerAuthorizePo;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 产品管理-授权-导入-认证
 *
 * <AUTHOR>
 * @since 2024.10.28
 */
@Data
public class ImportChangePMAVerify {

    private List<PdsProductManagerAuthorizePo> updatePoList = new ArrayList<>();

    private List<PdsProductAuthorizeModelImportErrorExport> importErrorList = new ArrayList<>();

    private List<PdsProductAuthorizeModelImportErrorExport> importRightList = new ArrayList<>();

    public void addImportRight(PdsProductAuthorizeModelImportErrorExport importRight) {
        this.importRightList.add(importRight);
    }

    public void addPdsProductManagerAuthorizePo(PdsProductManagerAuthorizePo po) {
        this.updatePoList.add(po);
    }

    public void addPdsProductAuthorizeModelImportErrorExport(PdsProductAuthorizeModelImportErrorExport errorExport) {
        this.importErrorList.add(errorExport);
    }

    /**
     * 是否认证通过
     *
     * @return true-认证通过，false-认证不通过
     */
    public Boolean verify() {
        return importErrorList.isEmpty();
    }

    /**
     * 存在正确数据
     *
     * @return true-存在，false-不存在
     */
    public Boolean existRightDatas() {
        return (!updatePoList.isEmpty());
    }

}