package com.renpho.erp.pds.application.brand.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 品牌列表用户搜索结果.
 *
 * <AUTHOR>
 * @since 2024.10.11
 */
@Data
public class PdsBrandUsersResponse {

    /**
     * 搜索结果
     */
    private List<PdsBrandUserDetailResponse> users = new ArrayList<PdsBrandUserDetailResponse>();

    /**
     * 添加用户
     * @param user 用户信息
     */
    public void addUser(PdsBrandUserDetailResponse user) {
        users.add(user);
    }

}
