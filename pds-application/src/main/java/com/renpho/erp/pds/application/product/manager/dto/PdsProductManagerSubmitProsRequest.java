package com.renpho.erp.pds.application.product.manager.dto;

import lombok.Data;

import java.util.List;


@Data
public class PdsProductManagerSubmitProsRequest {
    /**
     * 单品尺寸-长 (cm)/公制
     */
    private Double productLengthMetric;

    /**
     * 单品尺寸-宽 (cm)/公制
     */
    private Double productWidthMetric;

    /**
     * 单品尺寸-高 (cm)/公制
     */
    private Double productHeightMetric;

    /**
     * 单品净重 (kg)/公制
     */
    private Double productWeightMetric;

    /**
     * 产品数量
     */
    private Integer productQuantity;

    /**
     * 产品备注
     */
    private String productRemarks;

    /**
     * 包装尺寸(即彩盒尺寸)-长 (cm)/公制
     */
    private Double packagingLengthMetric;

    /**
     * 包装尺寸(即彩盒尺寸)-宽 (cm)/公制
     */
    private Double packagingWidthMetric;

    /**
     * 包装尺寸(即彩盒尺寸)-高 (cm)/公制
     */
    private Double packagingHeightMetric;

    /**
     * 单品毛重 (kg)/公制
     */
    private Double grossWeightMetric;

    /**
     * 产品单位
     */
    private String productUnit;


    /** 外箱规格 */
    private List<PdsProductManagerSubmitProsBoxRequest> boxList;
}