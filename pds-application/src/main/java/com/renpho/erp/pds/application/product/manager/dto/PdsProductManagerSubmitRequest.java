package com.renpho.erp.pds.application.product.manager.dto;

import lombok.Data;

import java.util.List;

/**
 * 产品管理请求体
 *
 * <AUTHOR>
 * @since 2024.11.11
 */
@Data
public class PdsProductManagerSubmitRequest{

	/** 基本信息 */
	private PdsProductManagerSubmitBasicRequest basic;

	/** 属性信息 */
	private PdsProductManagerSubmitProsRequest pros;

	/** 物流信息 */
	private PdsProductManagerSubmitLogisticsRequest logistics;

	/** 配置信息 */
	private List<PdsProductManagerSubmitFittingRequest> fittingList;

	/**
	 * 导入的原始数据
	 */
	private PurchaseSkuImport importData;

	/**
	 * 新增类型: 0,草稿; 1,提交(默认)
	 */
	private Integer status;

}
