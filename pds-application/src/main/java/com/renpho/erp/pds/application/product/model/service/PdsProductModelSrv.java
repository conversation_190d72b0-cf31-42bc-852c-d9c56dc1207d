package com.renpho.erp.pds.application.product.model.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.renpho.erp.oplog.log.OpLog;
import com.renpho.erp.oplog.log.enums.BusinessType;
import com.renpho.erp.pds.application.category.dto.*;
import com.renpho.erp.pds.application.category.service.PdsCategorySrv;
import com.renpho.erp.pds.application.product.model.dto.PdsProductModelResponse;
import com.renpho.erp.pds.application.product.model.dto.PdsProductModelUserResponse;
import com.renpho.erp.pds.application.product.model.service.oplog.PdsProductModelChangePmSnapt;
import com.renpho.erp.pds.application.product.model.service.oplog.PdsSystemModule;
import com.renpho.erp.pds.application.product.model.service.oplog.ProductModelBusinessModules;
import com.renpho.erp.pds.domain.common.CommonConstants;
import com.renpho.erp.pds.domain.common.LanguageEnum;
import com.renpho.erp.pds.domain.product.model.PdsProductModel;
import com.renpho.erp.pds.domain.product.model.PdsProductModelRepository;
import com.renpho.erp.pds.infrastructure.exception.PdsErrorCode;
import com.renpho.erp.pds.infrastructure.feignclient.RemoteUserDetailsFeign;
import com.renpho.erp.pds.infrastructure.persistence.constant.IsDeletedEnum;
import com.renpho.erp.pds.infrastructure.persistence.dto.PdsProductModelAutoModelNoDto;
import com.renpho.erp.pds.infrastructure.persistence.dto.PdsProductModelDto;
import com.renpho.erp.pds.infrastructure.persistence.dto.PdsProductModelQueryRequest;
import com.renpho.erp.pds.infrastructure.persistence.mapper.PdsBrandMapper;
import com.renpho.erp.pds.infrastructure.persistence.mapper.PdsProductManagerBasicMapper;
import com.renpho.erp.pds.infrastructure.persistence.mapper.PdsProductModelMapper;
import com.renpho.erp.pds.infrastructure.persistence.mapper.PdsProductModelSerialMapper;
import com.renpho.erp.pds.infrastructure.persistence.po.*;
import com.renpho.erp.pds.infrastructure.utils.PagingUtils;
import com.renpho.erp.pds.infrastructure.utils.StringUtils;
import com.renpho.erp.smc.client.dto.OumUserInfoRes;
import com.renpho.erp.smc.client.dto.QueryUserListReq;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import com.renpho.karma.exception.ErrorCodeException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 产品型号-服务接口.
 */
@Slf4j
@Service
@RefreshScope
public class PdsProductModelSrv {
    @Resource
    private PdsProductModelMapper pdsProductModelMapper;
    @Resource
    private PdsBrandMapper pdsBrandMapper;
    @Resource
    private PdsProductModelRepository pdsProductModelRepository;
    @Resource
    private PdsCategorySrv pdsCategorySrv;
    @Resource
    private RemoteUserDetailsFeign remoteUserDetailsFeign;

    @Value("${permission.roleLabel.product_manager}")
    private String roleLabelProductManager;

    @Resource
    private PdsProductModelSerialMapper pdsProductModelSerialMapper;

    @Resource
    private PdsProductManagerBasicMapper pdsProductManagerBasicMapper;

    private String nameTemplate = "%s (%s)";

    public Paging<PdsProductModelDto> list(PdsProductModelQueryRequest queryRequest, LanguageEnum language) {
        if (StringUtils.isNotBlank(queryRequest.getUpdateTimeStart())) {
            queryRequest.setUpdateTimeStart(queryRequest.getUpdateTimeStart() + " 00:00:00");
        }
        if (StringUtils.isNotBlank(queryRequest.getUpdateTimeEnd())) {
            queryRequest.setUpdateTimeEnd(queryRequest.getUpdateTimeEnd() + " 23:59:59");
        }

        Page<PdsProductModelDto> page = new Page<>(queryRequest.getPageIndex(), queryRequest.getPageSize());
        IPage<PdsProductModelDto> iPage = pdsProductModelMapper.list(page, queryRequest);

        List<PdsProductModelDto> records = iPage.getRecords();
        if (records.size() > 0) {
            Set<Integer> brandIdSet = new HashSet<>();
            Set<Integer> cateIdSet = new HashSet<>();
            Set<Integer> pmUseridSet = new HashSet<>();

            for (PdsProductModelDto productModel : records) {
                brandIdSet.add(productModel.getBrandId());
                cateIdSet.add(productModel.getCateFirst());
                cateIdSet.add(productModel.getCateSecond());
                cateIdSet.add(productModel.getCateThird());
                pmUseridSet.add(productModel.getPmUserid());
                pmUseridSet.add(productModel.getUpdateBy());
            }

            List<PdsBrandPo> pdsBrandPoList = pdsBrandMapper.selectBatchIds(new ArrayList<>(brandIdSet));
            List<PdsCategoryPo> pdsCategoryList = pdsCategorySrv.getPdsCategoryList(new ArrayList<>(cateIdSet));
            QueryUserListReq req = new QueryUserListReq();
            req.setUserIds(new ArrayList<>(pmUseridSet));
            List<OumUserInfoRes> oumUserInfoResList = getUserListInfo(req);

            for (PdsProductModelDto productModel : records) {
                for (PdsBrandPo brand : pdsBrandPoList) {
                    if (productModel.getBrandId().equals(brand.getId())) {
                        productModel.setBrandName(brand.getBrand());
                        productModel.setBrandCode(brand.getCode());
                    }
                }

                for (PdsCategoryPo pdsCategoryPo : pdsCategoryList) {
                    String languageName = pdsCategorySrv.getLanguage(pdsCategoryPo.getId(), language);

                    if (productModel.getCateFirst().equals(pdsCategoryPo.getId())) {
                        productModel.setCateFirstName(languageName);
                        productModel.setCateFirstCode(pdsCategoryPo.getCateCode());

                    }
                    if (productModel.getCateSecond().equals(pdsCategoryPo.getId())) {
                        productModel.setCateSecondName(languageName);
                        productModel.setCateSecondCode(pdsCategoryPo.getCateCode());
                    }
                    if (productModel.getCateThird().equals(pdsCategoryPo.getId())) {
                        productModel.setCateThirdName(languageName);
                        productModel.setCateThirdCode(pdsCategoryPo.getCateCode());
                    }
                }

                for (OumUserInfoRes oumUserInfoRes : oumUserInfoResList) {
                    if (productModel.getPmUserid().equals(oumUserInfoRes.getId())) {
                        productModel.setPmUserName(oumUserInfoRes.getName());
                        productModel.setPmUserCode(oumUserInfoRes.getCode());
                    }

                    if (productModel.getUpdateBy().equals(oumUserInfoRes.getId())) {
                        productModel.setUpdateByName(oumUserInfoRes.getName());
                        productModel.setUpdateByCode(oumUserInfoRes.getCode());
                    }
                }
            }
        }

        return PagingUtils.convertToPaging(iPage);
    }

    public List<PdsProductModelUserResponse> getPmUser(String userName, String userCode) {
        List<PdsProductModelPo> productModelList = pdsProductModelMapper.getPmUser();

        List<PdsProductModelUserResponse> userList = productModelList.stream()
                .map(x -> {
                    PdsProductModelUserResponse userResponse = new PdsProductModelUserResponse();
                    userResponse.setId(x.getPmUserid());
                    return userResponse;
                }).toList();

        setUserInfo(userList);

        return likeFilterUser(userList, userName, userCode);
    }

    private void setUserInfo(List<PdsProductModelUserResponse> userList) {
        List<Integer> userIdList = userList.stream().map(x -> x.getId()).collect(Collectors.toList());

        QueryUserListReq req = new QueryUserListReq();
        req.setUserIds(userIdList);
        List<OumUserInfoRes> oumUserInfoResList = getUserListInfo(req);

        for (PdsProductModelUserResponse user : userList) {
            for (OumUserInfoRes userInfoRes : oumUserInfoResList) {
                if (user.getId().equals(userInfoRes.getId())) {
                    user.setName(userInfoRes.getName());
                    user.setCode(userInfoRes.getCode());
                }
            }
        }
    }

    private List<OumUserInfoRes> getUserListInfo(QueryUserListReq req) {
        log.info("请求【获取用户列表】接口，req：{}", JSON.toJSON(req));
        R<List<OumUserInfoRes>> resp = remoteUserDetailsFeign.getUserListInfo(req);
        log.info("请求【获取用户列表】接口，resp：{}", JSON.toJSON(resp));
        if (!resp.isSuccess()) {
            throw new ErrorCodeException(PdsErrorCode.PRODUCTMODEL_GETUSERLIST_FAIL);
        }
        List<OumUserInfoRes> oumUserInfoResList = resp.getData();
        return oumUserInfoResList;
    }


    public List<PdsProductModelUserResponse> getOpUser(String userName, String userCode) {
        List<PdsProductModelPo> productModelList = pdsProductModelMapper.getOpUser();

        List<PdsProductModelUserResponse> userList = productModelList.stream()
                .map(x -> {
                    PdsProductModelUserResponse userResponse = new PdsProductModelUserResponse();
                    userResponse.setId(x.getUpdateBy());
                    return userResponse;
                }).toList();

        setUserInfo(userList);
        return likeFilterUser(userList, userName, userCode);
    }

    public List<PdsProductModelUserResponse> likeFilterUser(List<PdsProductModelUserResponse> userList,
                                                            String userName,
                                                            String userCode) {
        if (StringUtils.isNotBlank(userName)) {
            userList = userList.stream().filter(x -> x.getName().contains(userName)).collect(Collectors.toList());
        }
        if (StringUtils.isNotBlank(userCode)) {
            userList = userList.stream().filter(x -> x.getCode().contains(userCode)).collect(Collectors.toList());
        }

        return userList;
    }

    /**
     * 生成产品型号.
     *
     * @param dto 参数
     * @return 产品型号
     */
    public String autoModelNo(PdsProductModelAutoModelNoDto dto) {
        if (StringUtils.isEmpty(dto.getBrandCode())
                || StringUtils.isEmpty(dto.getCateFirstCode())
                || StringUtils.isEmpty(dto.getCateSecondCode())
                || StringUtils.isEmpty(dto.getCateThirdCode())) {
            return null;
        }
        String brandCode = dto.getBrandCode();
        String cateCode = dto.getCateFirstCode() + dto.getCateSecondCode() + dto.getCateThirdCode();
        Integer currentSerial = pdsProductModelRepository.getCurrentSerial(brandCode, cateCode);

        // 尝试 10 次，直到生成一个不存在的型号,理论上只需要尝试几次就够了，正常情况不太可能出现手动输入编码跟自动生成的型号重复
        String modelNo = null;
        boolean needFixSerial = false;
        for (int i = 0; i < 10; i++) {
            modelNo = generateModelNo(brandCode, cateCode, currentSerial);

            LambdaQueryWrapper<PdsProductModelPo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(PdsProductModelPo::getModelNo, modelNo);
            PdsProductModelPo pdsProductModelPo = pdsProductModelMapper.selectOne(queryWrapper);
            if (pdsProductModelPo == null) {
                break;
            } else {
                needFixSerial = true;
            }
            currentSerial = currentSerial + 1;
        }

        // 纠正手动输入等原因造成的重复
        if (needFixSerial) {
            Integer lastSerial = currentSerial - 1;
            Integer count = pdsProductModelSerialMapper.updateSerial(lastSerial, brandCode, cateCode);
            // 更新数量为0时，新增一条记录，说明没有序列化的记录
            if (count.equals(0)) {
                PdsProductModelSerialPo serialPo = new PdsProductModelSerialPo();
                serialPo.setBrandCode(brandCode);
                serialPo.setCateCode(cateCode);
                serialPo.setCurrentSerial(lastSerial);
                serialPo.setIsDeleted(IsDeletedEnum.NOT_DELETE.getValue());
                serialPo.setStatus(CommonConstants.ACTIVE);
                serialPo.setCreateTime(LocalDateTime.now());
                serialPo.setUpdateTime(LocalDateTime.now());
                pdsProductModelSerialMapper.insert(serialPo);
            }
        }

        return modelNo;
    }

    /**
     * 生成产品型号.
     *
     * @param brandCode     品牌编码
     * @param cateCode      分类编码
     * @param currentSerial 当前序号
     * @return 产品型号
     */
    private String generateModelNo(String brandCode, String cateCode, Integer currentSerial) {
        return brandCode + "-" + cateCode + String.format("%02d", currentSerial);
    }

    public Integer add(PdsProductModel pdsBrand, Integer currentUserId) {
        return pdsProductModelRepository.add(pdsBrand, currentUserId);
    }

    @Transactional(rollbackFor = Exception.class)
    public Integer edit(PdsProductModel pdsBrand, Integer currentUserId) {
        //编辑型号信息
        Integer id = pdsProductModelRepository.edit(pdsBrand, currentUserId);

        //更新型号对应产品信息的产品负责人
        updateProductManagerByModelId(pdsBrand.getId().getId(), pdsBrand.getPmUserid());

        return id;
    }

    /**
     * 根据型号ID更新产品信息的产品负责人
     * <AUTHOR>
     * @Date 15:36 2025/7/23
     * @Param [modelId, userId]
     **/
    @Transactional(rollbackFor = Exception.class)
    public void updateProductManagerByModelId(Integer modelId, Integer userId) {
        LambdaUpdateWrapper<PdsProductManagerBasicPo> updateWrapper = new LambdaUpdateWrapper<>();
        //修改产品负责人ID
        updateWrapper.set(PdsProductManagerBasicPo::getProductManagerId, userId);
        updateWrapper.eq(PdsProductManagerBasicPo::getProductModelId, modelId);
        pdsProductManagerBasicMapper.update(updateWrapper);
    }

    public List<PdsProductModelExport> toPdsBrandExportList(List<PdsProductModelDto> list) {
        List<PdsProductModelExport> exportList = new ArrayList<>();
        PdsProductModelExport export;

        for (PdsProductModelDto dto : list) {
            export = new PdsProductModelExport();
            BeanUtil.copyProperties(dto, export, false);

            String cateFirstName = dto.getCateFirstName();
            String cateSecondName = dto.getCateSecondName();
            String cateThirdName = dto.getCateThirdName();
            StringBuilder cateNameSB = new StringBuilder();

            if (null != dto.getCateFirstName() && null != cateFirstName) {
                cateNameSB.append(cateFirstName).append(StrUtil.SLASH);
            }
            if (null != dto.getCateSecondName() && null != cateSecondName) {
                cateNameSB.append(cateSecondName).append(StrUtil.SLASH);
            }
            if (null != dto.getCateThirdName() && null != cateThirdName) {
                cateNameSB.append(cateThirdName);
            }

            export.setCateName(cateNameSB.toString());
            export.setUpdateTime(DateUtil.format(dto.getUpdateTime(), DatePattern.NORM_DATETIME_MINUTE_PATTERN));
            export.setPmUserName(String.format(nameTemplate, dto.getPmUserName(), dto.getPmUserCode()));
            export.setUpdateByName(String.format(nameTemplate, dto.getUpdateByName(), dto.getUpdateByCode()));

            exportList.add(export);
        }

        return exportList;
    }

    public List<PdsProductModelEnExport> toPdsBrandExportEnList(List<PdsProductModelDto> list) {
        List<PdsProductModelEnExport> exportList = new ArrayList<>();
        PdsProductModelEnExport export;

        for (PdsProductModelDto dto : list) {
            export = new PdsProductModelEnExport();
            BeanUtil.copyProperties(dto, export, false);

            String cateFirstName = dto.getCateFirstName();
            String cateSecondName = dto.getCateSecondName();
            String cateThirdName = dto.getCateThirdName();
            StringBuilder cateNameSB = new StringBuilder();

            if (null != dto.getCateFirstName() && null != cateFirstName) {
                cateNameSB.append(cateFirstName).append(StrUtil.SLASH);
            }
            if (null != dto.getCateSecondName() && null != cateSecondName) {
                cateNameSB.append(cateSecondName).append(StrUtil.SLASH);
            }
            if (null != dto.getCateThirdName() && null != cateThirdName) {
                cateNameSB.append(cateThirdName);
            }

            export.setCateName(cateNameSB.toString());
            export.setUpdateTime(DateUtil.format(dto.getUpdateTime(), DatePattern.NORM_DATETIME_MINUTE_PATTERN));
            export.setPmUserName(String.format(nameTemplate, dto.getPmUserName(), dto.getPmUserCode()));
            export.setUpdateByName(String.format(nameTemplate, dto.getUpdateByName(), dto.getUpdateByCode()));

            exportList.add(export);
        }

        return exportList;
    }

    public PdsProductModelPo pdsProductModelDetail(Integer id) {
        return this.pdsProductModelMapper.selectById(id);
    }

    public ImportChangePmVerify importChangePmVerify(List<PdsProductModelImportExport> importDataList, Integer currentUserId) {
        ImportChangePmVerify result = new ImportChangePmVerify();
        List<String> modelNoList = new ArrayList<>();
        for (PdsProductModelImportExport importData : importDataList) {
            if (StringUtils.isNotBlank(importData.getModelNo())) {
                modelNoList.add(importData.getModelNo());
            }
        }
        QueryUserListReq req = new QueryUserListReq();
        req.setRoleLabel(roleLabelProductManager);
        List<OumUserInfoRes> userList = getUserListInfo(req);
        LambdaQueryWrapper<PdsProductModelPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PdsProductModelPo::getIsDeleted, IsDeletedEnum.NOT_DELETE.getValue());
        queryWrapper.in(PdsProductModelPo::getModelNo, modelNoList);
        List<PdsProductModelPo> productModelList = pdsProductModelMapper.selectList(queryWrapper);
        Map<String, PdsProductModelPo> productModelMap = new HashMap<>();
        for (PdsProductModelPo productModelPo : productModelList) {
            productModelMap.put(productModelPo.getModelNo(), productModelPo);
        }
        Map<String, String> modelNoReDoMap = new HashMap<>();
        List<PdsProductModelPo> updatePoList = new ArrayList<>();
        List<PdsProductModelImportErrorExport> importErrorList = new ArrayList<>();
        Boolean verifyFlag = Boolean.TRUE;
        for (PdsProductModelImportExport importData : importDataList) {
            PdsProductModelImportErrorExport importError = new PdsProductModelImportErrorExport();
            BeanUtil.copyProperties(importData, importError);
            PdsProductModelPo localPdsProductModel = productModelMap.get(importData.getModelNo());
            importChangePmVerifyModelNo(importData, importError, modelNoReDoMap, localPdsProductModel, verifyFlag);
            OumUserInfoRes user = null;
            for (OumUserInfoRes userResponse : userList) {
                if (userResponse.getCode().equals(importData.getPmUserCode())) {
                    user = userResponse;
                }
            }
            if (StringUtils.isBlank(importData.getPmUserCode())) {
                importError.appendErrorMsg("产品经理工号不能为空");
                verifyFlag = Boolean.FALSE;
            } else if (null == user) {
                importError.appendErrorMsg("产品经理工号不存在");
                verifyFlag = Boolean.FALSE;
            } else if (null != localPdsProductModel) {
                localPdsProductModel.setPmUserid(user.getId());
                localPdsProductModel.setUpdateTime(LocalDateTime.now());
                localPdsProductModel.setUpdateBy(currentUserId);
            }
            importErrorList.add(importError);
            updatePoList.add(localPdsProductModel);
        }
        result.setVerifyFlag(verifyFlag);
        result.setUpdatePoList(updatePoList);
        result.setImportErrorList(importErrorList);
        return result;
    }

    public Boolean importChangePmVerifyModelNo(PdsProductModelImportExport importData,
                                               PdsProductModelImportErrorExport importError,
                                               Map<String, String> modelNoReDoMap,
                                               PdsProductModelPo localPdsProductModel,
                                               Boolean verifyFlag) {
        if (StringUtils.isBlank(importData.getModelNo())) {
            importError.appendErrorMsg("产品型号不能为空");
            verifyFlag = Boolean.FALSE;
        } else if (importData.getModelNo().length() > 28) {
            importError.appendErrorMsg("产品型号字符长度必须在1与28之间");
            verifyFlag = Boolean.FALSE;
        } else if (null != modelNoReDoMap.get(importData.getModelNo())) {
            importError.appendErrorMsg("产品型号重复");
            verifyFlag = Boolean.FALSE;
        } else if (verifyFlag && null == localPdsProductModel) {
            importError.appendErrorMsg("产品型号不存在");
            verifyFlag = Boolean.FALSE;
        } else if (null != localPdsProductModel) {
            localPdsProductModel.setModelNo(importData.getModelNo());
        }

        modelNoReDoMap.put(importData.getModelNo(), importData.getModelNo());
        return verifyFlag;
    }

    @OpLog(snaptSource = PdsProductModelChangePmSnapt.class, title = "导入变更负责人", businessType = BusinessType.IMPORT,
            systemModule = PdsSystemModule.class, businessModule = ProductModelBusinessModules.class)
    @Transactional(rollbackFor = Exception.class)
    public PdsProductModelPo importChangePmStorage(PdsProductModelPo updatePo) {
        pdsProductModelMapper.updateById(updatePo);
        //更新型号对应产品信息的产品负责人
        updateProductManagerByModelId(updatePo.getId(), updatePo.getPmUserid());
        return updatePo;
    }

    public List<PdsProductModelUserResponse> getPmRoleUserList(String userName, String userCode) {
        QueryUserListReq req = new QueryUserListReq();
        req.setRoleLabel(roleLabelProductManager);
        List<OumUserInfoRes> oumUserInfoResList = getUserListInfo(req);
        List<PdsProductModelUserResponse> userList = new ArrayList<>();
        for (OumUserInfoRes userInfoRes : oumUserInfoResList) {
            PdsProductModelUserResponse userResponse = new PdsProductModelUserResponse();
            userResponse.setId(userInfoRes.getId());
            userResponse.setName(userInfoRes.getName());
            userResponse.setCode(userInfoRes.getCode());
            userList.add(userResponse);
        }
        return likeFilterUser(userList, userName, userCode);
    }

    public List<PdsProductModelPo> all(String modelNo) {
        LambdaQueryWrapper<PdsProductModelPo> queryWrapper = Wrappers.<PdsProductModelPo>lambdaQuery()
                .eq(PdsProductModelPo::getIsDeleted, 0)
                .like(StringUtils.isNotEmpty(modelNo), PdsProductModelPo::getModelNo, modelNo)
                .orderByDesc(PdsProductModelPo::getCreateTime);
        return pdsProductModelMapper.selectList(queryWrapper);
    }

    public PdsProductModelPo getById(Integer productModelId) {
        if (null == productModelId) {
            return null;
        }
        return pdsProductModelMapper.selectById(productModelId);
    }

    public Map<PdsProductModel.PdsProductModelID, PdsProductModel> findProductModelMap(Collection<PdsProductModel.PdsProductModelID> productModelIds) {
       List<PdsProductModel> modelList =  pdsProductModelRepository.findProductModelListByIds(productModelIds);
        return modelList.stream().collect(Collectors.toMap(PdsProductModel::getId, Function.identity()));
    }

    /**
     * 补充名字跟code
     * @param records 数据
     * @param language 语言
     */
    public void buildNames(List<PdsProductModelResponse> records, LanguageEnum language) {
        if (records.size() > 0) {
            Set<Integer> brandIdSet = new HashSet<>();
            Set<Integer> cateIdSet = new HashSet<>();
            Set<Integer> pmUseridSet = new HashSet<>();

            for (PdsProductModelResponse productModel : records) {
                brandIdSet.add(productModel.getBrandId());
                cateIdSet.add(productModel.getCateFirst());
                cateIdSet.add(productModel.getCateSecond());
                cateIdSet.add(productModel.getCateThird());
                pmUseridSet.add(productModel.getPmUserid());
                pmUseridSet.add(productModel.getUpdateBy());
            }

            List<PdsBrandPo> pdsBrandPoList = pdsBrandMapper.selectBatchIds(new ArrayList<>(brandIdSet));
            List<PdsCategoryPo> pdsCategoryList = pdsCategorySrv.getPdsCategoryList(new ArrayList<>(cateIdSet));
            QueryUserListReq req = new QueryUserListReq();
            req.setUserIds(new ArrayList<>(pmUseridSet));
            List<OumUserInfoRes> oumUserInfoResList = getUserListInfo(req);

            for (PdsProductModelResponse productModel : records) {
                for (PdsBrandPo brand : pdsBrandPoList) {
                    if (productModel.getBrandId().equals(brand.getId())) {
                        productModel.setBrandName(brand.getBrand());
                        productModel.setBrandCode(brand.getCode());
                    }
                }

                for (PdsCategoryPo pdsCategoryPo : pdsCategoryList) {
                    String languageName = pdsCategorySrv.getLanguage(pdsCategoryPo.getId(), language);

                    if (productModel.getCateFirst().equals(pdsCategoryPo.getId())) {
                        productModel.setCateFirstName(languageName);
                        productModel.setCateFirstCode(pdsCategoryPo.getCateCode());

                    }
                    if (productModel.getCateSecond().equals(pdsCategoryPo.getId())) {
                        productModel.setCateSecondName(languageName);
                        productModel.setCateSecondCode(pdsCategoryPo.getCateCode());
                    }
                    if (productModel.getCateThird().equals(pdsCategoryPo.getId())) {
                        productModel.setCateThirdName(languageName);
                        productModel.setCateThirdCode(pdsCategoryPo.getCateCode());
                    }
                }

                for (OumUserInfoRes oumUserInfoRes : oumUserInfoResList) {
                    if (productModel.getPmUserid().equals(oumUserInfoRes.getId())) {
                        productModel.setPmUserName(oumUserInfoRes.getName());
                        productModel.setPmUserCode(oumUserInfoRes.getCode());
                    }

                    if (productModel.getUpdateBy().equals(oumUserInfoRes.getId())) {
                        productModel.setUpdateByName(oumUserInfoRes.getName());
                        productModel.setUpdateByCode(oumUserInfoRes.getCode());
                    }
                }
            }
        }

    }

    /**
     * 根据型号查询产品
     * @param modelNo 型号
     * @return 产品
     */
    public PdsProductModelPo getProductModelByModelNo(String modelNo) {
        return pdsProductModelMapper.getProductModelByModelNo(modelNo);
    }

    /**
     * 根据型号查询产品
     * @param productModelNoBasicList 型号
     * @return 产品
     */
    public Map<String, PdsProductModelPo> getProductModelMap(List<String> productModelNoBasicList) {
        Map<String, PdsProductModelPo> map = new HashMap<>();
        List<PdsProductModelPo> modelPos = pdsProductModelMapper.getProductModelMap(productModelNoBasicList);
        for (PdsProductModelPo pdsProductModelPo : modelPos) {
            map.put(pdsProductModelPo.getModelNo(), pdsProductModelPo);
        }
        return map;
    }

    /**
     * 根据ID获取产品型号
     * @param modelIds ID
     * @return 产品型号集合信息
     */
    public List<PdsProductModelPo> getModelList(List<Integer> modelIds) {
        if (modelIds ==null || modelIds.isEmpty()) {
            return new ArrayList<>();
        }
        return pdsProductModelMapper.selectBatchIds(modelIds);
    }

    public List<PdsProductModelPo> getPdsProductModelList(List<Integer> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PdsProductModelPo> queryWrapper = Wrappers.<PdsProductModelPo>lambdaQuery()
                .in(PdsProductModelPo::getId, idList);
        return pdsProductModelMapper.selectList(queryWrapper);

    }
}
