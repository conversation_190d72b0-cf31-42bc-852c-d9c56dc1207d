package com.renpho.erp.pds.application.product.manager.dto;

import lombok.Data;

import java.util.Date;


@Data
public class PdsPackagingMaterialsEdit {
    /**
     * id主键
     */
    private Integer id;

    /**
     * 关联的产品管理主键ID
     */
    private Integer productManagerId;

    /**
     * 包材类型
     */
    private String pmType;

    /**
     * 包材版本
     */
    private String pmVersion;

    /**
     * 适用国家/地区 ID
     */
    private Integer applicableRegionsId;

    /**
     * 有效时间（开始）
     */
    private Date effectiveTimeStart;

    /**
     * 有效时间（结束）
     */
    private Date effectiveTimeEnd;

    /**
     * 备注
     */
    private String remark;

    /**
     * 附件
     */
    private String attachmentId;
}
