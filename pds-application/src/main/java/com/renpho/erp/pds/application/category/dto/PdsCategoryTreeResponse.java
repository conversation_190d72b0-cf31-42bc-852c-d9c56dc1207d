package com.renpho.erp.pds.application.category.dto;

import com.renpho.erp.pds.application.common.dto.CommonResponse;
import com.renpho.erp.pds.domain.category.LanguageCategory;
import com.renpho.erp.pds.domain.common.LanguageEnum;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 品牌表, 列表树状.
 *
 * <AUTHOR>
 * @since 2024.9.20
 */
@Data
public class PdsCategoryTreeResponse extends CommonResponse {

    /**
     * 主键, 添加不需要，更新才需要
     */
    private Integer id;

    /**
     * 品类代码
     */
    private String cateCode;

    /**
     * 父级品类主键ID
     */
    private Integer parentId;

    /**
     * SN码生成系数
     */
    private Integer snCoefficient;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 备注
     */
    private String remark;

    /**
     * Menu Status 选项为 Active 和 Inactive 单选，默认选中Active
     */
    private Integer status;

    /**
     * 多语言
     */
    private List<LanguageCategory> names;

    /**
     * 子节点，最多3层
     */
    private List<PdsCategoryTreeResponse> children;

    /**
     * 名称，解析完多语言
     */
    private String name;

    /**
     * 查看是否包含关键字，模糊匹配，不区分大小写
     *
     * @param keyword 关键字
     * @return 是否包含关键字
     */
    public boolean containKeyword(String keyword, PdsCategoryTreeResponse parent) {
        if (parent.names != null) {
            for (LanguageCategory languageCategory : parent.names) {
                String thisName = languageCategory.getName();
                if (StringUtils.isNotBlank(thisName) && thisName.toUpperCase().contains(keyword.toUpperCase())) {
                    return true;
                }
            }
        }
        if (parent.children != null) {
            for (PdsCategoryTreeResponse child : parent.children) {
                return containKeyword(keyword, child);
            }
        }
        return false;
    }

    public void initLanguageName(LanguageEnum language) {
        if (language == null) {
            language = LanguageEnum.China;
        }
        this.name = getLanguage(language);
    }

    /**
     * 根据语言环境获取语言信息
     *
     * @param language 语言
     * @return 语言信息
     */
    public String getLanguage(LanguageEnum language) {
        if (names == null || names.isEmpty()) {
            return null;
        }
        if (LanguageEnum.China == language) {
            for (LanguageCategory languageData : names) {
                if (languageData.getLanguage().equals("zh-CN")) {
                    return languageData.getName();
                }
            }
        } else {
            for (LanguageCategory languageData : names) {
                if (languageData.getLanguage().equals("en-US")) {
                    return languageData.getName();
                }
            }
        }
        return null;
    }

}
