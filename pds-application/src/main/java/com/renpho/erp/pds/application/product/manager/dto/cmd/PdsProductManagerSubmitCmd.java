package com.renpho.erp.pds.application.product.manager.dto.cmd;

import com.renpho.erp.pds.application.product.manager.dto.PurchaseSkuImport;
import com.renpho.erp.pds.domain.common.CommonConstants;
import com.renpho.erp.pds.domain.common.LanguageEnum;
import com.renpho.erp.pds.domain.product.manager.authorize.PdsProductManagerSubmitBasicCmd;
import lombok.Data;

import java.util.List;

/**
 * 产品管理-主体
 *
 * <AUTHOR> @since 2024/11/04
 */
@Data
public class PdsProductManagerSubmitCmd {

    /**
     * 基本信息
     */
    private PdsProductManagerSubmitBasicCmd basic;

    /**
     * 属性信息
     */
    private PdsProductManagerSubmitProsCmd pros;

    /**
     * 物流信息
     */
    private PdsProductManagerSubmitLogisticsCmd logistics;

    /**
     * 配件信息
     */
    private List<PdsProductManagerSubmitFittingValidateCmd> fittingList;

    /**
     * 导入的原始数据, 不参与校验
     */
    private transient PurchaseSkuImport importData;

    /**
     * 新增类型: 0,草稿; 1,提交(默认)
     * <br/>提交: 按注解规则校验；草稿: 非空时校验格式
     */
    private Integer status;

    /**
     * 校验字段内容是否合法
     *
     * @param language 语言类型（中文/英文）
     */
    public void validateFields(LanguageEnum language) {
        // 是否启用严格模式（true: 按注解规则校验；false: 非空时校验格式）
        boolean isStrict = true;
        if (status == null || CommonConstants.ACTION_TYPE_SUBMIT.equals(status)) {
            status = CommonConstants.ACTION_TYPE_SUBMIT;
        } else if (status.equals(CommonConstants.ACTION_TYPE_DRAFT)) {
            isStrict = false;
        } else {
            throwValidationException(language, "新增类型参数不合法", "Add type parameter is illegal");
        }

        // 校验 basic 信息
        if (basic == null) {
            throwValidationException(language, "基本信息不能为空", "Basic information cannot be null");
        } else {
            basic.validateFields(language, isStrict);
        }

        // 校验 pros 信息
        if (pros == null) {
            throwValidationException(language, "属性信息不能为空", "Pros information cannot be null");
        } else {
            pros.validateFields(language, isStrict);
        }

        // 校验 logistics 信息
        if (logistics == null) {
            throwValidationException(language, "物流信息不能为空", "Logistics information cannot be null");
        } else {
            logistics.validateFields(language, isStrict);
        }

        // 校验 fittingList 信息
        if (fittingList != null) {
            if (fittingList.size() > 20) {
                throwValidationException(language, "配件信息最多只能包含20行", "Fitting information can contain at most 20 rows");
            }
            for (PdsProductManagerSubmitFittingValidateCmd fitting : fittingList) {
                fitting.validateFields(language, isStrict);
            }
        }
    }

    /**
     * 抛出校验异常
     */
    private void throwValidationException(LanguageEnum language, String chineseMessage, String englishMessage) {
        if (language == LanguageEnum.China) {
            throw new IllegalArgumentException(chineseMessage);
        } else {
            throw new IllegalArgumentException(englishMessage);
        }
    }
}

