package com.renpho.erp.pds.application.category.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.renpho.erp.pds.application.category.dto.*;
import com.renpho.erp.pds.domain.category.LanguageCategory;
import com.renpho.erp.pds.domain.category.PdsCategory;
import com.renpho.erp.pds.domain.category.PdsCategoryRepository;
import com.renpho.erp.pds.domain.common.LanguageEnum;
import com.renpho.erp.pds.domain.common.LanguageRepository;

import com.renpho.erp.pds.domain.common.PdsCommonStatus;
import com.renpho.erp.pds.infrastructure.exception.PdsErrorCode;
import com.renpho.erp.pds.infrastructure.persistence.dto.PdsCategoryQueryRequest;
import com.renpho.erp.pds.infrastructure.persistence.dto.PdsCategorySearchTreeRequest;
import com.renpho.erp.pds.infrastructure.persistence.mapper.PdsCategoryMapper;
import com.renpho.erp.pds.infrastructure.persistence.po.PdsCategoryPo;
import com.renpho.erp.pds.infrastructure.utils.PagingUtils;

import com.renpho.karma.dto.PageQuery;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.exception.ErrorCodeException;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 品类服务接口.
 *
 * <AUTHOR>
 * @since 2024.9.19
 */
@Service
public class PdsCategorySrv {

    @Resource
    private PdsCategoryMapper pdsCategoryMapper;

    @Autowired
    private PdsCategoryRepository pdsCategoryRepository;

    @Autowired
    private LanguageRepository<LanguageCategory> languageRepository;

    // 多语言表名
    private static final String L_NAME = "pds_category_language";

    /**
     * 最高多少层
     */
    private static final int MAX_TOP = 3;

    /**
     * 同级最多多少条
     */
    private static final int MAX_COUNT = 99;

    /**
     * 添加/更新 多语言信息
     *
     * @param languages 语言信息
     * @param pk        主键
     * @param add       是否添加
     */
    public void saveLanguages(PdsCategory languages, Integer pk, boolean add) {
        // 多语言
        List<LanguageCategory> names = languages.getNames();
        if (names != null && !names.isEmpty()) {
            for (LanguageCategory language : names) {
                language.setCategoryId(pk);
                if (add) {
                    languageRepository.insertLanguage(language, L_NAME);
                } else {
                    languageRepository.updateLanguage(language, L_NAME);
                }
            }
        }
    }

    /**
     * 查询多语言信息
     *
     * @param pk 主键ID
     * @return 多语言信息
     */
    public List<LanguageCategory> getLanguages(Integer pk) {
        return languageRepository.selectByParentIdAndLanguage(pk, L_NAME, LanguageCategory.class);
    }

    /**
     * 根据主键获取品类信息
     *
     * @param id 主键
     * @return 品类信息
     */
    public PdsCategoryPo getPdsCategoryPoDetail(Integer id) {
        return pdsCategoryMapper.selectById(id);
    }

    /**
     * 根据品类Id集合，获取对应的品类信息
     *
     * @param ids 品类Id集合
     * @return 品类信息
     */
    public List<PdsCategoryPo> getPdsCategoryList(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            return null;
        }
        return pdsCategoryMapper.getPdsCategoryList(ids);
    }

    /**
     * 根据语言环境获取语言信息
     *
     * @param categoryId 品类主表ID
     * @param language   语言
     * @return 语言信息
     */
    public String getLanguage(Integer categoryId, LanguageEnum language) {
        List<LanguageCategory> names = this.getLanguages(categoryId);
        if (names == null || names.isEmpty()) {
            return null;
        }

        // 根据不同语言枚举获取对应的语言编码
        String targetLanguageCode;
        switch (language) {
            case China:
                targetLanguageCode = "zh-CN";
                break;
            case English:
                targetLanguageCode = "en-US";
                break;
            default:
                return null; // 无法处理的语言类型
        }

        // 查找符合条件的语言信息
        return names.stream()
                .filter(languageData -> languageData.getLanguage().equals(targetLanguageCode))
                .map(LanguageCategory::getName)
                .findFirst()
                .orElse(null);
    }


    /**
     * 添加品类数据, 在新增品类时，我们需要：
     * <br/>1 确保新数据被正确添加到指定层级。
     * <br/>2 如果是一级品类，parent_id 为 -1；如果是二级或三级品类，parent_id 为所选父级的 ID。
     * <br/>3 新增数据默认插入当前层级最后一位（最大排序值）。
     * <br/>
     * <br/>步骤：
     * <br/>1 确定层级，并分配正确的 parent_id。
     * <br/>2 查询该父级下的最大 sort 值。
     * <br/>3 插入新数据，并将 sort 值设为 max(sort) + 1。
     *
     * @param pdsCategory 品类数据
     * @return 主键id
     */
    @Transactional
    public int add(PdsCategory pdsCategory) {
        Integer parentId = pdsCategory.getParentId();

        checkIsValid(pdsCategory);

        // 获取当前层级的最大 sort 值
        Integer maxSort = pdsCategoryMapper.getMaxSortByParentId(parentId);
        int sortValue = (maxSort != null) ? maxSort + 1 : 1;

        // 判断同级别是否已经有99条数据
        if (sortValue > MAX_COUNT) {
            throw new ErrorCodeException(PdsErrorCode.CATEGORY_LEVEL_SAME_MAX);
        }

        // 检查同级分类下cate_code是否重复
        int count = pdsCategoryMapper.checkDuplicateCateCode(parentId, pdsCategory.getCateCode());
        if (count > 0) {
            throw new ErrorCodeException(PdsErrorCode.CATEGORY_LEVEL_SAME_CODE);
        }

        // 补充信息跟排序值
        pdsCategory.setParentId(parentId);
        pdsCategory.setSort(sortValue);

        Integer pk = pdsCategoryRepository.savePdsCategory(pdsCategory);
        // 多语言
        saveLanguages(pdsCategory, pk, true);
        return pk;
    }

    private void checkIsValid(PdsCategory pdsCategory) {
        Integer parentId = pdsCategory.getParentId();

        // 判断是否为一级品类
        if (parentId == null) {
            parentId = -1;
        }
        if (parentId != -1) {

            // 验证父级是否存在
            PdsCategoryPo parent = pdsCategoryMapper.selectById(parentId);
            if (parent == null) {
                throw new ErrorCodeException(PdsErrorCode.CATEGORY_PARENT_NOT_FOUND);
            }

            // 检查层级是否超过 3 级
            int level = 1;  // 默认1级
            if (pdsCategory.getParentId() != -1) { // 非根节点
                level = calculateCategoryLevel(parent);
                if (level >= MAX_TOP) {
                    throw new ErrorCodeException(PdsErrorCode.CATEGORY_LEVEL_CAN_NOT_GT);
                }
                if (level == 2) {
                    //sn系数合法性校验
                    Integer snCoefficient = Optional.ofNullable(pdsCategory.getSnCoefficient()).orElse(0);
                    if (snCoefficient > 500 || snCoefficient < 100) {
                        throw new ErrorCodeException(PdsErrorCode.CATEGORY_SN_COEFFICIENT_ERROR, 100, 500);
                    }
                }
            }
        }
    }

    /**
     * 计算分类的层级
     *
     * @param category 当前分类
     * @return 层级数
     */
    private int calculateCategoryLevel(PdsCategoryPo category) {
        int level = 1;
        while (category != null && category.getParentId() != -1) {
            category = pdsCategoryMapper.selectById(category.getParentId());
            level++;
        }
        return level;
    }

    /**
     * 移动顺序,对于移动品类的操作：
     * <p>
     * <br/>1 确保移动仅限于同级别。
     * <br/>2 调整 sort 值来实现排序的更新。
     * <br/>3 移动应该受限于每层级最多99条数据的规则。
     * <br/>
     * <br/>步骤：
     * <br/>1 查询当前品类和它在同级别的上一个或下一个品类。
     * <br/>2 交换这两个品类的 sort 值。
     *
     * @param categoryId 要移动的品类Id
     * @param moveUp     是否上移
     */
    public void moveCategory(int categoryId, boolean moveUp) {
        pdsCategoryRepository.moveCategory(categoryId, moveUp);
    }

    /**
     * 获取树状品类结构
     *
     * @param pdsCategoryQueryRequest 参数
     * @return 列表
     */
    public List<PdsCategoryTreeResponse> getCategoryTree(PdsCategoryQueryRequest pdsCategoryQueryRequest) {
        LanguageEnum language = LanguageEnum.getEnum(pdsCategoryQueryRequest.getLanguage());
        String keyword = pdsCategoryQueryRequest.getKeyword();

        // 查询所有一级分类 (parentId = -1)
        List<PdsCategoryPo> topLevelCategories = pdsCategoryMapper.getCategoriesByParentId(-1);

        // 构建树结构
        List<PdsCategoryTreeResponse> result = new ArrayList<PdsCategoryTreeResponse>();
        for (PdsCategoryPo topLevelCategory : topLevelCategories) {
            PdsCategoryTreeResponse treeVO = buildCategoryTree(topLevelCategory, language, pdsCategoryQueryRequest.getStatus());
            if (treeVO != null) {
                // 关键字匹配
                if (StringUtils.isNotBlank(keyword)) {
                    if (!treeVO.containKeyword(keyword, treeVO)) {
                        continue;
                    }
                }
                result.add(treeVO);
            }
        }

        return result;
    }

    /**
     * 构建树状结构
     *
     * @param language    language
     * @param statusValue status
     * @param category    品类数据
     */
    private PdsCategoryTreeResponse buildCategoryTree(PdsCategoryPo category, LanguageEnum language, Integer statusValue) {
        if (statusValue != null) {
            PdsCommonStatus status = PdsCommonStatus.getPdsCommonStatus(statusValue);
            // 有效状态要一致
            if (status.getValue().intValue() != category.getStatus().intValue()) {
                return null;
            }
        }

        PdsCategoryTreeResponse treeVO = new PdsCategoryTreeResponse();
        treeVO.setId(category.getId());
        treeVO.setCateCode(category.getCateCode());
        treeVO.setSort(category.getSort());
        treeVO.setStatus(category.getStatus());
        treeVO.setRemark(category.getRemark());
        treeVO.setSnCoefficient(category.getSnCoefficient());

        // 绑定多语言信息
        List<LanguageCategory> names = this.getLanguages(category.getId());
        treeVO.setNames(names);
        treeVO.initLanguageName(language);

        // 查询子分类
        List<PdsCategoryPo> childCategories = pdsCategoryMapper.getCategoriesByParentId(category.getId());

        // 递归构建子分类树
        List<PdsCategoryTreeResponse> children = new ArrayList<PdsCategoryTreeResponse>();
        for (PdsCategoryPo child : childCategories) {
            PdsCategoryTreeResponse childTreeResponse = buildCategoryTree(child, language, statusValue);
            if (childTreeResponse != null) {
                childTreeResponse.setParentId(category.getId());
                children.add(childTreeResponse);
            }
        }

        // 按sort字段排序
        children.sort(Comparator.comparingInt(PdsCategoryTreeResponse::getSort));
        treeVO.setChildren(children);

        return treeVO;
    }


    /**
     * 跟新品类数据
     *
     * @param pdsCategory 品牌数据
     * @return 受影响条数
     */
    public int update(PdsCategory pdsCategory) {

        checkIsValid(pdsCategory);
        // 多语言
        saveLanguages(pdsCategory, pdsCategory.getId().getId(), false);
        if (pdsCategory.getRemark() != null || pdsCategory.getStatus() != null) {
            return pdsCategoryRepository.updatePdsCategory(pdsCategory);
        }
        return 1;
    }

    /**
     * 列表分页查询,单表
     *
     * @param pageQuery 查询参数
     * @return 结果
     */
    public Paging<PdsCategoryPo> queryWithPageWithSingle(PageQuery pageQuery) {
        Page<PdsCategoryPo> page = new Page<PdsCategoryPo>(pageQuery.getPageIndex(), pageQuery.getPageSize());

        // 创建 QueryWrapper 对象
        LambdaQueryWrapper<PdsCategoryPo> queryWrapper = new LambdaQueryWrapper<PdsCategoryPo>();
        IPage<PdsCategoryPo> iPage = pdsCategoryMapper.selectPage(page, queryWrapper);
        return PagingUtils.convertToPaging(iPage);
    }

    /**
     * 禁用、启用
     *
     * @param pdsCategory 状态数据
     * @return 受影响条数
     */
    public int changeStatus(PdsCategory pdsCategory) {
        changeCategoryStatus(pdsCategory.getId().getId(), pdsCategory.getStatus());
        return 1;
    }

    /**
     * 更改分类状态
     *
     * @param categoryId  分类ID
     * @param statusValue 新的状态，1启用，0禁用
     */
    private void changeCategoryStatus(Integer categoryId, int statusValue) {
        // 更新当前分类状态
        pdsCategoryMapper.updateCategoryStatus(categoryId, statusValue);
        PdsCommonStatus status = PdsCommonStatus.getPdsCommonStatus(statusValue);
        if (PdsCommonStatus.Active == status) {
            // 如果是启用状态，递归启用父级分类
            enableParentCategories(categoryId);
        } else {
            // 如果是禁用状态，递归禁用子级分类
            disableChildCategories(categoryId);
        }
    }

    /**
     * 递归启用父级分类
     *
     * @param categoryId 当前分类ID
     */
    private void enableParentCategories(Integer categoryId) {
        Integer parentId = pdsCategoryMapper.getParentIdById(categoryId);
        if (parentId != null && parentId != -1) {
            // 启用父级分类
            pdsCategoryMapper.updateParentStatusToActive(parentId);
            // 递归启用上级
            enableParentCategories(parentId);
        }
    }

    /**
     * 递归禁用子级分类
     *
     * @param categoryId 当前分类ID
     */
    private void disableChildCategories(Integer categoryId) {
        // 禁用当前分类的所有子分类
        pdsCategoryMapper.updateChildrenStatusToInactive(categoryId);
        // 获取所有子分类的ID
        List<Integer> childIds = pdsCategoryMapper.getChildrenIdsByParentId(categoryId);
        for (Integer childId : childIds) {
            // 递归禁用子分类
            disableChildCategories(childId);
        }
    }

    /**
     * 把树状列表格式转成产品需要的excel格式
     *
     * @param categoryTree 数据
     * @param language     语言参数
     * @return excel格式
     */
    public List<PdsCategoryTreeVo> convertToPdsCategoryTreeVo(List<PdsCategoryTreeResponse> categoryTree, LanguageEnum language) {
        List<PdsCategoryTreeVo> categoryTreeVos = new LinkedList<>();

        for (PdsCategoryTreeResponse one : categoryTree) {
            String codeLevelOne = one.getCateCode();
            String nameLevelOne = one.getLanguage(language);

            // 添加一级分类
            categoryTreeVos.add(createCategoryTreeVo(new PdsCategoryCodeLevelVo(codeLevelOne, nameLevelOne, null, null, null, null, one.getRemark(), one.getStatus())));

            // 遍历二级分类
            for (PdsCategoryTreeResponse two : one.getChildren()) {
                String codeLevelTwo = two.getCateCode();
                String nameLevelTwo = two.getLanguage(language);

                // 添加二级分类
                categoryTreeVos.add(createCategoryTreeVo(new PdsCategoryCodeLevelVo(codeLevelOne, nameLevelOne, codeLevelTwo, nameLevelTwo, null, null, two.getRemark(), two.getStatus())));

                // 遍历三级分类
                for (PdsCategoryTreeResponse three : two.getChildren()) {
                    String codeLevelThree = three.getCateCode();
                    String nameLevelThree = three.getLanguage(language);

                    // 添加三级分类
                    categoryTreeVos.add(createCategoryTreeVo(new PdsCategoryCodeLevelVo(codeLevelOne, nameLevelOne, codeLevelTwo, nameLevelTwo, codeLevelThree, nameLevelThree, three.getRemark(), three.getStatus())));
                }
            }
        }
        return categoryTreeVos;
    }

    /**
     * 创建 PdsCategoryTreeVo 对象
     *
     * @param params 参数
     */
    private PdsCategoryTreeVo createCategoryTreeVo(PdsCategoryCodeLevelVo params) {
        PdsCategoryTreeVo vo = new PdsCategoryTreeVo();
        vo.setCodeLevelOne(params.getCodeLevelOne());
        vo.setNameLevelOne(params.getNameLevelOne());
        vo.setCodeLevelTwo(params.getCodeLevelTwo());
        vo.setNameLevelTwo(params.getNameLevelTwo());
        vo.setCodeLevelThree(params.getCodeLevelThree());
        vo.setNameLevelThree(params.getNameLevelThree());
        vo.setRemark(params.getRemark());
        vo.setStatus(params.getStatus());
        return vo;
    }

    /**
     * 获取品类名称，一直到顶层
     *
     * @param categoryId 品类id
     * @param language   语言
     * @param nameTree   名称树
     */
    public void getCategoryTreeName(Integer categoryId, LanguageEnum language, StringBuilder nameTree) {
        PdsCategoryPo pdsCategoryPo = pdsCategoryMapper.selectById(categoryId);
        if (pdsCategoryPo != null && pdsCategoryPo.getId() != null) {
            List<LanguageCategory> names = this.getLanguages(pdsCategoryPo.getId());
            String name = LanguageEnum.getLanguage(names, language);
            nameTree.append(name);
            if (pdsCategoryPo.getParentId() != null && pdsCategoryPo.getParentId() != -1) {
                nameTree.append(" / ");
                getCategoryTreeName(pdsCategoryPo.getParentId(), language, nameTree);
            }
        }
    }

    /**
     * 把前端的三级查询条件转换成三级品类ID列表
     *
     * @param categoryConditions 三级品类条件
     * @return 三级品类ID列表
     */
    public List<Integer> convertCategoryIdsToCategoryThreeLevelIds(List<PdsCategorySearchTreeRequest> categoryConditions) {
        if (categoryConditions == null || categoryConditions.isEmpty()) {
            return null;
        }
        List<Integer> categoryOneLevelIds = new ArrayList<Integer>();
        List<Integer> categoryTwoLevelIds = new ArrayList<Integer>();
        List<Integer> categoryThreeLevelIds = new ArrayList<Integer>();

        // 将条件分类到一级、二级和三级品类列表中
        PdsCategorySearchTreeRequest.categorizeIds(categoryConditions, categoryOneLevelIds, categoryTwoLevelIds, categoryThreeLevelIds);

        // 查找一级品类的所有子品类
        if (!categoryOneLevelIds.isEmpty()) {
            List<Integer> secondLevelIds = pdsCategoryMapper.findChildCategoryIds(categoryOneLevelIds);
            if (!secondLevelIds.isEmpty()) {
                List<Integer> thirdLevelIds = pdsCategoryMapper.findChildCategoryIds(secondLevelIds);
                categoryThreeLevelIds.addAll(thirdLevelIds);
            }
        }

        // 查找二级品类的所有子品类
        if (!categoryTwoLevelIds.isEmpty()) {
            List<Integer> thirdLevelIds = pdsCategoryMapper.findChildCategoryIds(categoryTwoLevelIds);
            categoryThreeLevelIds.addAll(thirdLevelIds);
        }

        // TODO 特殊处理, 没有限制的就默认添加负一，表示什么都不给你查询
        if (categoryThreeLevelIds.isEmpty()) {
            categoryThreeLevelIds.add(-1);
        }

        return categoryThreeLevelIds;
    }

    /**
     * 查询品类名称
     *
     * @param nameViewQuery 参数
     * @return 结果
     */
    public PdsCategoryNameViewResponse queryCategoryNames(PdsCategoryNameViewRequest nameViewQuery) {
        PdsCategoryNameViewResponse response = new PdsCategoryNameViewResponse();
        LanguageEnum language = LanguageEnum.getEnum(nameViewQuery.getLanguage());

        if (nameViewQuery.getCategoryId() != null) {
            StringBuilder nameTree = new StringBuilder();
            this.getCategoryTreeName(nameViewQuery.getCategoryId(), language, nameTree);
            response.setName(nameTree.toString());
        }

        if (nameViewQuery.getCategoryIds() != null && !nameViewQuery.getCategoryIds().isEmpty()) {
            for (Integer categoryId : nameViewQuery.getCategoryIds()) {
                StringBuilder nameTree = new StringBuilder();
                this.getCategoryTreeName(categoryId, language, nameTree);
                response.putName(categoryId, nameTree.toString());
            }

        }

        return response;
    }

    /**
     * 获取品类树
     *
     * @param categoryId 品类id
     */
    public void getCategoryTreeIds(Integer categoryId, Deque<Integer> categoryIds) {
        PdsCategoryPo pdsCategoryPo = pdsCategoryMapper.selectById(categoryId);
        if (pdsCategoryPo != null && pdsCategoryPo.getId() != null) {
            categoryIds.push(pdsCategoryPo.getId());
            if (pdsCategoryPo.getParentId() != null && pdsCategoryPo.getParentId() != -1) {
                getCategoryTreeIds(pdsCategoryPo.getParentId(), categoryIds);
            }
        }
    }
}
