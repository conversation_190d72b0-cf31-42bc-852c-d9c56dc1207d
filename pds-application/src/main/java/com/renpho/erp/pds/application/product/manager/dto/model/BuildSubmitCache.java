package com.renpho.erp.pds.application.product.manager.dto.model;

import cn.hutool.core.util.StrUtil;
import com.renpho.erp.data.trans.dict.Dict;
import com.renpho.erp.ftm.client.response.FileInfoResponse;
import com.renpho.erp.mdm.client.saleschannel.RemoteSalesChannelService;
import com.renpho.erp.pds.application.color.service.PdsColorSrv;
import com.renpho.erp.pds.application.common.converter.PdsProductManagerPoConverter;
import com.renpho.erp.pds.application.common.service.PdsCommonSrv;
import com.renpho.erp.pds.application.country.service.PdsCountryRegionSrv;
import com.renpho.erp.pds.application.product.manager.dto.ImportProductManagerVerify;
import com.renpho.erp.pds.application.product.manager.dto.PurchaseSkuImportError;
import com.renpho.erp.pds.application.product.manager.dto.cmd.PdsProductManagerSubmitCmd;
import com.renpho.erp.pds.application.product.manager.dto.cmd.PdsProductManagerSubmitFittingValidateCmd;
import com.renpho.erp.pds.application.product.manager.dto.cmd.PdsProductManagerSubmitProsCmd;
import com.renpho.erp.pds.application.product.model.service.PdsProductModelSrv;
import com.renpho.erp.pds.domain.common.ChannelType;
import com.renpho.erp.pds.domain.common.file.FilePathConstants;
import com.renpho.erp.pds.domain.common.file.FileUploadType;
import com.renpho.erp.pds.domain.product.manager.authorize.PdsProductManagerSubmitBasicCmd;
import com.renpho.erp.pds.infrastructure.persistence.po.PdsProductModelPo;
import com.renpho.erp.smc.client.dto.OumUserInfoRes;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;

import java.util.*;

/**
 * 产品管理-批量导入-缓存.
 *
 * <AUTHOR>
 * @since 2024.11.22
 */
@Data
public class BuildSubmitCache {

    private PdsCommonSrv pdsCommonSrv;

    private PdsCountryRegionSrv pdscountryRegionSrv;

    private PdsColorSrv colorSrv;

    private PdsProductModelSrv productModelSrv;

    private RemoteSalesChannelService remoteSalesChannelService;

    /**
     * 封面图片链接
     */
    private  Set<String> productCoverImageIdBasicList = new HashSet<>();

    /**
     * 添加 封面图片链接
     */
    public void addProductCoverImageIdBasicList(String productCoverImageIdBasic) {
        this.productCoverImageIdBasicList.add(productCoverImageIdBasic);
    }

    /**
     * 封面图片链接 : 封面图片详情信息
     */
    private Map<String,FileInfoResponse> imageMap = new HashMap<String,FileInfoResponse>();

    /**
     * 国家地区编码
     */
    private Set<String> countryRegionCodeBasicList = new HashSet<>();

    /**
     * 添加 国家地区编码
     */
    public void addCountryRegionCodeBasicList(String countryRegionCodeBasic) {
        this.countryRegionCodeBasicList.add(countryRegionCodeBasic);
    }

    /**
     * 国家地区编码 : 国家地区ID
     */
    private Map<String, Integer> countryMap = new HashMap<String, Integer>();

    /**
     * 颜色编码
     */
    private  Set<String> colorCodeBasicList = new HashSet<>();

    /**
     * 添加 颜色编码
     */
    public void addColorCodeBasicList(String colorCodeBasic) {
        this.colorCodeBasicList.add(colorCodeBasic);
    }

    /**
     * 颜色编码 : 颜色ID
     */
    private Map<String, Integer> colorMap = new HashMap<>();

    /**
     * 产品型号
     */
    private Set<String> productModelNoBasicList = new HashSet<String>();

    /**
     * 添加 产品型号
     */
    public void addProductModelNoBasicList(String productModelNoBasic) {
        this.productModelNoBasicList.add(productModelNoBasic);
    }

    /**
     * 产品型号 : 产品型号信息
     */
    private Map<String, PdsProductModelPo> productModelMap = new HashMap<String, PdsProductModelPo>();

    /**
     * 销售渠道编码
     */
    private Set<String> salesChannelCodeBasicList = new HashSet<>();

    /**
     * 添加 销售渠道编码
     */
    public void addSalesChannelCodeBasicList(String salesChannelCodeBasic) {
        this.salesChannelCodeBasicList.add(salesChannelCodeBasic);
    }

    // /**
    //  * 销售渠道编码 : 销售渠道ID
    //  */
    // private Map<String,Integer> salesChannelMap = new HashMap<>();
    /**
     * 产品经理工号
     */
    private List<String> productManagerCodeBasicList = new ArrayList<String>();

    /**
     * 添加 产品经理工号
     */
    public void addProductManagerCodeBasicList(String productManagerCodeBasic) {
        this.productManagerCodeBasicList.add(productManagerCodeBasic);
    }

    /**
     * 商务负责人工号
     */
    private List<String> businessManagerCodeBasicList = new ArrayList<String>();

    /**
     * 添加 商务负责人工号
     */
    public void addBusinessManagerCodeBasicList(String businessManagerCodeBasic) {
        this.businessManagerCodeBasicList.add(businessManagerCodeBasic);
    }

    /**
     * 采购负责人工号
     */
    private List<String> procurementManagerCodeBasicList = new ArrayList<String>();

    /**
     * 添加 采购负责人工号
     */
    public void addProcurementManagerCodeBasicList(String procurementManagerCodeBasic) {
        this.procurementManagerCodeBasicList.add(procurementManagerCodeBasic);
    }

    /**
     * 计划负责人工号
     */
    private List<String> planningManagerCodeBasicList = new ArrayList<String>();

    /**
     * 添加 计划负责人工号
     */
    public void addPlanningManagerCodeBasicList(String planningManagerCodeBasic) {
        this.planningManagerCodeBasicList.add(planningManagerCodeBasic);
    }

    /**
     * 合规负责人工号
     */
    private List<String> complianceManagerCodeBasicList = new ArrayList<String>();

    /**
     * 添加 合规负责人工号
     */
    public void addComplianceManagerCodeBasicList(String complianceManagerCodeBasic) {
        this.complianceManagerCodeBasicList.add(complianceManagerCodeBasic);
    }

    /**
     * 包材负责人工号
     */
    private List<String> packingMaterialManagerCodeBasicList = new ArrayList<String>();

    /**
     * 添加 包材负责人工号
     */
    public void addPackingMaterialManagerCodeBasicList(String packingMaterialManagerCodeBasic) {
        this.packingMaterialManagerCodeBasicList.add(packingMaterialManagerCodeBasic);
    }

    /**
     * 员工工号 : 员工ID
     */
    private Map<String,Integer> userMapCode = new HashMap<>();

    /**
     * 产品类型 value值, 字典：product_type
     */
    private List<String> productTypeBasicList = new ArrayList<String>();

    /**
     * 添加 产品类型 value值, 字典：product_type
     */
    public void addProductTypeBasicList(String productTypeBasic) {
        this.productTypeBasicList.add(productTypeBasic);
    }

    /**
     * 产品类型, 字典：product_type
     */
    private List<Dict.ParamItem> productTypeItemList = new ArrayList<>();


    /**
     * 产品单位,字典 product_unit
     */
    private List<String> productUnitPros = new ArrayList<String>();

    /**
     * 产品单位,字典 product_unit
     */
    private List<Dict.ParamItem> productUnitItemList = new ArrayList<>();

    /**
     * 添加 产品单位,字典 product_unit
     * @param productUnitPros 产品单位,字典 product_unit
     */
    public void addProductUnitList(String productUnitPros) {
        this.productUnitPros.add(productUnitPros);
    }


    public void buildSrv(PdsCommonSrv pdsCommonSrv,
                         PdsCountryRegionSrv pdscountryRegionSrv,
                         PdsColorSrv colorSrv,
                         PdsProductModelSrv productModelSrv,
                         RemoteSalesChannelService remoteSalesChannelService) {
        this.pdsCommonSrv = pdsCommonSrv;
        this.pdscountryRegionSrv = pdscountryRegionSrv;
        this.colorSrv = colorSrv;
        this.productModelSrv = productModelSrv;
        this.remoteSalesChannelService = remoteSalesChannelService;
    }


    /**
     * 构建批量信息缓存
     */
    public void buildCache(Logger log) {
        // 封面图片链接 : 封面图片详情信息
        Long start = System.currentTimeMillis();

        //批量上传 封面图片链接
        start = System.currentTimeMillis();
        List<FileInfoResponse> fileInfoResponseList = pdsCommonSrv.uploadFileByUrls(productCoverImageIdBasicList.stream().toList(), FileUploadType.PUBLIC, FilePathConstants.PRODUCT_MANAGER);
        if (fileInfoResponseList != null) {
            for (FileInfoResponse fileInfoResponse : fileInfoResponseList) {
                if(fileInfoResponse!=null){
                    this.imageMap.put(fileInfoResponse.getOriginalFilename(), fileInfoResponse);
                }
            }
        }
        long end = System.currentTimeMillis();
        log.info("图片批量查询 耗时: " + (end - start)+" ms!");

        // 国家地区编码 : 国家地区ID
        start = System.currentTimeMillis();
        this.countryMap = pdscountryRegionSrv.getCountryRegionMap(countryRegionCodeBasicList.stream().toList());
        end = System.currentTimeMillis();
        log.info("国家地区批量查询 耗时: " + (end - start)+" ms!");

        // 颜色编码 : 颜色ID
        start = System.currentTimeMillis();
        this.colorMap = colorSrv.getColorMap(colorCodeBasicList.stream().toList());
        end = System.currentTimeMillis();
        log.info("颜色批量查询 耗时: " + (end - start)+" ms!");

        // 产品型号 : 产品型号信息
        start = System.currentTimeMillis();
        this.productModelMap = productModelSrv.getProductModelMap(productModelNoBasicList.stream().toList());
        end = System.currentTimeMillis();
        log.info("产品型号批量查询 耗时: " + (end - start));

        // // 销售渠道编码 : 销售渠道ID
        // start = System.currentTimeMillis();
        // List<SalesChannelVo> salesChannelList = pdsCommonSrv.getSalesChannelList(salesChannelCodeBasicList.stream().toList());
        // if (salesChannelList != null) {
        //     for (SalesChannelVo salesChannelVo : salesChannelList) {
        //         salesChannelMap.put(salesChannelVo.getChannelCode(), salesChannelVo.getId());
        //     }
        // }
        // end = System.currentTimeMillis();
        // log.info("销售渠道批量查询 耗时: " + (end - start)+" ms!");
        // 员工工号 : 员工ID
        Set<String> userCodes = new HashSet<>();
        userCodes.addAll(this.productManagerCodeBasicList);
        userCodes.addAll(this.procurementManagerCodeBasicList);
        userCodes.addAll(this.businessManagerCodeBasicList);
        userCodes.addAll(this.planningManagerCodeBasicList);
        userCodes.addAll(this.complianceManagerCodeBasicList);
        userCodes.addAll(this.packingMaterialManagerCodeBasicList);

        start = System.currentTimeMillis();
        Map<String, OumUserInfoRes> userMap = pdsCommonSrv.getUserMapByUserCodes(userCodes.stream().toList());
        if (userMap!= null) {
            for (Map.Entry<String, OumUserInfoRes> entry : userMap.entrySet()) {
                userMapCode.put(entry.getKey(), entry.getValue().getId());
            }
        }
        end = System.currentTimeMillis();
        log.info("员工批量查询 耗时: " + (end - start)+" ms!");

        // 产品类型, 字典：product_type
        start = System.currentTimeMillis();
        this.productTypeItemList = pdsCommonSrv.getDict("product_type");
        end = System.currentTimeMillis();
        log.info("产品类型批量查询 耗时: " + (end - start)+" ms!");

        // 产品单位,字典 product_unit
        start = System.currentTimeMillis();
        this.productUnitItemList = pdsCommonSrv.getDict("product_unit");
        end = System.currentTimeMillis();
        log.info("产品单位批量查询 耗时: " + (end - start)+" ms!");
    }

    /**
     * 够级成产品管理数据
     * @param submitBasicCmdList 数据
     * @return 获取通过数据是否存在的数据
     */
    public List<PdsProductManagerSubmitCmd> buildDataBasicCmdList(List<PdsProductManagerSubmitCmd> submitBasicCmdList, ImportProductManagerVerify verify, Logger log){
        List<PdsProductManagerSubmitCmd> rightBasicCmdList = new ArrayList<>();
        for (PdsProductManagerSubmitCmd submitBasicCmd : submitBasicCmdList) {
            try{
                //基本信息
                PdsProductManagerSubmitBasicCmd basic = submitBasicCmd.getBasic();
                // 封面图片链接
                if (StringUtils.isNotBlank(basic.getProductCoverImageIdBasic())) {
                    String fileId = getFileInfoId(basic.getProductCoverImageIdBasic(), this.imageMap);
                    if (StringUtils.isNotBlank(fileId)) {
                        basic.setProductCoverImageId(fileId);
                    } else {
                        throw new RuntimeException("封面图片上传失败: " + basic.getProductCoverImageIdBasic());
                    }
                }
                // 国家代码
                if (StringUtils.isNotBlank(basic.getCountryRegionCodeBasic())) {
                    Integer countryRegionId = this.countryMap.get(basic.getCountryRegionCodeBasic());
                    if (countryRegionId != null) {
                        basic.setCountryRegionId(countryRegionId);
                    } else {
                        throw new RuntimeException("国家地区编码不存在: " + basic.getCountryRegionCodeBasic());
                    }
                }
                // 颜色编码
                if (StringUtils.isNotBlank(basic.getColorCodeBasic())) {
                    Integer colorId = this.colorMap.get(basic.getColorCodeBasic());
                    if (colorId!= null) {
                        basic.setColorId(colorId);
                    } else {
                        throw new RuntimeException("颜色代码不存在: " + basic.getColorCodeBasic());
                    }
                }
                // 产品型号
                if (StringUtils.isNotBlank(basic.getProductModelNoBasic())) {
                    PdsProductModelPo productModelPo = this.productModelMap.get(basic.getProductModelNoBasic());
                    if (productModelPo!= null) {
                        basic.setProductModelId(productModelPo.getId());
                        basic.setBrandId(productModelPo.getBrandId());
                        basic.setCategoryId(productModelPo.getCateThird());
                    } else {
                        throw new RuntimeException("产品型号不存在: " + basic.getProductModelNoBasic());
                    }
                }

                // // 销售渠道编码
                // if (StringUtils.isNotBlank(basic.getSalesChannelCodeBasic())) {
                //     Integer salesChannelId = this.salesChannelMap.get(basic.getSalesChannelCodeBasic());
                //     if (salesChannelId!= null) {
                //         basic.setSalesChannelId(salesChannelId);
                //     } else {
                //         throw new RuntimeException("销售渠道编码不存在: " + basic.getSalesChannelCodeBasic());
                //     }
                // }

                // 渠道类型
                if (StringUtils.isNotBlank(basic.getChannelTypeBasic())) {
                    ChannelType channelType = ChannelType.getByName(basic.getChannelTypeBasic());
                    if (channelType != null) {
                        basic.setChannelType(channelType.getValue());
                    } else {
                        throw new RuntimeException("渠道类型不存在: " + basic.getChannelTypeBasic());
                    }
                }

                // 产品负责人
                if (StringUtils.isNotBlank(basic.getProductManagerCodeBasic())) {
                    Integer productManagerId = this.userMapCode.get(basic.getProductManagerCodeBasic());
                    if (productManagerId!= null) {
                        basic.setProductManagerId(productManagerId);
                    } else {
                        throw new RuntimeException("产品负责人不存在: " + basic.getProductManagerCodeBasic());
                    }
                }
                // 商务负责人
                if (StringUtils.isNotBlank(basic.getBusinessManagerCodeBasic())) {
                    Integer businessManagerId = this.userMapCode.get(basic.getBusinessManagerCodeBasic());
                    if (businessManagerId!= null) {
                        basic.setBusinessManagerId(businessManagerId);
                    } else {
                        throw new RuntimeException("商务负责人不存在: " + basic.getBusinessManagerCodeBasic());
                    }
                }
                // 采购负责人
                if (StringUtils.isNotBlank(basic.getProcurementManagerCodeBasic())) {
                   Integer procurementManagerId = this.userMapCode.get(basic.getProcurementManagerCodeBasic());
                   if (procurementManagerId!= null) {
                       basic.setProcurementManagerId(procurementManagerId);
                   } else {
                       throw new RuntimeException("采购负责人不存在: " + basic.getProcurementManagerCodeBasic());
                   }
                }

                // 计划负责人
                if (StringUtils.isNotBlank(basic.getPlanningManagerCodeBasic())) {
                    Integer planningManagerId = this.userMapCode.get(basic.getPlanningManagerCodeBasic());
                    if (planningManagerId!= null) {
                        basic.setPlanningManagerId(planningManagerId);
                    } else {
                        throw new RuntimeException("计划负责人不存在: " + basic.getPlanningManagerCodeBasic());
                    }
                }
                // 合规负责人
                if (StringUtils.isNotBlank(basic.getComplianceManagerCodeBasic())) {
                    Integer complianceManagerId = this.userMapCode.get(basic.getComplianceManagerCodeBasic());
                    if (complianceManagerId!= null) {
                        basic.setComplianceManagerId(complianceManagerId);
                    } else {
                        throw new RuntimeException("合规负责人不存在: " + basic.getComplianceManagerCodeBasic());
                    }
                }
                // 包材负责人
                if (StringUtils.isNotBlank(basic.getPackingMaterialManagerCodeBasic())) {
                    Integer packingMaterialManagerId = this.userMapCode.get(basic.getPackingMaterialManagerCodeBasic());
                    if (packingMaterialManagerId!= null) {
                        basic.setPackingMaterialManagerId(packingMaterialManagerId);
                    } else {
                        throw new RuntimeException("包装设计师不存在: " + basic.getPackingMaterialManagerCodeBasic());
                    }
                }

                // 产品类型
                if (StringUtils.isNotBlank(basic.getProductTypeBasic())) {
                    String keyValue = pdsCommonSrv.getDictValue(basic.getProductTypeBasic(), this.productTypeItemList);
                    if (keyValue != null) {
                        basic.setProductType(Integer.parseInt(keyValue));
                    } else {
                        throw new RuntimeException("产品类型不存在: " + basic.getProductTypeBasic());
                    }
                }

                // 属性信息
                PdsProductManagerSubmitProsCmd pros = submitBasicCmd.getPros();
                // 产品单位
                if (StringUtils.isNotBlank(pros.getProductUnitPros())) {
                    String keyValue = pdsCommonSrv.getDictValue(pros.getProductUnitPros(), this.productUnitItemList);
                    if (keyValue != null) {
                        pros.setProductUnit(Integer.parseInt(keyValue));
                    } else {
                        throw new RuntimeException("产品单位不存在: " + pros.getProductUnitPros());
                    }
                }

                // 配件属性
                List<PdsProductManagerSubmitFittingValidateCmd> fittingList = submitBasicCmd.getFittingList();
                if (fittingList != null) {
                    for (PdsProductManagerSubmitFittingValidateCmd fitting : fittingList) {
                        // 配件图片
                        if (fitting.getImageIdFittingValidate()!= null) {
                            String fileId = getFileInfoId(fitting.getImageIdFittingValidate(), this.imageMap);
                            if (fileId!= null) {
                                fitting.setImageId(fileId);
                            } else {
                                throw new RuntimeException("配件图片上传失败: " + fitting.getImageIdFittingValidate());
                            }
                        }
                        // 国家地区编码
                        if (fitting.getCountryCodeList()!= null) {
                            // 多个地区用逗号隔开
                            List<Integer> applicableRegionsIdList = new ArrayList<>();
                            for (String countryCode : fitting.getCountryCodeList()) {
                                if (countryCode!= null) {
                                    Integer countryRegionId = this.countryMap.get(countryCode);
                                    if (countryRegionId!= null) {
                                        applicableRegionsIdList.add(countryRegionId);
                                    } else {
                                        throw new RuntimeException("配件国家地区编码不存在: "+countryCode);
                                    }
                                }
                            }
                            fitting.setApplicableRegionsIds(StrUtil.join(",", applicableRegionsIdList));
                        }
                    }
                }

                rightBasicCmdList.add(submitBasicCmd);
            } catch (Exception e) {
                log.error("导入数据校验失败", e);
                PurchaseSkuImportError error = PdsProductManagerPoConverter.INSTANCE.toPurchaseSkuImportError(submitBasicCmd.getImportData());
                error.appendErrorMsg(e.getMessage());
                verify.addImportError(error);
            }
        }
        return rightBasicCmdList;
    }

    /**
     *
     * 解析出原始文件名称
     * @param url 文件路径
     * @return 原始文件名称
     */
    public static String getOriginalFilename(String url) {
       if (StringUtils.isNotBlank(url)) {
           return url.substring(url.lastIndexOf("/") + 1);
       } else {
           return null;
       }
    }

    /**
     *
     * 解析出文件信息
     * @param url 文件路径
     * @param fileMap 文件信息
     * @return 文件id
     */
    public static String getFileInfoId(String url, Map<String,FileInfoResponse> fileMap) {
        FileInfoResponse fileInfoResponse =fileMap.get(getOriginalFilename(url));
        if (fileInfoResponse!= null && StringUtils.isNotBlank(fileInfoResponse.getId())) {
            return fileInfoResponse.getId();
        }
        return null;
    }

}
