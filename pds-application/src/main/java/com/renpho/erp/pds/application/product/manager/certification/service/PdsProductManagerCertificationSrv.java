package com.renpho.erp.pds.application.product.manager.certification.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.ftm.client.response.FileDetailResponse;
import com.renpho.erp.pds.application.common.converter.PdsProductManagerConverter;
import com.renpho.erp.pds.application.common.service.PdsCommonSrv;
import com.renpho.erp.pds.application.common.service.PdsSysTypesSrv;
import com.renpho.erp.pds.application.product.manager.certification.dto.PdsProductManagerUpdateCertificationBasicRequest;
import com.renpho.erp.pds.application.product.manager.certification.dto.PdsProductManagerUpdateCertificationComponentRequest;
import com.renpho.erp.pds.application.product.manager.certification.dto.PdsProductManagerUpdateCertificationMachineRequest;
import com.renpho.erp.pds.domain.common.LockRedisson;
import com.renpho.erp.pds.domain.common.RedisCacheKey;
import com.renpho.erp.pds.domain.common.SysModuleEnum;
import com.renpho.erp.pds.domain.common.SysTypeConstants;
import com.renpho.erp.pds.infrastructure.persistence.dto.PdsProductManagerCertificationBasicResponse;
import com.renpho.erp.pds.infrastructure.persistence.dto.PdsProductManagerCertificationComponentResponse;
import com.renpho.erp.pds.infrastructure.persistence.dto.PdsProductManagerCertificationMachineResponse;
import com.renpho.erp.pds.infrastructure.persistence.mapper.PdsProductManagerCertificationBasicMapper;
import com.renpho.erp.pds.infrastructure.persistence.mapper.PdsProductManagerCertificationComponentMapper;
import com.renpho.erp.pds.infrastructure.persistence.mapper.PdsProductManagerCertificationMachineMapper;
import com.renpho.erp.pds.infrastructure.persistence.po.PdsProductManagerCertificationBasicPo;
import com.renpho.erp.pds.infrastructure.persistence.po.PdsProductManagerCertificationComponentPo;
import com.renpho.erp.pds.infrastructure.persistence.po.PdsProductManagerCertificationMachinePo;
import com.renpho.erp.pds.infrastructure.persistence.service.RedisService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 产品管理-认证资料-服务接口
 *
 * <AUTHOR>
 * @since 2024.11.01
 */
@Slf4j
@Service
public class PdsProductManagerCertificationSrv extends ServiceImpl<PdsProductManagerCertificationBasicMapper, PdsProductManagerCertificationBasicPo> {

    @Resource
    private PdsProductManagerCertificationBasicMapper certificationBasicMapper;

    @Resource
    private PdsProductManagerCertificationComponentMapper certificationComponentMapper;

    @Resource
    private PdsProductManagerCertificationMachineMapper certificationMachineMapper;

    @Resource
    private PdsCommonSrv pdsCommonSrv;

    @Resource
    private PdsSysTypesSrv sysTypesSrv;

    @Resource
    private RedisService redisService;

    /**
     * 根据产品管理主键ID获取认证资料信息
     * @param productManagerId 产品管理主键ID
     * @return 认证资料信息
     */
    public PdsProductManagerCertificationBasicResponse getCertificationById(Integer productManagerId) {
        PdsProductManagerCertificationBasicResponse res = new PdsProductManagerCertificationBasicResponse();

        PdsProductManagerCertificationBasicPo basicPo = certificationBasicMapper.selectByProductManagerId(productManagerId);
        if (basicPo != null) {
            res = PdsProductManagerConverter.INSTANCE.toBasicResponse(basicPo);
        }

        List<PdsProductManagerCertificationMachineResponse> machineList = getMachineList(productManagerId);
        res.setMachineList(machineList);

        List<PdsProductManagerCertificationComponentResponse> componentList = getComponentList(productManagerId);
        res.setComponentList(componentList);

        return res;
    }

    /**
     * 根据产品管理主键ID获取整机信息
     * @param productManagerId 产品管理主键ID
     * @return 整机信息
     */
    private List<PdsProductManagerCertificationMachineResponse> getMachineList(Integer productManagerId){
        List<PdsProductManagerCertificationMachinePo> machinePoList = certificationMachineMapper.selectListByProductManagerId(productManagerId);
        List<PdsProductManagerCertificationMachineResponse> machineList = PdsProductManagerConverter.INSTANCE.toMachineList(machinePoList);
        if (machinePoList != null) {
            for (PdsProductManagerCertificationMachineResponse machineResponse : machineList) {
                String attachmentIds = machineResponse.getAttachmentIds();
                if(StringUtils.isNotBlank(attachmentIds)){
                    List<String> attachmentIdList = JSON.parseArray(attachmentIds, String.class);
                    machineResponse.setAttachmentIdList(attachmentIdList);

                    List<FileDetailResponse> attachmentFileInfoList = pdsCommonSrv.getFileInfoList(attachmentIdList);
                    machineResponse.setAttachmentFileInfoList(attachmentFileInfoList);
                }
            }
        }
        return machineList;
    }

    /**
     * 根据产品管理主键ID获取部件信息
     * @param productManagerId 产品管理主键ID
     * @return 部件信息
     */
    private List<PdsProductManagerCertificationComponentResponse> getComponentList(Integer productManagerId){
        List<PdsProductManagerCertificationComponentPo> componentPoList = certificationComponentMapper.selectListByProductManagerId(productManagerId);
        List<PdsProductManagerCertificationComponentResponse> componentList = PdsProductManagerConverter.INSTANCE.toComponentList(componentPoList);
        if (componentPoList != null) {
            for (PdsProductManagerCertificationComponentResponse componentResponse : componentList) {
                String attachmentIds = componentResponse.getAttachmentIds();
                if(StringUtils.isNotBlank(attachmentIds)){
                    List<String> attachmentIdList = JSON.parseArray(attachmentIds, String.class);
                    componentResponse.setAttachmentIdList(attachmentIdList);

                    List<FileDetailResponse> attachmentFileInfoList = pdsCommonSrv.getFileInfoList(attachmentIdList);
                    componentResponse.setAttachmentFileInfoList(attachmentFileInfoList);
                }
            }
        }
        return componentList;
    }

    /**
     * 验证是否存在
     * @param productManagerId 产品管理主键ID
     * @return 存在返回true
     */
    public boolean isExist(Integer productManagerId) {
        // 验证缓存是否存在
        Long expire=redisService.getExpire(RedisCacheKey.getPdsPmCertKey(productManagerId));
        boolean isExist = false;
        if(expire!=null && expire.intValue()>0){
            isExist = true;
        } else{
            Integer count = certificationBasicMapper.selectCountByProductManagerId(productManagerId);
            if(count>0){
                isExist = true;
            }
        }
        return isExist;
    }

    /**
     * 编辑认证资料信息
     * @param certificationBasic 认证资料信息
     * @return 数据库ID
     */
    @Transactional(rollbackFor = Exception.class)
    @LockRedisson(keys = {"'PDS:PM:CERT:LOCK:' + #certificationBasic.productManagerId"}, expire = 10000, acquireTimeout = 5000)
    public Integer editCertification(PdsProductManagerUpdateCertificationBasicRequest certificationBasic) {
        Integer productManagerId = certificationBasic.getProductManagerId();
        PdsProductManagerCertificationBasicPo basicPo = PdsProductManagerConverter.INSTANCE.toBasicPo(certificationBasic);

        boolean isExist = isExist(productManagerId);
        if(isExist){
            certificationBasicMapper.updateByPrimaryKey(basicPo);
        } else {
            certificationBasicMapper.insert(basicPo);
            redisService.setCacheObject(RedisCacheKey.getPdsPmCertKey(productManagerId), true, 10L, TimeUnit.SECONDS);  // 保证事务成功入库可查
        }

        // 整机: 历史id集合
        List<Integer> machineIdList = certificationMachineMapper.selectIdListByProductManagerId(productManagerId);
        // 需要删除的过期id集合
        List<Integer> machineDeleteList = new ArrayList<>();
        // 现在的id集合
        List<PdsProductManagerUpdateCertificationMachineRequest> machineList = certificationBasic.getMachineList();
        // 过滤出需要删除的
        for(Integer machineId : machineIdList){
            boolean exist = false;
            for(PdsProductManagerUpdateCertificationMachineRequest machineRequest : machineList){
                if(machineId.equals(machineRequest.getId())){
                     exist = true;
                     break;
                }
            }
            if(!exist){
                machineDeleteList.add(machineId);
            }
        }
        // 删除过期的
        for(Integer machineId : machineDeleteList){
            certificationMachineMapper.deleteById(machineId);
        }
        // 处理新增/修改
        if (machineList != null) {
            for (PdsProductManagerUpdateCertificationMachineRequest machineRequest : machineList) {
                PdsProductManagerCertificationMachinePo machinePo = PdsProductManagerConverter.INSTANCE.toMachinePo(machineRequest);
                machinePo.setProductManagerId(productManagerId);
                if(machineRequest.getId()==null){  //新增
                    certificationMachineMapper.insert(machinePo);
                } else{  //修改
                    certificationMachineMapper.updateByPrimaryKey(machinePo);
                }

                // 维护系统类型
                sysTypesSrv.saveType(SysModuleEnum.PM_CERT, SysTypeConstants.PM_CERT_MACHINE_CERT_NAME, machinePo.getCertificationName());
            }
        }

        // 部件: 历史id集合
        List<Integer> componentIdList = certificationComponentMapper.selectIdListByProductManagerId(productManagerId);
        // 需要删除的过期id集合
        List<Integer> componentDeleteList = new ArrayList<>();
        // 现在的id集合
        List<PdsProductManagerUpdateCertificationComponentRequest> componentList = certificationBasic.getComponentList();
        // 过滤出需要删除的
        for(Integer componentId : componentIdList){
            boolean exist = false;
            for(PdsProductManagerUpdateCertificationComponentRequest componentRequest : componentList){
                if(componentId.equals(componentRequest.getId())){
                    exist = true;
                    break;
                }
            }
            if(!exist){
                componentDeleteList.add(componentId);
            }
        }
        // 删除过期的
        for(Integer componentId : componentDeleteList){
            certificationComponentMapper.deleteById(componentId);
        }
        // 处理新增/修改
        if (componentList != null) {
            for (PdsProductManagerUpdateCertificationComponentRequest componentRequest : componentList) {
                PdsProductManagerCertificationComponentPo componentPo = PdsProductManagerConverter.INSTANCE.toComponentPo(componentRequest);
                componentPo.setProductManagerId(productManagerId);
                if(componentRequest.getId()==null){  //新增
                    certificationComponentMapper.insert(componentPo);
                } else{  //修改
                    certificationComponentMapper.updateByPrimaryKey(componentPo);
                }

                // 维护系统类型
                sysTypesSrv.saveType(SysModuleEnum.PM_CERT, SysTypeConstants.PM_CERT_CP_CP_TYPE, componentPo.getComponentType());
                sysTypesSrv.saveType(SysModuleEnum.PM_CERT, SysTypeConstants.PM_CERT_CP_CERT_MODEL, componentPo.getCertificationModel());
                sysTypesSrv.saveType(SysModuleEnum.PM_CERT, SysTypeConstants.PM_CERT_CP_CERT_NAME, componentPo.getCertificationName());
            }
        }

        return productManagerId;
    }

    /**
     * 复制认证资料
     * @param sku sku
     * @return 认证资料
     */
    public PdsProductManagerCertificationBasicResponse copyCertificationBySku(String sku) {
        PdsProductManagerCertificationBasicResponse res = new PdsProductManagerCertificationBasicResponse();

        PdsProductManagerCertificationBasicPo basicPo = certificationBasicMapper.copyCertificationBySku(sku);
        if(basicPo != null){
            res = PdsProductManagerConverter.INSTANCE.toBasicResponse(basicPo);

            Integer productManagerId = basicPo.getProductManagerId();
            List<PdsProductManagerCertificationMachineResponse> machineList = getMachineList(productManagerId);
            res.setMachineList(machineList);

            List<PdsProductManagerCertificationComponentResponse> componentList = getComponentList(productManagerId);
            res.setComponentList(componentList);

            // 去掉ID
            res.setId(null);
            for(PdsProductManagerCertificationMachineResponse machineResponse : machineList){
                machineResponse.setId(null);
                machineResponse.setProductManagerId(null);
            }
            for(PdsProductManagerCertificationComponentResponse componentResponse : componentList){
                componentResponse.setId(null);
                componentResponse.setProductManagerId(null);
            }
        }

        return res;
    }

}
