package com.renpho.erp.pds.application.category.dto;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 品类名称查询-返参.
 *
 * <AUTHOR>
 * @since 2024.11.7
 */
@Data
public class PdsCategoryNameViewResponse {

    /**
     * 品类名称
     */
    private String name;

    /**
     * 语言环境
     */
    private Map<Integer, String> names;

    public void putName(int id, String name) {
        if (names == null) {
            names = new HashMap<>();
        }
        names.put(id, name);
    }

}
