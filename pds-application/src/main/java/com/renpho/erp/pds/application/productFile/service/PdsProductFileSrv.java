package com.renpho.erp.pds.application.productFile.service;


import com.renpho.erp.ftm.client.request.FileDetailNameCmd;
import com.renpho.erp.ftm.client.request.FileRequest;
import com.renpho.erp.ftm.client.response.FileDetailResponse;
import com.renpho.erp.pds.application.common.converter.FileDatailConverter;
import com.renpho.erp.pds.application.common.service.PdsCommonSrv;
import com.renpho.erp.pds.application.common.service.PdsSysTypesSrv;
import com.renpho.erp.pds.domain.common.FtmFileDetailResponse;
import com.renpho.erp.pds.domain.common.PdsSysTypes;
import com.renpho.erp.pds.domain.common.SysModuleEnum;
import com.renpho.erp.pds.domain.common.SysTypeConstants;
import com.renpho.erp.pds.domain.productFile.*;
import com.renpho.erp.pds.infrastructure.exception.DataNotFoundException;
import com.renpho.erp.pds.infrastructure.feignclient.RemoteFileFeign;
import com.renpho.erp.smc.client.dto.OumUserInfoRes;
import com.renpho.karma.dto.Paging;
import jakarta.validation.constraints.NotEmpty;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: Wyatt
 * @Date: 2024-12-02 19:36:31
 * @Description: 产品文件服务
 **/
@Service
@RequiredArgsConstructor
public class PdsProductFileSrv {

    private final PdsProductFileLookup pdsProductFileLookup;
    private final PdsProductFileRepository pdsProductFileRepository;
    private final PdsCommonSrv pdsCommonSrv;
    private final RemoteFileFeign remoteFileFeign;
    private final PdsSysTypesSrv sysTypesSrv;

    private final static Integer DELAY_DELETE_DAY = 90;
    /**
     * 图片最大值
     */
    private static final Integer PICTURE_NUMBER = 50;
    /**
     * 视频最大值
     */
    private static final Integer VIDEO_NUMBER = 1;



    /**
     * 前置校验
     * @param productManagerId
     */
    public PdsProductFile preValidation(Integer productManagerId) {
        return pdsProductFileLookup.getNumberByProductManagerId(productManagerId);
    }

    /**
     * 新增文件
     * @param addProductFileReq
     */
    @Transactional(rollbackFor = Exception.class)
    public List<PdsProductFile> addProductFile(AddProductFileReq addProductFileReq) {
        if (null != addProductFileReq.getPdsSysTypes() && StringUtils.isNotBlank(addProductFileReq.getPdsSysTypes().getTypeValue())){
            PdsSysTypes pdsSysTypes = sysTypesSrv.saveType(SysModuleEnum.PM_FILE, SysTypeConstants.PM_FILE_VISUAL_NAME, addProductFileReq.getPdsSysTypes().getTypeValue());
            addProductFileReq.getPdsSysTypes().setId(pdsSysTypes.getId());
        }
        String pictureLimitData = pdsCommonSrv.getSystemValue("pds_visual_picture_limit");
        String videoLimitData = pdsCommonSrv.getSystemValue("pds_visual_video_limit");
        addProductFileReq.setPictureNumberLimit(StringUtils.isBlank(pictureLimitData) ? PICTURE_NUMBER : Integer.parseInt(pictureLimitData));
        addProductFileReq.setVideoNumberLimit(StringUtils.isBlank(videoLimitData) ? PICTURE_NUMBER : Integer.parseInt(videoLimitData));
        return pdsProductFileRepository.addProductFile(addProductFileReq);
    }

    /**
     * 分页查询
     * @param pdsProductFileQuery
     * @return
     */
    public Paging<PdsProductFile> findPage(PdsProductFileQuery pdsProductFileQuery) {
        Paging<PdsProductFile> dbData = pdsProductFileRepository.findPage(pdsProductFileQuery);
        if (CollectionUtils.isEmpty(dbData.getRecords())) {
            return dbData;
        }
        // 查询用户数据
        List<Integer> userIdList = dbData.getRecords().stream().map(PdsProductFile::getCreateBy).toList();
        Map<Integer, OumUserInfoRes> users = pdsCommonSrv.getUserMap(userIdList);

        // 查询文件数据
        List<String> fileDetailList = dbData.getRecords().stream().map(PdsProductFile::getFileDetailId).toList();
        FileRequest fileRequest = new FileRequest();
        fileRequest.setFileIds(fileDetailList);
        List<FileDetailResponse> fileInfoById = CollectionUtils.isEmpty(fileDetailList)
                ? Collections.emptyList() : remoteFileFeign.getFileInfoById(fileRequest);
        List<FtmFileDetailResponse> fileList = fileInfoById.stream().map(FileDatailConverter.INSTANCE::toCopy).toList();
        Map<String, FtmFileDetailResponse> fileMap = fileList
                .stream().collect(Collectors.toMap(FtmFileDetailResponse::getId, Function.identity(), (o, n) -> o));

        dbData.getRecords().forEach(item -> {
            item.setCreateByName(Optional.ofNullable(users.get(item.getCreateBy())).map(OumUserInfoRes::getName).orElse(StringUtils.EMPTY));
            item.setCreateByCode(Optional.ofNullable(users.get(item.getCreateBy())).map(OumUserInfoRes::getCode).orElse(StringUtils.EMPTY));
            item.setFtmFileDetailResponse(fileMap.get(item.getFileDetailId()));
        });
        return dbData;
    }


    /**
     * 更新名称
     * @param pdsProductFile
     */
    public void updateFileName(PdsProductFile pdsProductFile) {
        PdsProductFile dbPdsProductFile = pdsProductFileLookup.findById(pdsProductFile.getId())
                .orElseThrow(DataNotFoundException::new);
        //更新文件名称
        pdsProductFileRepository.updateFileName(pdsProductFile);
        // 更新ftm
        FileDetailNameCmd fileDetailNameCmd = new FileDetailNameCmd();
        fileDetailNameCmd.setId(dbPdsProductFile.getFileDetailId());
        fileDetailNameCmd.setOriginalFilename(pdsProductFile.getOriginalFilename());
        remoteFileFeign.updateFileName(fileDetailNameCmd);
    }

    /**
     * 删除数据
     * @param pdsProductFile
     */
    public List<Integer> deleteFile(PdsProductFile pdsProductFile) {
        PdsProductFile.PdsProductFileID id = pdsProductFile.getId();
        List<PdsProductFile> dbData = pdsProductFileLookup.getListById(List.of(id.getId()));
        List<Integer> list = pdsProductFileRepository.deleteFile(pdsProductFile);
        // 文件id集合
        List<String> idList = dbData.stream().map(PdsProductFile::getFileDetailId).toList();
        remoteFileFeign.deleteFile(idList, DELAY_DELETE_DAY);
        return list;
    }

    /**
     * 查询上传人员
     * @return
     */
    public List<PdsProductFile> queryUploadUser() {
        List<PdsProductFile> byUserIdList = pdsProductFileLookup.findByUserIdList();
        if (CollectionUtils.isEmpty(byUserIdList)){
            return Collections.emptyList();
        }
        List<Integer> userIdList = byUserIdList.stream().map(PdsProductFile::getCreateBy).toList();
        Map<Integer, OumUserInfoRes> users = pdsCommonSrv.getUserMap(userIdList);
        byUserIdList.forEach(item -> {
            item.setCreateByName(Optional.ofNullable(users.get(item.getCreateBy())).map(OumUserInfoRes::getName).orElse(StringUtils.EMPTY));
            item.setCreateByCode(Optional.ofNullable(users.get(item.getCreateBy())).map(OumUserInfoRes::getCode).orElse(StringUtils.EMPTY));
        });
        return byUserIdList;
    }

    /**
     * 批量删除文件
     * @param idList
     */
    public List<Integer> batchDeleteFile(List<Integer> idList) {
        List<PdsProductFile> dbData = pdsProductFileLookup.getListById(idList);
        List<Integer> list = pdsProductFileRepository.batchDeleteFile(idList);
        // 文件id集合
        List<String> idRequestList = dbData.stream().map(PdsProductFile::getFileDetailId).toList();
        remoteFileFeign.deleteFile(idRequestList, DELAY_DELETE_DAY);
        return list;
    }


    public List<PdsProductFile> getListById(@NotEmpty List<Integer> idList) {
        return pdsProductFileLookup.getListById(idList);
    }
}
