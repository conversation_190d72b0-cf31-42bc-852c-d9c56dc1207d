package com.renpho.erp.pds.application.common.dto;

import com.renpho.erp.smc.client.dto.OumUserInfoRes;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 公共返回信息父类.
 *
 * <AUTHOR>
 * @since 2024.9.25
 */
@Data
public class CommonResponse {

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人账号
     */
    private Integer createBy;

    /**
     * 更新人账号
     */
    private Integer updateBy;

    /**
     * 创建人信息
     */
    private String createUser;

    /**
     * 更新人信息
     */
    private String updateUser;

    /**
     * 补充用户信息
     *
     * @param users 用户信息
     */
    public void initUserInfo(Map<Integer, OumUserInfoRes> users) {
        if (users.containsKey(createBy)) {
            OumUserInfoRes user = users.get(createBy);
            this.createUser = user.getName() + " (" + user.getCode() + ")";
        }
        if (users.containsKey(updateBy)) {
            OumUserInfoRes user = users.get(updateBy);
            this.updateUser = user.getName() + " (" + user.getCode() + ")";
        }
    }

}
