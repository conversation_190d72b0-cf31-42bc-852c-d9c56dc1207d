package com.renpho.erp.pds.application.product.manager.dto;

import lombok.Data;

import java.util.Date;


@Data
public class PdsCertificationEdit {
    /**
     * id主键
     */
    private Integer id;

    /**
     * 关联的产品管理主键ID
     */
    private Integer productManagerId;

    /**
     * 名称
     */
    private String name;

    /**
     * 描述
     */
    private String describe;

    /**
     * 适用国家/地区 ID
     */
    private Integer applicableRegionsId;

    /**
     * 有效时间（开始）
     */
    private Date effectiveTimeStart;

    /**
     * 有效时间（结束）
     */
    private Date effectiveTimeEnd;

    /**
     * 附件
     */
    private String attachmentId;

    /**
     * 备注
     */
    private String remark;
}
