package com.renpho.erp.pds.application.category.dto;

import com.renpho.erp.pds.infrastructure.utils.poi.Excel;
import lombok.Data;

/**
 * 产品型号.
 */
@Data
public class PdsProductModelExport {
    @Excel(name = "产品型号")
    private String modelNo;

    @Excel(name = "品牌")
    private String brandName;

    @Excel(name = "品类")
    private String cateName;

    @Excel(name = "产品经理")
    private String pmUserName;

    @Excel(name = "操作人")
    private String updateByName;

    @Excel(name = "更新时间")
    private String updateTime;
}
