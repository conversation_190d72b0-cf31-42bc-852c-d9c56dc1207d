package com.renpho.erp.pds.application.product.manager.authorize.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 产品授权-运营人员接口.
 *
 * <AUTHOR>
 * @since 2024.10.28
 */
@Data
public class PdsProductAuthorizePersonnelImportExport {

    /**
     * 采购SKU
     */
    @ExcelProperty(value = "excel.purchase_sku", index = 0)
    private String purchaseSku;

    /**
     * 运营人员工号
     */
    @ExcelProperty(value = "excel.personnel_code", index = 1)
    private String personnelCode;
}
