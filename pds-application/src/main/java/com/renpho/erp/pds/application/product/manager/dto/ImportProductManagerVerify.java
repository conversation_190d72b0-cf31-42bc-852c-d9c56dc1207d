package com.renpho.erp.pds.application.product.manager.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 产品管理-导入-认证
 *
 * <AUTHOR>
 * @since 2024.10.28
 */
@Data
public class ImportProductManagerVerify {

    /**
     * 转换之后的通过验证的数据
     */
    private List<PdsProductManagerSubmitRequest> verifyList = new ArrayList<>();

    /**
     * 没有通过验证的数据
     */
    private List<PurchaseSkuImportError> importErrorList = new ArrayList<>();

    /**
     * 正常添加的数据id集合
     */
    List<Integer> insertIds = new ArrayList<>();

    /**
     * 是否认证通过
     *
     * @return true-认证通过，false-认证不通过
     */
    public Boolean verify() {
        return importErrorList.isEmpty();
    }

    public void addInsertId(Integer id) {
        insertIds.add(id);
    }

    public void addImportError(PurchaseSkuImportError error) {
        importErrorList.add(error);
    }

    public void addVerifyList(PdsProductManagerSubmitRequest request) {
        this.verifyList.add(request);
    }
}
