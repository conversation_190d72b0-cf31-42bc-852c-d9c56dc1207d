package com.renpho.erp.pds.application.product.manager.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fhs.trans.service.impl.TransService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.renpho.erp.data.trans.dict.Dict;
import com.renpho.erp.ftm.client.request.FileRequest;
import com.renpho.erp.ftm.client.response.FileDetailResponse;
import com.renpho.erp.mdm.client.saleschannel.RemoteSalesChannelService;
import com.renpho.erp.mdm.client.saleschannel.command.SalesChannelIdsQuery;
import com.renpho.erp.mdm.client.saleschannel.vo.SalesChannelVo;
import com.renpho.erp.oplog.log.OpLog;
import com.renpho.erp.oplog.log.enums.BusinessType;
import com.renpho.erp.pds.application.brand.service.PdsBrandSrv;
import com.renpho.erp.pds.application.category.service.PdsCategorySrv;
import com.renpho.erp.pds.application.color.service.PdsColorSrv;
import com.renpho.erp.pds.application.common.converter.PdsProductManagerConverter;
import com.renpho.erp.pds.application.common.converter.PdsProductManagerPoConverter;
import com.renpho.erp.pds.application.common.oplog.ProductManagerImportSnapt;
import com.renpho.erp.pds.application.common.oplog.module.ProductManagerModules;
import com.renpho.erp.pds.application.common.service.PdsCommonSrv;
import com.renpho.erp.pds.application.country.service.PdsCountryRegionSrv;
import com.renpho.erp.pds.application.mq.PdsSyncBmProducer;
import com.renpho.erp.pds.application.product.manager.authorize.service.PdsProductManagerAuthorizeSrv;
import com.renpho.erp.pds.application.product.manager.certification.service.PdsProductManagerCertificationSrv;
import com.renpho.erp.pds.application.product.manager.dto.*;
import com.renpho.erp.pds.application.product.manager.dto.PdsProductManagerSkuQueryViewResponse;
import com.renpho.erp.pds.application.product.manager.dto.cmd.*;
import com.renpho.erp.pds.application.product.manager.dto.model.BuildSubmitCache;
import com.renpho.erp.pds.application.product.model.dto.PdsProductManagerQueryDetailViewRequest;
import com.renpho.erp.pds.application.product.model.dto.PdsProductManagerQueryViewRequest;
import com.renpho.erp.pds.application.product.model.service.PdsProductModelSrv;
import com.renpho.erp.pds.application.product.model.service.oplog.PdsSystemModule;
import com.renpho.erp.pds.application.utils.ValidationUtil;
import com.renpho.erp.pds.domain.category.LanguageCategory;
import com.renpho.erp.pds.domain.color.LanguageColor;
import com.renpho.erp.pds.domain.common.*;
import com.renpho.erp.pds.domain.country.LanguageCountryRegion;
import com.renpho.erp.pds.domain.product.manager.authorize.PdsProductManagerSubmitBasicCmd;
import com.renpho.erp.pds.domain.product.model.PdsProductModel;
import com.renpho.erp.pds.domain.product.srm.request.ProductBusinessManagerRequest;
import com.renpho.erp.pds.domain.product.srm.request.ProductManagerBasicRequest;
import com.renpho.erp.pds.infrastructure.exception.PdsErrorCode;
import com.renpho.erp.pds.infrastructure.feignclient.RemoteFileFeign;
import com.renpho.erp.pds.infrastructure.feignclient.RemoteUserDetailsFeign;
import com.renpho.erp.pds.infrastructure.feignclient.bpm.BpmProcessClient;
import com.renpho.erp.pds.infrastructure.feignclient.srm.RemoteProductBusinessManagerFeign;
import com.renpho.erp.pds.infrastructure.form.ProductAuditForm;
import com.renpho.erp.pds.infrastructure.persistence.dto.*;
import com.renpho.erp.pds.infrastructure.persistence.mapper.*;
import com.renpho.erp.pds.infrastructure.persistence.po.*;
import com.renpho.erp.pds.infrastructure.persistence.service.RedisService;
import com.renpho.erp.pds.infrastructure.utils.ImageUrlValidator;
import com.renpho.erp.pds.infrastructure.utils.PagingUtils;
import com.renpho.erp.pds.infrastructure.utils.StackUtils;
import com.renpho.erp.security.util.SecurityUtils;
import com.renpho.erp.smc.client.dto.OumUserInfoRes;
import com.renpho.erp.smc.client.dto.QueryUserListReq;
import com.renpho.erp.srm.client.cmd.ProductBusinessManagerQuery;
import com.renpho.erp.srm.client.vo.ProductBusinessManagerResponse;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import com.renpho.karma.dto.RetOps;
import com.renpho.karma.exception.ErrorCodeException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 产品管理-服务接口
 *
 * <AUTHOR>
 * @since 2024.11.01
 */
@Slf4j
@Service
public class PdsProductManagerSrv {
    @Resource
    private PdsProductManagerBasicMapper basicMapper;
    @Resource
    private PdsProductManagerCertificationMapper certificationMapper;
    @Resource
    private PdsProductManagerFittingMapper fittingMapper;
    @Resource
    private PdsProductManagerLogisticsMapper logisticsMapper;
    @Resource
    private PdsProductManagerPackagingMaterialsMapper packagingMaterialsMapper;
    @Resource
    private PdsProductManagerProsBoxMapper prosBoxMapper;
    @Resource
    private PdsProductManagerProsMapper prosMapper;
    @Resource
    private RemoteUserDetailsFeign remoteUserDetailsFeign;
    @Resource
    private PdsBrandMapper brandMapper;
    @Resource
    private PdsCountryRegionMapper countryRegionMapper;
    @Resource
    private PdsBrandSrv brandSrv;
    @Resource
    private PdsCategorySrv categorySrv;
    @Resource
    private PdsProductModelSrv productModelSrv;
    @Resource
    private PdsColorSrv colorSrv;
    @Resource
    private PdsCountryRegionSrv countryRegionSrv;
    @Autowired
    private TransService transService;

    @Resource
    private RemoteFileFeign remoteFileFeign;
    @Resource
    private PdsProductModelSrv pdsProductModelSrv;
    @Resource
    private RemoteSalesChannelService remoteSalesChannelService;
    @Resource
    private PdsCommonSrv pdsCommonSrv;

    @Autowired
    private PdsProductManagerAuthorizeSrv pdsProductManagerAuthorizeSrv;
    @Autowired
    private PdsCountryRegionSrv pdsCountryRegionSrv;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Resource
    private PdsProductManagerCertificationSrv certificationSrv;

    @Resource
    private RedisService redisService;

    @Resource
    private PdsSyncBmProducer pdsSyncBmProducer;
    @Autowired
    private BpmProcessClient bpmProcessClient;

    @Resource
    private RemoteProductBusinessManagerFeign remoteProductBusinessManagerFeign;

    /**
     * 角色标签-合规角色标签
     */
    @Value("${permission.roleLabel.compliance}")
    private String roleLabelCompliance;

    /**
     * 角色标签-合规附件查看标签
     */
    @Value("${permission.roleLabel.compliance_attachment}")
    private String roleLabelComplianceAttachment;



    /**
     * 查询基础信息集合
     * <AUTHOR>
     * @Date 12:15 2025/3/20
     * @Param [queryDto]
     * @return java.util.List<com.renpho.erp.pds.infrastructure.persistence.dto.PdsProductManagerResponse>
     **/
    public List<PdsProductManagerResponse> listBasic(PdsProductManagerQueryDto queryDto){
        LambdaQueryWrapper<PdsProductManagerBasicPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(queryDto.getReviewStatus() != null, PdsProductManagerBasicPo::getReviewStatus, queryDto.getReviewStatus());
        List<PdsProductManagerBasicPo> list = basicMapper.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            return BeanUtil.copyToList(list, PdsProductManagerResponse.class);
        }
        return List.of();
    }

    /**
     * 产品管理-分页查询
     *
     * @param queryDto     查询条件
     * @param languageEnum 语言
     * @return 分页数据
     */
    public Paging<PdsProductManagerResponse> page(PdsProductManagerQueryDto queryDto, LanguageEnum languageEnum) {
        setTime(queryDto);
        int pageNum = queryDto.getPageIndex();
        int pageSize = queryDto.getPageSize();
        //查询供应商产品信息
        if (StringUtils.isNotBlank(queryDto.getSupplierKeyword())) {
            ProductBusinessManagerQuery productBusinessManagerQuery = new ProductBusinessManagerQuery();
            productBusinessManagerQuery.setKeyword(queryDto.getSupplierKeyword());
            List<ProductBusinessManagerResponse> productBusinessList = remoteProductBusinessManagerFeign.list(productBusinessManagerQuery);
            if (CollectionUtil.isNotEmpty(productBusinessList)) {
                Set<Integer> prdocutIdSet = productBusinessList.stream().map(ProductBusinessManagerResponse::getProductManagerId).collect(Collectors.toSet());
                queryDto.setIdList(prdocutIdSet.stream().toList());
            } else {
                return Paging.of(pageSize, pageNum);
            }
        }
        // 使用 PageHelper 进行分页
        PageHelper.startPage(pageNum, pageSize);
        List<PdsProductManagerResponse> iPage = basicMapper.selectProductManagerList(queryDto);

        Paging<PdsProductManagerResponse> pmPage = PagingUtils.toPaging(new PageInfo<>(iPage));
        List<PdsProductManagerResponse> recordList = pmPage.getRecords();
        if (CollectionUtil.isEmpty(recordList)) {
            return pmPage;
        }

        // 数字字典
        transService.transBatch(recordList);

        Set<Integer> productManagerIdSet = new HashSet<>();
        Set<Integer> userIdSet = new HashSet<>();
        Set<Integer> colorIdSet = new HashSet<>();
        Set<Integer> brandIdSet = new HashSet<>();
        Set<Integer> countryRegionIdSet = new HashSet<>();
        for (PdsProductManagerResponse productManager : recordList) {
            productManagerIdSet.add(productManager.getId());
            userIdSet.add(productManager.getProductManagerId());
            userIdSet.add(productManager.getProcurementManagerId());
            colorIdSet.add(productManager.getColorId());
            brandIdSet.add(productManager.getBrandId());
            countryRegionIdSet.add(productManager.getCountryRegionId());
        }

        List<OumUserInfoRes> userListInfo = getUserListInfo(userIdSet);
        List<PdsProductManagerProsPo> prosPoList = prosPoList(productManagerIdSet);
        List<PdsProductManagerProsBoxPo> prosBoxPoList = prosBoxPoList(prosPoList);
        List<PdsBrandPo> brandPoList = brandPoList(brandIdSet);
        List<PdsColorPo> colorPoList = colorPoList(colorIdSet);
        List<PdsCountryRegionPo> countryRegionPoList = countryRegionPoList(countryRegionIdSet);

        String language = languageEnum.equals(LanguageEnum.China) ? "zh-CN" : "en-US";
        for (PdsProductManagerResponse productManager : recordList) {
            if (StringUtils.isNotBlank(productManager.getProductCoverImageId())) {
                FileDetailResponse fileDetailResponse = pdsCommonSrv.getFileInfo(productManager.getProductCoverImageId());
                if (fileDetailResponse != null) {
                    productManager.setProductCoverImageFileInfo(fileDetailResponse);
                }
            }

            setColorName(colorPoList, productManager, language);
            setCountryRegionName(countryRegionPoList, productManager, language);

            StringBuilder treeName = new StringBuilder();
            categorySrv.getCategoryTreeName(productManager.getCategoryId(), languageEnum, treeName);
            productManager.setCategoryName(treeName.toString());

            if (StringUtils.isNotBlank(productManager.getChineseName()) && StringUtils.isNotBlank(productManager.getEnglishName())) {
                if (languageEnum == LanguageEnum.China) {
                    productManager.setProductName(productManager.getChineseName());
                } else {
                    productManager.setProductName(productManager.getEnglishName());
                }
            }

            setBrand(brandPoList, productManager);
            setName(userListInfo, productManager);
            setProsAndProsBox(prosPoList, prosBoxPoList, productManager);
        }
        return pmPage;
    }

    private void setTime(PdsProductManagerQueryDto queryDto) {
        if (StringUtils.isNotBlank(queryDto.getCreateTimeStart())) {
            queryDto.setCreateTimeStart(queryDto.getCreateTimeStart() + " 00:00:00");
        }
        if (StringUtils.isNotBlank(queryDto.getCreateTimeEnd())) {
            queryDto.setCreateTimeEnd(queryDto.getCreateTimeEnd() + " 23:59:59");
        }

        if (StringUtils.isNotBlank(queryDto.getUpdateTimeStart())) {
            queryDto.setUpdateTimeStart(queryDto.getUpdateTimeStart() + " 00:00:00");
        }
        if (StringUtils.isNotBlank(queryDto.getUpdateTimeEnd())) {
            queryDto.setUpdateTimeEnd(queryDto.getUpdateTimeEnd() + " 23:59:59");
        }

        if (StringUtils.isNotBlank(queryDto.getReviewTimeStart())) {
            queryDto.setReviewTimeStart(queryDto.getReviewTimeStart() + " 00:00:00");
        }
        if (StringUtils.isNotBlank(queryDto.getReviewTimeEnd())) {
            queryDto.setReviewTimeEnd(queryDto.getReviewTimeEnd() + " 23:59:59");
        }

        if (StringUtils.isNotBlank(queryDto.getSubmitTimeStart())) {
            queryDto.setSubmitTimeStart(queryDto.getSubmitTimeStart() + " 00:00:00");
        }
        if (StringUtils.isNotBlank(queryDto.getSubmitTimeEnd())) {
            queryDto.setSubmitTimeEnd(queryDto.getSubmitTimeEnd() + " 23:59:59");
        }
    }

    public Map<Integer, Integer> countByStatus() {
        Map<Integer, Integer> map = new HashMap<>();

        PdsProductManagerQueryDto queryDto = new PdsProductManagerQueryDto();
        // 使用 PageHelper 进行分页
        int pageNum = 1;
        int pageSize = 1;

        //查询草稿状态数量
        Integer draftNum = 0;
        queryDto.setReviewStatus(PdsManagerReviewStatus.DRAFT.getCode());
        PageHelper.startPage(pageNum, pageSize);
        List<PdsProductManagerResponse> draftList = basicMapper.selectProductManagerList(queryDto);
        if (CollectionUtil.isNotEmpty(draftList)) {
            draftNum = PagingUtils.toPaging(new PageInfo<>(draftList)).getTotalCount();
        }
        map.put(PdsManagerReviewStatus.DRAFT.getCode(), draftNum);

        //审核中
        Integer pendingNum = 0;
        queryDto.setReviewStatus(PdsManagerReviewStatus.PENDING_REVIEW.getCode());
        PageHelper.startPage(pageNum, pageSize);
        List<PdsProductManagerResponse> pendingList = basicMapper.selectProductManagerList(queryDto);
        if (CollectionUtil.isNotEmpty(pendingList)) {
            pendingNum = PagingUtils.toPaging(new PageInfo<>(pendingList)).getTotalCount();
        }
        map.put(PdsManagerReviewStatus.PENDING_REVIEW.getCode(), pendingNum);

        //审核通过
        Integer approvedNum = 0;
        queryDto.setReviewStatus(PdsManagerReviewStatus.APPROVED.getCode());
        PageHelper.startPage(pageNum, pageSize);
        List<PdsProductManagerResponse> approvedList = basicMapper.selectProductManagerList(queryDto);
        if (CollectionUtil.isNotEmpty(approvedList)) {
            approvedNum = PagingUtils.toPaging(new PageInfo<>(approvedList)).getTotalCount();
        }
        map.put(PdsManagerReviewStatus.APPROVED.getCode(), approvedNum);

        //审核不通过
        Integer rejectNum = 0;
        queryDto.setReviewStatus(PdsManagerReviewStatus.REJECTED.getCode());
        PageHelper.startPage(pageNum, pageSize);
        List<PdsProductManagerResponse> rejectList = basicMapper.selectProductManagerList(queryDto);
        if (CollectionUtil.isNotEmpty(rejectList)) {
            rejectNum = PagingUtils.toPaging(new PageInfo<>(rejectList)).getTotalCount();
        }
        map.put(PdsManagerReviewStatus.REJECTED.getCode(), rejectNum);

        map.put(-1, draftNum + pendingNum + approvedNum + rejectNum);
        return map;
    }



    private List<PdsProductManagerProsPo> prosPoList(Set<Integer> productManagerIdSet) {
        if (productManagerIdSet.isEmpty()) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<PdsProductManagerProsPo> prosQueryWrapper = Wrappers.<PdsProductManagerProsPo>lambdaQuery()
                .in(PdsProductManagerProsPo::getProductManagerId, productManagerIdSet);
        return prosMapper.selectList(prosQueryWrapper);
    }

    public List<PdsProductManagerProsBoxPo> prosBoxPoList(List<PdsProductManagerProsPo> prosPoList) {
        if (prosPoList.isEmpty()) {
            return new ArrayList<>();
        }
        List<Integer> productManagerProsIdList = prosPoList.stream().map(x -> x.getId()).toList();
        LambdaQueryWrapper<PdsProductManagerProsBoxPo> prosBoxQueryWrapper = Wrappers.<PdsProductManagerProsBoxPo>lambdaQuery()
                .in(PdsProductManagerProsBoxPo::getProductManagerProsId, productManagerProsIdList);
        return prosBoxMapper.selectList(prosBoxQueryWrapper);
    }

    private List<PdsBrandPo> brandPoList(Set<Integer> brandIdSet) {
        if (brandIdSet.isEmpty()) {
            return new ArrayList<>();
        }
        return brandMapper.selectBatchIds(new ArrayList<>(brandIdSet));
    }

    private List<PdsColorPo> colorPoList(Set<Integer> colorIdSet) {
        if (colorIdSet.isEmpty()) {
            return new ArrayList<>();
        }
        return colorSrv.getPdsColorDetails(new ArrayList<>(colorIdSet));
    }

    private List<PdsCountryRegionPo> countryRegionPoList(Set<Integer> countryRegionIdSet) {
        if (countryRegionIdSet.isEmpty()) {
            return new ArrayList<>();
        }
        return countryRegionSrv.getPdsCountryRegionDetails(new ArrayList<>(countryRegionIdSet));
    }

    private List<OumUserInfoRes> getUserListInfo(Set<Integer> userIdSet) {
        QueryUserListReq req = new QueryUserListReq();
        req.setUserIds(new ArrayList<>(userIdSet));

        log.info("请求【获取用户列表】接口，req：{}", JSON.toJSON(req));
        R<List<OumUserInfoRes>> resp = remoteUserDetailsFeign.getUserListInfo(req);
        log.info("请求【获取用户列表】接口，resp：{}", JSON.toJSON(resp));
        if (!resp.isSuccess()) {
            throw new ErrorCodeException(PdsErrorCode.PRODUCTMODEL_GETUSERLIST_FAIL);
        }
        List<OumUserInfoRes> oumUserInfoResList = resp.getData();
        return oumUserInfoResList;
    }

    private void setColorName(List<PdsColorPo> colorPoList, PdsProductManagerResponse productManager, String language) {
        for (PdsColorPo colorPo : colorPoList) {
            if (colorPo.getId().equals(productManager.getColorId())) {
                List<LanguageColor> languageColorList = colorSrv.getLanguages(colorPo.getId());
                for (LanguageColor languageColor : languageColorList) {
                    if (languageColor.getLanguage().equals(language)) {
                        productManager.setColorName(languageColor.getName());
                    }
                }
            }
        }
    }

    private void setCountryRegionName(List<PdsCountryRegionPo> countryRegionPoList, PdsProductManagerResponse productManager, String language) {
        for (PdsCountryRegionPo countryRegion : countryRegionPoList) {
            if (countryRegion.getId().equals(productManager.getCountryRegionId())) {
                List<LanguageCountryRegion> languageCountryRegionList = countryRegionSrv.getLanguages(countryRegion.getId());
                for (LanguageCountryRegion languageCountryRegion : languageCountryRegionList) {
                    if (languageCountryRegion.getLanguage().equals(language)) {
                        productManager.setCountryRegionName(languageCountryRegion.getName());
                    }
                }
            }
        }
    }

    private void setBrand(List<PdsBrandPo> brandPoList, PdsProductManagerResponse productManager) {
        for (PdsBrandPo brand : brandPoList) {
            if (brand.getId().equals(productManager.getBrandId())) {
                productManager.setBrandName(brand.getBrand());
            }
        }
    }

    private void setName(List<OumUserInfoRes> userListInfo, PdsProductManagerResponse productManager) {
        for (OumUserInfoRes oumUserInfoRes : userListInfo) {
            if (oumUserInfoRes.getId().equals(productManager.getProductManagerId())) {
                productManager.setProductManagerName(pdsCommonSrv.getUserDesc(oumUserInfoRes));
            }
            if (oumUserInfoRes.getId().equals(productManager.getProcurementManagerId())) {
                productManager.setProcurementManagerName(pdsCommonSrv.getUserDesc(oumUserInfoRes));
            }
        }
    }

    private void setProsAndProsBox(List<PdsProductManagerProsPo> prosPoList, List<PdsProductManagerProsBoxPo> prosBoxPoList, PdsProductManagerResponse productManager) {
        for (PdsProductManagerProsPo prosPo : prosPoList) {
            if (prosPo.getProductManagerId().equals(productManager.getId())) {
                productManager.setPackagingLengthMetric(prosPo.getPackagingLengthMetric());
                productManager.setPackagingWidthMetric(prosPo.getPackagingWidthMetric());
                productManager.setPackagingHeightMetric(prosPo.getPackagingHeightMetric());
                productManager.setGrossWeightMetric(prosPo.getGrossWeightMetric());
                productManager.setPackagingLengthImperial(prosPo.getPackagingLengthImperial());
                productManager.setPackagingWidthImperial(prosPo.getPackagingWidthImperial());
                productManager.setPackagingHeightImperial(prosPo.getPackagingHeightImperial());
                productManager.setGrossWeightImperial(prosPo.getGrossWeightImperial());
                for (PdsProductManagerProsBoxPo prosBoxPo : prosBoxPoList) {
                    if (prosPo.getId().equals(prosBoxPo.getProductManagerProsId())
                            && prosBoxPo.getActive().equals(1)) {
                        productManager.setBoxLengthMetric(prosBoxPo.getBoxLengthMetric());
                        productManager.setBoxWidthMetric(prosBoxPo.getBoxWidthMetric());
                        productManager.setBoxHeightMetric(prosBoxPo.getBoxHeightMetric());
                        productManager.setGrossWeightPerBoxMetric(prosBoxPo.getGrossWeightPerBoxMetric());
                        productManager.setBoxLengthImperial(prosBoxPo.getBoxLengthImperial());
                        productManager.setBoxWidthImperial(prosBoxPo.getBoxWidthImperial());
                        productManager.setBoxHeightImperial(prosBoxPo.getBoxHeightImperial());
                        productManager.setGrossWeightPerBoxImperial(prosBoxPo.getGrossWeightPerBoxImperial());
                    }
                }
            }
        }
    }

    /**
     * 更新产品管理数据
     *
     * @param productManagerUpdate 更新的产品管理信息
     * @param status               编辑类型: 0,草稿; 1,提交(默认)
     * @param actionType           操作类型: 0,新增; 1,编辑
     * @return 更新的行数
     */
    @Transactional(rollbackFor = Exception.class)
    public synchronized Integer updateByPrimaryKey(PdsProductManagerUpdateRequest productManagerUpdate, Integer status, Integer actionType) {
        PdsProductManagerBasicPo basicHisPo = basicMapper.selectById(productManagerUpdate.getBasic().getId());
        if (basicHisPo == null) {
            throw new ErrorCodeException(PdsErrorCode.PRODUCT_MANAGER_ID_NOT_EXIST);
        }

        PdsManagerReviewStatus statusHis = PdsManagerReviewStatus.getEnum(basicHisPo.getReviewStatus());
        if (statusHis == null) {
            throw new ErrorCodeException(PdsErrorCode.PRODUCT_MANAGER_REVIEW_STATUS_NOT_EXIST);
        }

        if (CommonConstants.ACTION_TYPE_SUBMIT.equals(status)) {
            // 检查是否可以提交: 待审核不能继续提交(更新)
            if (statusHis == PdsManagerReviewStatus.PENDING_REVIEW) {
                throw new ErrorCodeException(PdsErrorCode.PRODUCT_MANAGER_SUBMIT_ONLY_DRAFT);
            }

            // 检查是否可以编辑: 草稿/审核通过/审核不通过/撤回 才能编辑
            if (!statusHis.canEdit()) {
                throw new ErrorCodeException(PdsErrorCode.PRODUCT_MANAGER_EDIT_ONLY_APPROVED);
            }
        }

        // 草稿类型只能编辑草稿状态的数据
        if (CommonConstants.ACTION_TYPE_DRAFT.equals(status) && statusHis != PdsManagerReviewStatus.DRAFT) {
            throw new ErrorCodeException(PdsErrorCode.PRODUCT_MANAGER_DRAFT_ONLY_DRAFT);
        }

        //基础信息
        PdsProductManagerBasicPo basicPo = PdsProductManagerPoConverter.INSTANCE.basicRequestToBasicPo(productManagerUpdate.getBasic());
        // 检查是否有重复的采购SKU
        LambdaQueryWrapper<PdsProductManagerBasicPo> queryWrapper = Wrappers.<PdsProductManagerBasicPo>lambdaQuery()
                .eq(PdsProductManagerBasicPo::getDeleted, 0)
                .eq(PdsProductManagerBasicPo::getPurchaseSku, basicPo.getPurchaseSku())
                .ne(PdsProductManagerBasicPo::getId, basicPo.getId());  // 不包含自己;
        List<PdsProductManagerBasicPo> localBasicList = basicMapper.selectList(queryWrapper);
        if (!localBasicList.isEmpty()) {
            throw new ErrorCodeException(PdsErrorCode.PRODUCTMANAGER_SUBMIT_SKU_REPEAT);
        }
        // 产品描述/卖点
        if(productManagerUpdate.getBasic().getProductDescriptionList()!=null){
            basicPo.setProductDescription(JSON.toJSONString(productManagerUpdate.getBasic().getProductDescriptionList()));
        }
        if(productManagerUpdate.getBasic().getProductSellingPointsList()!=null){
            basicPo.setProductSellingPoints(JSON.toJSONString(productManagerUpdate.getBasic().getProductSellingPointsList()));
        }
        if (productManagerUpdate.getBasic().getAttachmentList() != null) {
            basicPo.setAttachment(JSON.toJSONString(productManagerUpdate.getBasic().getAttachmentList()));
        }

        basicMapper.updateById(basicPo);

        //关联的产品管理主键ID
        Integer productManagerId = basicPo.getId();

        //属性信息
        Integer productManagerProsId = prosMapper.selectIdByProductManagerId(productManagerId);  // 产品管理-属性信息-主键ID，避免修改其它数据
        PdsProductManagerUpdateProsRequest pros = productManagerUpdate.getPros();
        PdsProductManagerProsPo prosPo = PdsProductManagerPoConverter.INSTANCE.prosRequestToProsPo(pros);
        prosPo.setId(productManagerProsId);
        prosPo.setProductManagerId(productManagerId);
        prosPo.convertMetricToImperial();
        prosMapper.updateByPrimaryKey(prosPo);

        //属性信息-外箱规格
        List<PdsProductManagerUpdateProsBoxRequest> boxList = pros.getBoxList();
        List<PdsProductManagerProsBoxPo> boxListHis = prosBoxMapper.selectList(Wrappers.<PdsProductManagerProsBoxPo>lambdaQuery()
                .eq(PdsProductManagerProsBoxPo::getProductManagerProsId, productManagerProsId));
        List<Integer> boxListDelete = new ArrayList<>();
        if (boxList != null && !boxList.isEmpty()) {
            for (PdsProductManagerProsBoxPo boxHis : boxListHis) {
                boolean exist = false;
                for (PdsProductManagerUpdateProsBoxRequest box : boxList) {
                    if (boxHis.getId().equals(box.getId())) {
                        exist = true;
                        break;
                    }
                }
                if (!exist) {
                    boxListDelete.add(boxHis.getId());
                }
            }
            for (Integer boxId : boxListDelete) {
                prosBoxMapper.deleteById(boxId);
            }

            for (PdsProductManagerUpdateProsBoxRequest box : boxList) {
                PdsProductManagerProsBoxPo prosBoxPo = PdsProductManagerPoConverter.INSTANCE.prosBoxRequestToProsBoxPo(box);
                prosBoxPo.convertMetricToImperial();
                prosBoxPo.setProductManagerProsId(productManagerProsId);
                if (box.getId() == null) {
                    prosBoxMapper.insert(prosBoxPo);
                } else {
                    prosBoxMapper.updateByPrimaryKey(prosBoxPo);
                }
            }
        } else {
            for (PdsProductManagerProsBoxPo boxHis : boxListHis) {
                prosBoxMapper.deleteById(boxHis.getId());
            }
        }

        //物流信息
        Integer productManagerLogisticsId = logisticsMapper.selectIdByProductManagerId(productManagerId);  // 产品管理-物流信息-主键ID，避免修改其它数据
        PdsProductManagerUpdateLogisticsRequest logistics = productManagerUpdate.getLogistics();
        PdsProductManagerLogisticsPo logisticsPo = PdsProductManagerPoConverter.INSTANCE.logisticsRequestToLogisticsPo(logistics);
        logisticsPo.setId(productManagerLogisticsId);
        logisticsPo.setProductManagerId(productManagerId);
        if (logistics.getOtherInformationList() != null) {
            logisticsPo.setOtherInformation(JSON.toJSONString(logistics.getOtherInformationList()));
        }
        logisticsMapper.updateByPrimaryKey(logisticsPo);

        //配件信息
        List<PdsProductManagerUpdateFittingRequest> fittingList = productManagerUpdate.getFittingList();
        List<PdsProductManagerFittingPo> fittingListHis = fittingMapper.selectList(Wrappers.<PdsProductManagerFittingPo>lambdaQuery()
                .eq(PdsProductManagerFittingPo::getProductManagerId, productManagerId));
        List<Integer> fittingListDelete = new ArrayList<>();
        if (fittingList != null && !fittingList.isEmpty()) {
            for (PdsProductManagerFittingPo fittingHis : fittingListHis) {
                boolean exist = false;
                for (PdsProductManagerUpdateFittingRequest fitting : fittingList) {
                    if (fittingHis.getId().equals(fitting.getId())) {
                        exist = true;
                        break;
                    }
                }
                if (!exist) {
                    fittingListDelete.add(fittingHis.getId());
                }
            }
            for (Integer fittingId : fittingListDelete) {
                fittingMapper.deleteById(fittingId);
            }

            for (PdsProductManagerUpdateFittingRequest fitting : fittingList) {
                PdsProductManagerFittingPo fittingPo = PdsProductManagerPoConverter.INSTANCE.fittingRequestToFittingPo(fitting);
                fittingPo.setProductManagerId(productManagerId);
                if (fittingPo.getId() == null) {
                    fittingMapper.insert(fittingPo);
                } else {
                    fittingMapper.updateByPrimaryKey(fittingPo);
                }
            }
        } else {
            for (PdsProductManagerFittingPo fittingHis : fittingListHis) {
                fittingMapper.deleteById(fittingHis.getId());
            }
        }

        // 提交需要推送到bpm
        if (Objects.equals(CommonConstants.ACTION_TYPE_SUBMIT, status)) {
            String bpmInstanceId = bpmProcessClient.startProcessInstance(new ProductAuditForm(), String.valueOf(productManagerId));
            PdsProductManagerBasicPo pdsProductManagerBasicPo = new PdsProductManagerBasicPo();
            pdsProductManagerBasicPo.setId(productManagerId);
            pdsProductManagerBasicPo.setBpmProcessInstanceId(bpmInstanceId);
            basicMapper.updateById(pdsProductManagerBasicPo);
        }

        return CommonConstants.SUCCESS;
    }


    /**
     * 保存产品管理信息-主体
     *
     * @param submit        提交的产品管理信息
     * @param currentUserId 当前用户ID
     * @return 产品管理主键ID
     */
    @Transactional(rollbackFor = Exception.class)
    public synchronized Integer submit(PdsProductManagerSubmitRequest submit, Integer currentUserId) {
        // 获取代理对象
        LocalDateTime nowDate = LocalDateTime.now();
        Integer productManagerId = submitBasic(submit.getBasic(), nowDate, currentUserId);
        submitPros(submit.getPros(), productManagerId);
        submitLogistics(submit.getLogistics(), productManagerId);
        submitFitting(submit.getFittingList(), productManagerId);

        // 如果是待提交审核需要推送到bpm
        if (Objects.equals(PdsManagerReviewStatus.PENDING_REVIEW.getCode(), submit.getBasic().getReviewStatus())) {
            String bpmInstanceId = bpmProcessClient.startProcessInstance(new ProductAuditForm(), String.valueOf(productManagerId));
            PdsProductManagerBasicPo pdsProductManagerBasicPo = new PdsProductManagerBasicPo();
            pdsProductManagerBasicPo.setId(productManagerId);
            pdsProductManagerBasicPo.setBpmProcessInstanceId(bpmInstanceId);
            basicMapper.updateById(pdsProductManagerBasicPo);
        }
        return productManagerId;
    }

    /**
     * 保存产品管理信息-基础信息
     *
     * @param basic         提交的产品管理信息
     * @param nowDate       当前时间
     * @param currentUserId 当前用户ID
     * @return 产品管理主键ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer submitBasic(PdsProductManagerSubmitBasicRequest basic, LocalDateTime nowDate, Integer currentUserId) {
        LambdaQueryWrapper<PdsProductManagerBasicPo> queryWrapper = Wrappers.<PdsProductManagerBasicPo>lambdaQuery()
                .eq(PdsProductManagerBasicPo::getDeleted, 0)
                .eq(PdsProductManagerBasicPo::getPurchaseSku, basic.getPurchaseSku());
        List<PdsProductManagerBasicPo> localBasicList = basicMapper.selectList(queryWrapper);
        if (!localBasicList.isEmpty()) {
            throw new ErrorCodeException(PdsErrorCode.PRODUCTMANAGER_SUBMIT_SKU_REPEAT);
        }

        PdsProductManagerBasicPo basicPo = new PdsProductManagerBasicPo();
        BeanUtil.copyProperties(basic, basicPo);
        basicPo.setCreateTime(nowDate);
        basicPo.setCreateBy(currentUserId);
        basicPo.setProductDescription(JSON.toJSONString(basic.getProductDescriptionList()));
        basicPo.setProductSellingPoints(JSON.toJSONString(basic.getProductSellingPointsList()));
        if (basic.getAttachmentList() != null) {
            basicPo.setAttachment(JSON.toJSONString(basic.getAttachmentList()));
        }

        basicMapper.insert(basicPo);
        return basicPo.getId();
    }

    /**
     * 保存产品管理信息-属性信息
     *
     * @param pros             提交的产品管理信息
     * @param productManagerId 当前时间
     */
    @Transactional(rollbackFor = Exception.class)
    public void submitPros(PdsProductManagerSubmitProsRequest pros,
                           Integer productManagerId) {
        PdsProductManagerProsPo prosPo = new PdsProductManagerProsPo();
        BeanUtil.copyProperties(pros, prosPo);
        prosPo.setProductManagerId(productManagerId);
        prosPo.setProductLengthImperial(pros.getProductLengthMetric());
        prosPo.setProductWidthImperial(pros.getProductWidthMetric());
        prosPo.setProductHeightImperial(pros.getProductHeightMetric());
        prosPo.setProductWeightImperial(pros.getProductWeightMetric());
        prosPo.setPackagingLengthImperial(pros.getPackagingLengthMetric());
        prosPo.setPackagingWidthImperial(pros.getPackagingWidthMetric());
        prosPo.setPackagingHeightImperial(pros.getPackagingHeightMetric());
        prosPo.setGrossWeightImperial(pros.getGrossWeightMetric());

        // 公制转英制
        prosPo.convertMetricToImperial();

        Integer rows = prosMapper.insert(prosPo);
        List<PdsProductManagerSubmitProsBoxRequest> boxList = pros.getBoxList();
        for (PdsProductManagerSubmitProsBoxRequest prosBox : boxList) {
            PdsProductManagerProsBoxPo prosBoxPo = new PdsProductManagerProsBoxPo();
            BeanUtil.copyProperties(prosBox, prosBoxPo);
            prosBoxPo.setProductManagerProsId(prosPo.getId());
            prosBoxPo.setBoxLengthImperial(prosBox.getBoxLengthMetric());
            prosBoxPo.setBoxWidthImperial(prosBox.getBoxWidthMetric());
            prosBoxPo.setBoxHeightImperial(prosBox.getBoxHeightMetric());
            prosBoxPo.setGrossWeightPerBoxImperial(prosBox.getGrossWeightPerBoxMetric());

            // 公制转英制
            prosBoxPo.convertMetricToImperial();

            prosBoxMapper.insert(prosBoxPo);
        }
    }

    /**
     * 保存产品管理信息-物流信息
     *
     * @param logistics        提交的产品管理-物流信息
     * @param productManagerId 产品管理主键ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void submitLogistics(PdsProductManagerSubmitLogisticsRequest logistics, Integer productManagerId) {
        PdsProductManagerLogisticsPo logisticsPo = new PdsProductManagerLogisticsPo();
        BeanUtil.copyProperties(logistics, logisticsPo);
        logisticsPo.setProductManagerId(productManagerId);
        if (logistics.getOtherInformationList() != null) {
            logisticsPo.setOtherInformation(JSON.toJSONString(logistics.getOtherInformationList()));
        }
        logisticsMapper.insert(logisticsPo);
    }

    /**
     * 保存产品管理信息-配件信息
     *
     * @param fittingList      配件信息
     * @param productManagerId 产品管理主键ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void submitFitting(List<PdsProductManagerSubmitFittingRequest> fittingList, Integer productManagerId) {
        for (PdsProductManagerSubmitFittingRequest fitting : fittingList) {
            PdsProductManagerFittingPo fittingPo = new PdsProductManagerFittingPo();
            BeanUtil.copyProperties(fitting, fittingPo);
            fittingPo.setProductManagerId(productManagerId);
            fittingMapper.insert(fittingPo);
        }
    }

    /**
     * 获取包材信息
     *
     * @param id 产品管理主键id
     * @return 包材信息列表
     */
    public List<PdsProductManagerPackagingMaterialsPo> getPackagingMaterials(Integer id) {
        if (null == id) {
            return null;
        }

        LambdaQueryWrapper<PdsProductManagerPackagingMaterialsPo> queryWrapper = Wrappers.<PdsProductManagerPackagingMaterialsPo>lambdaQuery()
                .eq(PdsProductManagerPackagingMaterialsPo::getDeleted, CommonConstants.NOT_DELETE)
                .eq(PdsProductManagerPackagingMaterialsPo::getProductManagerId, id);

        List<PdsProductManagerPackagingMaterialsPo> poList = packagingMaterialsMapper.selectList(queryWrapper);

        return poList;
    }

    /**
     * 验证是否存在
     * @param productManagerId 产品管理主键ID
     * @return 存在返回true
     */
    public boolean isExist(Integer productManagerId) {
        // 验证缓存是否存在
        Long expire=redisService.getExpire(RedisCacheKey.getPdsPmCertKey(productManagerId));
        boolean isExist = false;
        if(expire!=null && expire.intValue()>0){
            isExist = true;
        } else{
            Integer count = packagingMaterialsMapper.selectCountByProductManagerId(productManagerId);
            if(count>0){
                isExist = true;
            }
        }
        return isExist;
    }

    /**
     * 编辑/添加 包材信息
     *
     * @param editMain      包材信息列表
     * @param currentUserId 当前用户id
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class)
    @LockRedisson(keys = {"'PDS:PM:PACKAGE:LOCK:' + #editMain.productManagerId"}, expire = 10000, acquireTimeout = 5000)
    public void editPackagingMaterials(PdsPackagingMaterialsEditMainRequest editMain, Integer currentUserId) {
        LocalDateTime nowDateTime = LocalDateTime.now();
        Integer productManagerId = editMain.getProductManagerId();

        List<PdsPackagingMaterialsEdit> editList = editMain.getPackagingMaterials();
        if (editList != null && editList.size() > 20) {
            throw new ErrorCodeException(PdsErrorCode.PACKAGINGMATERIALS_ADDSIZE_VERIFY);
        }

        List<PdsProductManagerPackagingMaterialsPo> localPackagingMaterialsList = getPackagingMaterials(productManagerId);

        if (editList != null && !editList.isEmpty()) {
            // 验证产品管理主键id是否存在、验证国家区域id是否存在
            for (PdsPackagingMaterialsEdit edit : editList) {
                Integer basicCount = basicMapper.selectCountById(productManagerId);
                if (basicCount == 0) {
                    throw new ErrorCodeException(PdsErrorCode.PRODUCT_MANAGER_ID_NOT_EXIST);
                }

                Integer applicableRegionsId = edit.getApplicableRegionsId();
                if (applicableRegionsId != null) {
                    Integer regionsCount = countryRegionMapper.selectCountById(applicableRegionsId);
                    if (regionsCount == 0) {
                        throw new ErrorCodeException(PdsErrorCode.COUNTRY_REGION_ID_NOT_EXIST);
                    }
                }
            }
            // 分析出要删除的包材信息
            List<PdsProductManagerPackagingMaterialsPo> delPoList = localPackagingMaterialsList
                    .stream()
                    .filter(x -> !editList.stream().map(e -> e.getId()).collect(Collectors.toList()).contains(x.getId()))
                    .collect(Collectors.toList());

            for (PdsProductManagerPackagingMaterialsPo delPo : delPoList) {
                packagingMaterialsMapper.deleteById(delPo.getId());
            }

            // 添加或编辑包材信息
            for (PdsPackagingMaterialsEdit edit : editList) {
                if (null == edit.getId()) {
                    PdsProductManagerPackagingMaterialsPo addPo = new PdsProductManagerPackagingMaterialsPo();
                    BeanUtil.copyProperties(edit, addPo);
                    addPo.setCreateBy(currentUserId);
                    addPo.setCreateTime(nowDateTime);
                    packagingMaterialsMapper.insert(addPo);
                } else {
                    LambdaUpdateWrapper<PdsProductManagerPackagingMaterialsPo> updateWrapper = Wrappers.<PdsProductManagerPackagingMaterialsPo>lambdaUpdate()
                            .eq(PdsProductManagerPackagingMaterialsPo::getId, edit.getId())
                            .set(PdsProductManagerPackagingMaterialsPo::getPmType, edit.getPmType())
                            .set(PdsProductManagerPackagingMaterialsPo::getPmVersion, edit.getPmVersion())
                            .set(PdsProductManagerPackagingMaterialsPo::getApplicableRegionsId, edit.getApplicableRegionsId())
                            .set(PdsProductManagerPackagingMaterialsPo::getEffectiveTimeStart, edit.getEffectiveTimeStart())
                            .set(PdsProductManagerPackagingMaterialsPo::getEffectiveTimeEnd, edit.getEffectiveTimeEnd())
                            .set(PdsProductManagerPackagingMaterialsPo::getAttachmentId, edit.getAttachmentId())
                            .set(PdsProductManagerPackagingMaterialsPo::getRemark, edit.getRemark())
                            .set(PdsProductManagerPackagingMaterialsPo::getPmType, edit.getPmType())
                            .set(PdsProductManagerPackagingMaterialsPo::getUpdateBy, currentUserId)
                            .set(PdsProductManagerPackagingMaterialsPo::getUpdateTime, nowDateTime);
                    packagingMaterialsMapper.update(updateWrapper);
                }
            }
        } else {
            if (localPackagingMaterialsList != null && !localPackagingMaterialsList.isEmpty()) {
                for (PdsProductManagerPackagingMaterialsPo delPo : localPackagingMaterialsList) {
                    packagingMaterialsMapper.deleteById(delPo.getId());
                }
            }
        }
        // 更新主体信息的更新时间
        this.updatePMUpdateTime(productManagerId);
    }

    /**
     * 更新产品管理基本信息的更新时间
     * @param productManagerId 产品管理主键id
     */
    public void updatePMUpdateTime(Integer productManagerId) {
        if(productManagerId == null){
            return;
        }
        PdsProductManagerBasicPo basicPo = new PdsProductManagerBasicPo();
        basicPo.setId(productManagerId);
        basicPo.setUpdateTime(LocalDateTime.now());
        basicMapper.updateByPrimaryKey(basicPo);
    }

    /**
     * 获取认证资料
     *
     * @param id 产品管理id
     * @return 认证资料列表
     */
    public List<PdsProductManagerCertificationPo> getCertification(Integer id) {
        if (null == id) {
            return null;
        }

        LambdaQueryWrapper<PdsProductManagerCertificationPo> queryWrapper = Wrappers.<PdsProductManagerCertificationPo>lambdaQuery()
                .eq(PdsProductManagerCertificationPo::getProductManagerId, id)
                .eq(PdsProductManagerCertificationPo::getDeleted, CommonConstants.NOT_DELETE)
                .orderByDesc(PdsProductManagerCertificationPo::getCreateTime);

        List<PdsProductManagerCertificationPo> poList = certificationMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(poList)) {
            return new ArrayList<>();
        }

        return poList;
    }

    /**
     * 编辑/添加 认证资料
     *
     * @param editMain 包材认证资料
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class)
    public void editCertification(PdsCertificationEditMainRequest editMain, Integer currentUserId) {
        LocalDateTime nowDateTime = LocalDateTime.now();

        List<PdsCertificationEdit> editList = editMain.getCertificationList();
        if (editList != null && editList.size() > 20) {
            throw new ErrorCodeException(PdsErrorCode.PACKAGINGMATERIALS_ADDSIZE_VERIFY);
        }

        Integer productManagerId = editMain.getProductManagerId();
        List<PdsProductManagerCertificationPo> localCertificationList = getCertification(productManagerId);
        if (editList != null && !editList.isEmpty()) {
            // 验证产品管理主键id是否存在、验证国家区域id是否存在
            for (PdsCertificationEdit edit : editList) {
                Integer applicableRegionsId = edit.getApplicableRegionsId();

                Integer basicCount = basicMapper.selectCountById(productManagerId);
                if (basicCount == 0) {
                    throw new ErrorCodeException(PdsErrorCode.PRODUCT_MANAGER_ID_NOT_EXIST);
                }

                if (applicableRegionsId != null) {
                    Integer regionsCount = countryRegionMapper.selectCountById(applicableRegionsId);
                    if (regionsCount == 0) {
                        throw new ErrorCodeException(PdsErrorCode.COUNTRY_REGION_ID_NOT_EXIST);
                    }
                }
            }

            List<PdsProductManagerCertificationPo> delPoList = localCertificationList
                    .stream()
                    .filter(x -> !editList.stream().map(e -> e.getId()).collect(Collectors.toList()).contains(x.getId()))
                    .collect(Collectors.toList());
            for (PdsProductManagerCertificationPo delPo : delPoList) {
                certificationMapper.deleteById(delPo.getId());
            }

            for (PdsCertificationEdit edit : editList) {
                if (null == edit.getId()) {
                    PdsProductManagerCertificationPo addPo = new PdsProductManagerCertificationPo();
                    BeanUtil.copyProperties(edit, addPo);
                    addPo.setCreateBy(currentUserId);
                    addPo.setCreateTime(nowDateTime);
                    certificationMapper.insert(addPo);
                } else {
                    LambdaUpdateWrapper<PdsProductManagerCertificationPo> updateWrapper = Wrappers.<PdsProductManagerCertificationPo>lambdaUpdate()
                            .eq(PdsProductManagerCertificationPo::getId, edit.getId())
                            .set(PdsProductManagerCertificationPo::getName, edit.getName())
                            .set(PdsProductManagerCertificationPo::getDescribe, edit.getDescribe())
                            .set(PdsProductManagerCertificationPo::getApplicableRegionsId, edit.getApplicableRegionsId())
                            .set(PdsProductManagerCertificationPo::getEffectiveTimeStart, edit.getEffectiveTimeStart())
                            .set(PdsProductManagerCertificationPo::getEffectiveTimeEnd, edit.getEffectiveTimeEnd())
                            .set(PdsProductManagerCertificationPo::getAttachmentId, edit.getAttachmentId())
                            .set(PdsProductManagerCertificationPo::getRemark, edit.getRemark())
                            .set(PdsProductManagerCertificationPo::getUpdateBy, currentUserId)
                            .set(PdsProductManagerCertificationPo::getUpdateTime, nowDateTime);
                    certificationMapper.update(updateWrapper);
                }
            }
        } else {
            if (localCertificationList != null && !localCertificationList.isEmpty()) {
                for (PdsProductManagerCertificationPo delPo : localCertificationList) {
                    certificationMapper.deleteById(delPo.getId());
                }
            }
        }
    }

    /**
     * 产品管理-列表-复制
     *
     * @param id       产品管理主键id
     * @param language 语言
     * @return 产品管理详情信息
     */
    public PdsProductManagerDetailsResponse copy(Integer id, LanguageEnum language) {
        return details(id, language);
    }

    /**
     * 产品管理-详情-基础信息
     *
     * @param id 产品管理主键ID
     * @param language 语言
     * @return 产品管理详情
     */
    public PdsProductManagerDetailsBasicResponse viewBasicDetail(Integer id, LanguageEnum language) {
        PdsProductManagerBasicPo basicPo = basicMapper.selectById(id);
        if (null == basicPo) {
            return null;
        }
        // 基本信息
        PdsProductManagerDetailsBasicResponse basicResp = new PdsProductManagerDetailsBasicResponse();
        BeanUtil.copyProperties(basicPo, basicResp);

        // 补充基本信息详情
        buildBasicInfo(basicResp, language);
        return basicResp;
    }

    /**
     * 根据产品管理主键ID获取详情信息
     *
     * @param id       主键ID
     * @param language 语言
     * @return 详情信息
     */
    public PdsProductManagerDetailsResponse details(Integer id, LanguageEnum language) {
        if (null == id) {
            return null;
        }

        PdsProductManagerBasicPo basicPo = basicMapper.selectById(id);
        if (null == basicPo) {
            return null;
        }

        // 主体信息
        PdsProductManagerDetailsResponse resp = new PdsProductManagerDetailsResponse();

        // 基本信息
        PdsProductManagerDetailsBasicResponse basicResp = new PdsProductManagerDetailsBasicResponse();
        BeanUtil.copyProperties(basicPo, basicResp);

        // 补充基本信息详情
        buildBasicInfo(basicResp, language);
        resp.setBasic(basicResp);

        // 属性信息
        LambdaQueryWrapper<PdsProductManagerProsPo> queryWrapper = Wrappers.<PdsProductManagerProsPo>lambdaQuery();
        queryWrapper.eq(PdsProductManagerProsPo::getProductManagerId, id);

        PdsProductManagerProsPo prosPo = prosMapper.selectOne(queryWrapper);
        if (prosPo != null) {
            PdsProductManagerDetailsProsResponse prosResp = new PdsProductManagerDetailsProsResponse();
            BeanUtil.copyProperties(prosPo, prosResp);
            resp.setPros(prosResp);

            // 外箱规格
            LambdaQueryWrapper<PdsProductManagerProsBoxPo> queryWrapperBox = Wrappers.<PdsProductManagerProsBoxPo>lambdaQuery();
            queryWrapperBox.eq(PdsProductManagerProsBoxPo::getProductManagerProsId, prosPo.getId());
            List<PdsProductManagerProsBoxPo> prosBoxPos = prosBoxMapper.selectList(queryWrapperBox);

            if (CollectionUtil.isNotEmpty(prosBoxPos)) {
                for (PdsProductManagerProsBoxPo prosBoxPo : prosBoxPos) {
                    PdsProductManagerDetailsProsBoxResponse prosBoxResp = new PdsProductManagerDetailsProsBoxResponse();
                    BeanUtil.copyProperties(prosBoxPo, prosBoxResp);
                    prosResp.addBox(prosBoxResp);
                }
            }
        }

        // 物流信息
        LambdaQueryWrapper<PdsProductManagerLogisticsPo> queryWrapperLogistics = Wrappers.<PdsProductManagerLogisticsPo>lambdaQuery();
        queryWrapperLogistics.eq(PdsProductManagerLogisticsPo::getProductManagerId, id);
        PdsProductManagerLogisticsPo logisticsPo = logisticsMapper.selectOne(queryWrapperLogistics);

        if (logisticsPo != null) {
            PdsProductManagerDetailsLogisticsResponse logisticsResp = new PdsProductManagerDetailsLogisticsResponse();
            BeanUtil.copyProperties(logisticsPo, logisticsResp);
            resp.setLogistics(logisticsResp);
        }

        // 配件信息
        LambdaQueryWrapper<PdsProductManagerFittingPo> queryWrapperFitting = Wrappers.<PdsProductManagerFittingPo>lambdaQuery();
        queryWrapperFitting.eq(PdsProductManagerFittingPo::getProductManagerId, id);
        List<PdsProductManagerFittingPo> fittingList = fittingMapper.selectList(queryWrapperFitting);

        if (CollectionUtil.isNotEmpty(fittingList)) {
            List<PdsProductManagerDetailsFittingResponse> fittingResponses = new ArrayList<>();
            for (PdsProductManagerFittingPo fittingPo : fittingList) {
                PdsProductManagerDetailsFittingResponse fittingResp = new PdsProductManagerDetailsFittingResponse();
                BeanUtil.copyProperties(fittingPo, fittingResp);

                FileDetailResponse imageInfo = pdsCommonSrv.getFileInfo(fittingPo.getImageId());
                fittingResp.setImageInfo(imageInfo);

                String applicableRegionsIds = fittingPo.getApplicableRegionsIds();
                if (StringUtils.isNotBlank(applicableRegionsIds)) {
                    List<String> names = new ArrayList<>();
                    String[] applicableRegionsIdArr = applicableRegionsIds.split(",");
                    for (String applicableRegionsId : applicableRegionsIdArr) {
                        if (StringUtils.isNotBlank(applicableRegionsId)) {
                            List<LanguageCountryRegion> languageCountryRegions = countryRegionSrv.getLanguages(Integer.parseInt(applicableRegionsId));
                            String name = LanguageEnum.getLanguage(languageCountryRegions, language);
                            names.add(name);
                        }
                    }
                    fittingResp.setApplicableRegionsNames(names);
                }


                fittingResponses.add(fittingResp);
            }
            resp.setFittingList(fittingResponses);
        }

        // 包材信息
        List<PdsProductManagerPackagingMaterialsPo> packagingMaterialsList = getPackagingMaterials(id);
        if (CollectionUtil.isNotEmpty(packagingMaterialsList)) {
            List<PdsPackagingMaterialsResponse> packagingMaterialsRespList = packagingMaterialsList
                    .stream()
                    .map(x -> {
                        PdsPackagingMaterialsResponse packagingMaterialsResp = new PdsPackagingMaterialsResponse();
                        BeanUtil.copyProperties(x, packagingMaterialsResp);

                        FileDetailResponse attachmentInfo = pdsCommonSrv.getFileInfo(x.getAttachmentId());
                        packagingMaterialsResp.setAttachmentInfo(attachmentInfo);

                        if (x.getApplicableRegionsId() != null) {
                            List<LanguageCountryRegion> languageCountryRegions = countryRegionSrv.getLanguages(x.getApplicableRegionsId());
                            String name = LanguageEnum.getLanguage(languageCountryRegions, language);
                            packagingMaterialsResp.setApplicableRegionsName(name);
                        }

                        return packagingMaterialsResp;
                    })
                    .collect(Collectors.toList());
            resp.setPackagingMaterialsList(packagingMaterialsRespList);
        }

        // 认证信息
        PdsProductManagerCertificationBasicResponse certification = certificationSrv.getCertificationById(id);
        //根据权限控制数据 不为管理员时
        if (!SecurityUtils.isAdmin()) {
            //判断是否包含合规角色标签
            if (!isContainCompliance()) {
                //不包含，则过滤掉认证信息 数据状态为缺失的数据
                if (CollectionUtil.isNotEmpty(certification.getComponentList())) {
                    certification.getComponentList().removeIf(x -> x.getDataStatus() == 1);
                }
                if (CollectionUtil.isNotEmpty(certification.getMachineList())) {
                    certification.getMachineList().removeIf(x -> x.getDataStatus() == 1);
                }
            }
            //判断是否包含合规附件查看角色标签
            if (!isContainComplianceAttachment()) {
                //不包含，则过滤掉认证信息 附件数据
                certification.setComplianceAttachmentFlag(false);
            }
        }
        resp.setCertification(certification);

        // 授权信息
        List<PdsProductManagerAuthorizeDetailResponse> authorizeDetails = pdsProductManagerAuthorizeSrv.getAuthorizeDetails(id);
        resp.setAuthorizeList(authorizeDetails);

        return resp;
    }


    /**
     * 是否包含合规角色标签
     * <AUTHOR>
     * @Date 16:54 2025/7/23
     * @Param []
     * @return java.lang.Boolean
     **/
    private Boolean isContainCompliance() {
        // 获取用户标签
        String[] userLabels = SecurityUtils.getUserLabels();
        if (userLabels == null || userLabels.length == 0) {
            return false;
        }
        List<String> userLabelList = Arrays.asList(userLabels);
        // 配置的合规角色标签
        List<String> roleLabelComplianceList = Arrays.asList(roleLabelCompliance.split(","));
        boolean complianceFlag = false;
        for (String label : userLabelList) {
            if (roleLabelComplianceList.contains(label)) {
                complianceFlag = true;
            }
        }
        return complianceFlag;
    }

    /**
     * 是否包含合规附件查看角色标签
     * <AUTHOR>
     * @Date 16:54 2025/7/23
     * @Param []
     * @return java.lang.Boolean
     **/
    private Boolean isContainComplianceAttachment() {
        // 获取用户标签
        String[] userLabels = SecurityUtils.getUserLabels();
        if (userLabels == null || userLabels.length == 0) {
            return false;
        }
        List<String> userLabelList = Arrays.asList(userLabels);
        // 配置的合规附件查看角色标签
        List<String> roleLabelComplianceAttachmentList = Arrays.asList(roleLabelComplianceAttachment.split(","));
        boolean complianceFlag = false;
        for (String label : userLabelList) {
            if (roleLabelComplianceAttachmentList.contains(label)) {
                complianceFlag = true;
            }
        }
        return complianceFlag;
    }



    /**
     * 补充基本信息详情
     *
     * @param basicResp 基本信息
     * @param language  语言
     */
    public void buildBasicInfo(PdsProductManagerDetailsBasicResponse basicResp, LanguageEnum language) {
        if (StringUtils.isNotBlank(basicResp.getProductCoverImageId())) {
            FileDetailResponse fileInfo = pdsCommonSrv.getFileInfo(basicResp.getProductCoverImageId());
            if (fileInfo != null) {
                basicResp.setProductCoverImageFileInfo(fileInfo);
            }
        }
        if (basicResp.getCountryRegionId() != null) {
            List<LanguageCountryRegion> languageCountryRegions = countryRegionSrv.getLanguages(basicResp.getCountryRegionId());
            String name = LanguageEnum.getLanguage(languageCountryRegions, language);
            basicResp.setCountryRegionName(name);
        }
        if (basicResp.getCategoryId() != null) {
            StringBuilder treeName = new StringBuilder();
            categorySrv.getCategoryTreeName(basicResp.getCategoryId(), language, treeName);
            basicResp.setCategoryName(treeName.toString());

            Deque<Integer> categoryIds = new ArrayDeque<>();
            categorySrv.getCategoryTreeIds(basicResp.getCategoryId(), categoryIds);
            basicResp.setCategoryIds(StackUtils.stackToList(categoryIds));

        }
        if (basicResp.getColorId() != null) {
            List<LanguageColor> languageColors = colorSrv.getLanguages(basicResp.getColorId());
            String name = LanguageEnum.getLanguage(languageColors, language);
            basicResp.setColorName(name);
        }
        if (basicResp.getProductModelId() != null) {
            PdsProductModelPo productModelPo = pdsProductModelSrv.getById(basicResp.getProductModelId());
            if (productModelPo != null) {
                basicResp.setProductModelNo(productModelPo.getModelNo());
            }
        }
        if (basicResp.getCategoryId() != null) {
            PdsBrandPo brandPo = brandSrv.getPdsBrandDetail(basicResp.getBrandId());
            if (brandPo != null) {
                basicResp.setBrandName(brandPo.getBrand());
            }
        }
        if (basicResp.getSalesChannelId() != null) {
            SalesChannelIdsQuery request = new SalesChannelIdsQuery();
            request.setIds(Collections.singletonList(basicResp.getSalesChannelId()));

            R<List<SalesChannelVo>> salesChannelVos = remoteSalesChannelService.findByIds(request);
            if (salesChannelVos != null && salesChannelVos.getData()!=null) {
                SalesChannelVo salesChannelVo = salesChannelVos.getData().get(0);
                basicResp.setSalesChannelName(salesChannelVo.getChannelName());
            }
        }

        // 补充人员信息
        Set<Integer> userIds = new HashSet<Integer>();
        Map<Integer, OumUserInfoRes> userInfoResMap = new HashMap<>();
        if (basicResp.getProductManagerId() != null) {
            userIds.add(basicResp.getProductManagerId());
        }
        if (basicResp.getBusinessManagerId() != null) {
            userIds.add(basicResp.getBusinessManagerId());
        }
        if (basicResp.getProcurementManagerId() != null) {
            userIds.add(basicResp.getProcurementManagerId());
        }
        if (basicResp.getPlanningManagerId() != null) {
            userIds.add(basicResp.getPlanningManagerId());
        }
        if (basicResp.getComplianceManagerId() != null) {
            userIds.add(basicResp.getComplianceManagerId());
        }
        if (basicResp.getPackingMaterialManagerId() != null) {
            userIds.add(basicResp.getPackingMaterialManagerId());
        }

        List<OumUserInfoRes> userListInfo = pdsCommonSrv.getUserListInfo(userIds.stream().toList());
        if (userListInfo != null && !userListInfo.isEmpty()) {
            userInfoResMap = userListInfo.stream().collect(Collectors.toMap(OumUserInfoRes::getId, Function.identity()));
        }

        if (basicResp.getProductManagerId() != null) {
            OumUserInfoRes userInfoRes = userInfoResMap.get((basicResp.getProductManagerId()));
            if (userInfoRes != null) {
                String userDesc = pdsCommonSrv.getUserDesc(userInfoRes);
                basicResp.setProductManagerName(userDesc);
                basicResp.setProductManagerStatus(userInfoRes.getStatus());
            }
        }
        if (basicResp.getBusinessManagerId() != null) {
            OumUserInfoRes userInfoRes = userInfoResMap.get(basicResp.getBusinessManagerId());
            if (userInfoRes != null) {
                String userDesc = pdsCommonSrv.getUserDesc(userInfoRes);
                basicResp.setBusinessManagerName(userDesc);
                basicResp.setBusinessManagerStatus(userInfoRes.getStatus());
            }
        }
        if (basicResp.getProcurementManagerId() != null) {
            OumUserInfoRes userInfoRes = userInfoResMap.get(basicResp.getProcurementManagerId());
            if (userInfoRes != null) {
                String userDesc = pdsCommonSrv.getUserDesc(userInfoRes);
                basicResp.setProcurementManagerName(userDesc);
                basicResp.setProcurementManagerStatus(userInfoRes.getStatus());
            }
        }
        if (basicResp.getPlanningManagerId() != null) {
            OumUserInfoRes userInfoRes = userInfoResMap.get(basicResp.getPlanningManagerId());
            if (userInfoRes != null) {
                String userDesc = pdsCommonSrv.getUserDesc(userInfoRes);
                basicResp.setPlanningManagerName(userDesc);
                basicResp.setPlanningManagerStatus(userInfoRes.getStatus());
            }
        }
        if (basicResp.getComplianceManagerId() != null) {
            OumUserInfoRes userInfoRes = userInfoResMap.get(basicResp.getComplianceManagerId());
            if (userInfoRes != null) {
                String userDesc = pdsCommonSrv.getUserDesc(userInfoRes);
                basicResp.setComplianceManagerName(userDesc);
                basicResp.setComplianceManagerStatus(userInfoRes.getStatus());
            }
        }
        if (basicResp.getPackingMaterialManagerId() != null) {
            OumUserInfoRes userInfoRes = userInfoResMap.get(basicResp.getPackingMaterialManagerId());
            if (userInfoRes != null) {
                String userDesc = pdsCommonSrv.getUserDesc(userInfoRes);
                basicResp.setPackingMaterialManagerName(userDesc);
                basicResp.setPackingMaterialManagerStatus(userInfoRes.getStatus());
            }
        }


    }

    /**
     * 产品管理-主数据查询
     *
     * @param queryCmd 查询条件
     * @param language 语言
     * @return 列表
     */
    public Paging<PdsProductManagerBasicPo> viewPage(PdsProductManagerQueryViewRequest queryCmd, LanguageEnum language) {
        // 使用 PageHelper 进行分页
        int pageNum = queryCmd.getPageIndex();
        int pageSize = queryCmd.getPageSize();
        PageHelper.startPage(pageNum, pageSize);

        LambdaQueryWrapper<PdsProductManagerBasicPo> queryWrapper = new LambdaQueryWrapper<PdsProductManagerBasicPo>();
        queryWrapper.eq(PdsProductManagerBasicPo::getReviewStatus, PdsManagerReviewStatus.APPROVED.getCode());  //审核通过

        // // 根据入参条件构建查询
        // if (queryCmd.getSalesChannelId() != null) {
        //     queryWrapper.eq(PdsProductManagerBasicPo::getSalesChannelId, queryCmd.getSalesChannelId());
        // }

        // 根据入参条件构建查询
        if (queryCmd.getChannelType() != null) {
            queryWrapper.eq(PdsProductManagerBasicPo::getChannelType, queryCmd.getChannelType());
        }
        if(queryCmd.getId()!=null){
            queryWrapper.eq(PdsProductManagerBasicPo::getId, queryCmd.getId());
        }
        if (queryCmd.getPurchaseSku() != null && !queryCmd.getPurchaseSku().isEmpty()) {
            queryWrapper.eq(PdsProductManagerBasicPo::getPurchaseSku, queryCmd.getPurchaseSku());
        }
        if (queryCmd.getName() != null && !queryCmd.getName().isEmpty()) {
            queryWrapper.and(wrapper -> wrapper
                    .like(PdsProductManagerBasicPo::getChineseName, queryCmd.getName())
                    .or()
                    .like(PdsProductManagerBasicPo::getEnglishName, queryCmd.getName()));
        }
        if (queryCmd.getReviewStatus() != null) {
            queryWrapper.eq(PdsProductManagerBasicPo::getReviewStatus, queryCmd.getReviewStatus());
        }
        if (queryCmd.getBusinessManagerId() != null) {
            queryWrapper.eq(PdsProductManagerBasicPo::getBusinessManagerId, queryCmd.getBusinessManagerId());
        }
        if (queryCmd.getProcurementManagerId() != null) {
            queryWrapper.eq(PdsProductManagerBasicPo::getProcurementManagerId, queryCmd.getProcurementManagerId());
        }
        if (queryCmd.getPlanningManagerId() != null) {
            queryWrapper.eq(PdsProductManagerBasicPo::getPlanningManagerId, queryCmd.getPlanningManagerId());
        }
        if (queryCmd.getCateThird() != null) {
            queryWrapper.eq(PdsProductManagerBasicPo::getCategoryId, queryCmd.getCateThird());
        }
        if (queryCmd.getColorId() != null) {
            queryWrapper.eq(PdsProductManagerBasicPo::getColorId, queryCmd.getColorId());
        }
        if (queryCmd.getCountryRegionId() != null) {
            queryWrapper.eq(PdsProductManagerBasicPo::getCountryRegionId, queryCmd.getCountryRegionId());
        }
        if (queryCmd.getProductType() != null) {
            queryWrapper.eq(PdsProductManagerBasicPo::getProductType, queryCmd.getProductType());
        }
        if (queryCmd.getPurchaseSkuList() != null && !queryCmd.getPurchaseSkuList().isEmpty()) {
            queryWrapper.in(PdsProductManagerBasicPo::getPurchaseSku, queryCmd.getPurchaseSkuList());
        }

        // psku 模糊查询
        if (StringUtils.isNotBlank(queryCmd.getKeyword())) {
            String keyword = "%" + queryCmd.getKeyword() + "%";
            queryWrapper.and(wrapper -> wrapper.like(PdsProductManagerBasicPo::getPurchaseSku, keyword));
        }

        Page<PdsProductManagerBasicPo> page = new Page<PdsProductManagerBasicPo>(pageNum, pageSize);
        IPage<PdsProductManagerBasicPo> skuPage = basicMapper.selectPage(page, queryWrapper);
        return PagingUtils.convertToPaging(skuPage);

    }

    /**
     * 产品管理-主数据查询-详情
     *
     * @param queryCmd 查询条件
     * @param language 语言
     * @return 列表
     */
    public List<PdsProductManagerDetailViewResponse> viewListDetail(PdsProductManagerQueryDetailViewRequest queryCmd, LanguageEnum language) {
        List<PdsProductManagerDetailViewResponse> respList = new ArrayList<>();

        LambdaQueryWrapper<PdsProductManagerBasicPo> queryWrapper = new LambdaQueryWrapper<PdsProductManagerBasicPo>();
        if (queryCmd.getPSkuList() != null && !queryCmd.getPSkuList().isEmpty()) {
            queryWrapper.in(PdsProductManagerBasicPo::getPurchaseSku, queryCmd.getPSkuList());
        }
        if (queryCmd.getProductIdList()!= null &&!queryCmd.getProductIdList().isEmpty()) {
            queryWrapper.in(PdsProductManagerBasicPo::getId, queryCmd.getProductIdList());
        }
        if(queryCmd.getReviewStatus()!=null){
            queryWrapper.eq(PdsProductManagerBasicPo::getReviewStatus,queryCmd.getReviewStatus());
        }
        if(queryCmd.getProductStatus()!=null){
            queryWrapper.eq(PdsProductManagerBasicPo::getProductStatus,queryCmd.getProductStatus());
        }

        // TODO 限制单次查询最多100条
        Page<PdsProductManagerBasicPo> page = new Page<PdsProductManagerBasicPo>(1, 200);
        IPage<PdsProductManagerBasicPo> skuPage = basicMapper.selectPage(page, queryWrapper);

        List<PdsProductManagerBasicPo> basicPoList = skuPage.getRecords();
        List<PdsProductModel.PdsProductModelID> modelIds = basicPoList.stream()
                .map(PdsProductManagerBasicPo::getProductModelId)
                .filter(Objects::nonNull)
                .map(PdsProductModel.PdsProductModelID::new)
                .toList();
        Map<PdsProductModel.PdsProductModelID, PdsProductModel> modelMap = pdsProductModelSrv.findProductModelMap(modelIds);
        for (PdsProductManagerBasicPo basicPo : basicPoList) {
            PdsProductManagerDetailViewResponse resp = new PdsProductManagerDetailViewResponse();

            // 基本信息
            PdsProductManagerBasicViewResponse basic = PdsProductManagerConverter.INSTANCE.toBasicViewResponse(basicPo);
            // 补充产品描述
            if(basicPo.getProductDescription()!=null && !basicPo.getProductDescription().equals("null")){
                List<String> descriptionList = JSON.parseArray(basicPo.getProductDescription(), String.class);
                basic.setDescriptionList(descriptionList);
            }

            //型号
            Optional.ofNullable(modelMap.get(new PdsProductModel.PdsProductModelID(basic.getProductModelId()))).ifPresent(model -> {
                basic.setProductModelNo(model.getModelNo());
            });

            FileRequest fileRequest = new FileRequest();
            if(basicPo.getProductCoverImageId()!=null){
                fileRequest.setFileIds(List.of(basicPo.getProductCoverImageId()));
                fileRequest.setContentDisposition("inline");
                R<FileDetailResponse> r = remoteFileFeign.getFileInfo(fileRequest);
                RetOps.of(r).getData()
                        .ifPresent(fileInfo -> basic.setProductCoverImageUrl(fileInfo.getUrl()));
            }

            PdsBrandPo brandPo = brandMapper.selectById(basicPo.getBrandId());
            Optional.ofNullable(brandPo).ifPresent(po->{
                basic.setBrandCode(po.getCode());
                basic.setLastSnCode(po.getLastSnCode());
                basic.setBrandName(po.getBrand());
            });
            PdsCategoryPo catePO = categorySrv.getPdsCategoryPoDetail(basicPo.getCategoryId());
            List<LanguageCategory> languages = categorySrv.getLanguages(catePO.getId());
            Optional.ofNullable(catePO).ifPresent(po -> {
                basic.setSnCoefficient(po.getSnCoefficient());
                basic.setLanguageCategories(languages);
            });
            Optional.ofNullable(catePO).map(PdsCategoryPo::getSnCoefficient).ifPresent(basic::setSnCoefficient);

            resp.setBasic(basic);

            // 属性信息
            LambdaQueryWrapper<PdsProductManagerProsPo> proWrapper = Wrappers.<PdsProductManagerProsPo>lambdaQuery();
            proWrapper.eq(PdsProductManagerProsPo::getProductManagerId, basicPo.getId());
            PdsProductManagerProsPo prosPo = prosMapper.selectOne(proWrapper);
            if (prosPo != null) {
                PdsProductManagerDetailsProsResponse prosResp = new PdsProductManagerDetailsProsResponse();
                BeanUtil.copyProperties(prosPo, prosResp);
                resp.setPros(prosResp);

                // 外箱规格
                LambdaQueryWrapper<PdsProductManagerProsBoxPo> boxWrapper = Wrappers.<PdsProductManagerProsBoxPo>lambdaQuery();
                boxWrapper.eq(PdsProductManagerProsBoxPo::getProductManagerProsId, prosPo.getId());
                List<PdsProductManagerProsBoxPo> prosBoxPos = prosBoxMapper.selectList(boxWrapper);
                if (CollectionUtil.isNotEmpty(prosBoxPos)) {
                    for (PdsProductManagerProsBoxPo prosBoxPo : prosBoxPos) {
                        PdsProductManagerDetailsProsBoxResponse prosBoxResp = new PdsProductManagerDetailsProsBoxResponse();
                        BeanUtil.copyProperties(prosBoxPo, prosBoxResp);
                        prosResp.addBox(prosBoxResp);
                    }
                }
            }
            // 物流信息
            LambdaQueryWrapper<PdsProductManagerLogisticsPo> queryWrapperLogistics = Wrappers.<PdsProductManagerLogisticsPo>lambdaQuery();
            queryWrapperLogistics.eq(PdsProductManagerLogisticsPo::getProductManagerId, basicPo.getId());
            PdsProductManagerLogisticsPo logisticsPo = logisticsMapper.selectOne(queryWrapperLogistics);
            if (logisticsPo != null) {
                PdsProductManagerDetailsLogisticsResponse logisticsResp = new PdsProductManagerDetailsLogisticsResponse();
                BeanUtil.copyProperties(logisticsPo, logisticsResp);
                resp.setLogistics(logisticsResp);
            }

            respList.add(resp);
        }

        return respList;
    }


    /**
     * 处理导入的数据
     *
     * @param importDataList 导入的原始数据
     * @param language       语言
     * @return 处理后的导入数据
     */
    public ImportProductManagerVerify handlerImportData(List<PurchaseSkuImport> importDataList, LanguageEnum language) {
        ImportProductManagerVerify verify = new ImportProductManagerVerify();

        // 1.转换数据
        List<PdsProductManagerSubmitCmd> submitCmdList = new ArrayList<>();
        // 缓存数据
        BuildSubmitCache cache = new BuildSubmitCache();
        cache.buildSrv(pdsCommonSrv, countryRegionSrv, colorSrv, productModelSrv, remoteSalesChannelService);  //构建srv

        Long startTime = System.currentTimeMillis();
        for (PurchaseSkuImport purchaseSkuImport : importDataList) {
            try {
                PdsProductManagerSubmitCmd submitCmd = this.toSubmitCmd(purchaseSkuImport, cache);
                // 新增状态，需要严格检验
                submitCmd.setStatus(CommonConstants.ACTION_TYPE_SUBMIT);
                // 默认提交状态(待审核)
                submitCmd.getBasic().setReviewStatus(PdsManagerReviewStatus.DRAFT.getCode());
                submitCmdList.add(submitCmd);
            } catch (Exception e) {
                log.error("导入数据校验失败", e);
                PurchaseSkuImportError error = PdsProductManagerPoConverter.INSTANCE.toPurchaseSkuImportError(purchaseSkuImport);
                error.appendErrorMsg(e.getMessage());
                verify.addImportError(error);
            }
        }
        Long endTime = System.currentTimeMillis();
        log.info("导入数据->转换数据耗时：{}ms", endTime - startTime);

        startTime = System.currentTimeMillis();
        try{
            // 构建批量信息
            cache.buildCache(log);
        }catch (Exception e){
            log.error("导入数据->构建批量信息异常", e);
            for (PdsProductManagerSubmitCmd submitCmd : submitCmdList) {
                PurchaseSkuImportError error = PdsProductManagerPoConverter.INSTANCE.toPurchaseSkuImportError(submitCmd.getImportData());
                error.appendErrorMsg(e.getMessage());
                verify.addImportError(error);
            }
            submitCmdList.clear();  //清空数据
        }
        endTime = System.currentTimeMillis();
        log.info("导入数据->构建批量信息耗时：{}ms", endTime - startTime);

        // 获取通过数据是否存在的数据
        startTime = System.currentTimeMillis();
        List<PdsProductManagerSubmitCmd> rightSubmitList= cache.buildDataBasicCmdList(submitCmdList, verify, log);
        endTime = System.currentTimeMillis();
        log.info("导入数据->检验数据是否存在的数据耗时：{}ms", endTime - startTime);

        // 2.验证格式是否合法
        startTime = System.currentTimeMillis();
        for (PdsProductManagerSubmitCmd submitCmd : rightSubmitList) {
            try {
                // 2.1 验证参数是否合法,共用前端验证这一套
                ValidationUtil.validate(submitCmd);
                this.verifyDataAdd(language, submitCmd);

                // 2.2 验证合法则转为可提交的数据
                PdsProductManagerSubmitRequest pmRequest = PdsProductManagerPoConverter.INSTANCE.submitCmdToProductManagerSubmit(submitCmd);
                verify.addVerifyList(pmRequest);
            } catch (Exception e) {
                log.error("导入数据校验失败", e);
                PurchaseSkuImportError error = PdsProductManagerPoConverter.INSTANCE.toPurchaseSkuImportError(submitCmd.getImportData());
                error.appendErrorMsg(e.getMessage());
                verify.addImportError(error);
            }
        }
        endTime = System.currentTimeMillis();
        log.info("导入数据->验证格式是否合法耗时：{}ms", endTime - startTime);

        // 3.验证数据是否存在，存在则尝试导入数据
        startTime = System.currentTimeMillis();
        this.importProductManagerVerify(verify);
        endTime = System.currentTimeMillis();
        log.info("导入数据->添加到数据库操作耗时间：{}ms", endTime - startTime);

        return verify;
    }

    /**
     * 产品管理-导入-验证数据
     *
     * @param verify 验证过程
     * @return 验证结果
     */
    public void importProductManagerVerify(ImportProductManagerVerify verify) {
        Integer userId = SecurityUtils.getUserId();
        List<PdsProductManagerSubmitRequest> verifyList = verify.getVerifyList();
        for (PdsProductManagerSubmitRequest submit : verifyList) {
            //this.toSubmit(verify,submit, userId);
            PdsProductManagerSrv proxy = (PdsProductManagerSrv) AopContext.currentProxy();
            proxy.toSubmit(verify,submit, userId, this.transactionManager);
        }
    }

    /**
     * 执行添加产品管理数据
     *
     * @param verify 验证过程
     * @param submit 数据
     * @param userId 当前登录用户ID
     */
    @OpLog(snaptSource = ProductManagerImportSnapt.class, title = "产品管理-导入数据", businessType = BusinessType.IMPORT,
            systemModule = PdsSystemModule.class, businessModule = ProductManagerModules.class)
    public Integer toSubmit(ImportProductManagerVerify verify,PdsProductManagerSubmitRequest submit, Integer userId, PlatformTransactionManager transactionManager) {
        TransactionStatus status = transactionManager.getTransaction(new DefaultTransactionDefinition());
        try {
            // 使用代理类执行 submitImport 方法并控制事务
            Integer productManagerId = submit(submit, userId);
            verify.addInsertId(productManagerId);

            transactionManager.commit(status); // 提交事务
            return productManagerId;
        } catch (Exception e) {
            log.error("产品管理-导入异常: ", e);
            transactionManager.rollback(status); // 回滚事务

            PurchaseSkuImportError error = PdsProductManagerPoConverter.INSTANCE.toPurchaseSkuImportError(submit.getImportData());
            error.appendErrorMsg(e.getMessage());
            verify.addImportError(error);
        }
        return 0;
    }

    /**
     * 转换数据
     * <br/> TODO 后期需要改为批量查询方式，减少数据库查询次数
     *
     * @param pi 导入的原始数据
     * @param cache 缓存数据
     * @return PdsProductManagerSubmitCmd格式数据
     */
    public PdsProductManagerSubmitCmd toSubmitCmd(PurchaseSkuImport pi, BuildSubmitCache cache) {
        PdsProductManagerSubmitCmd submitCmd = new PdsProductManagerSubmitCmd();

        // 1. 补充基础信息
        PdsProductManagerSubmitBasicCmd basic = buildSubmitCmd(pi, cache);
        submitCmd.setBasic(basic);

        // 2. 补充属性信息
        PdsProductManagerSubmitProsCmd pros = buildSubmitProsCmd(pi, cache);
        submitCmd.setPros(pros);

        // 3. 属性信息-外箱规格
        PdsProductManagerSubmitProsBoxCmd box = buildSubmitBoxCmd(pi);
        pros.addBox(box);

        // 4. 补充物流信息
        PdsProductManagerSubmitLogisticsCmd logistics = buildSubmitLogistics(pi);
        submitCmd.setLogistics(logistics);

        // 5. 补充配件信息
        List<PdsProductManagerSubmitFittingValidateCmd> fittingList = buildSubmitFittingList(pi, cache);
        submitCmd.setFittingList(fittingList);

        submitCmd.setImportData(pi);
        return submitCmd;
    }

    /**
     * 补充配件信息
     *
     * @param pi 导入的原始数据
     * @param cache 缓存数据
     * @return PdsProductManagerSubmitFittingValidateCmd格式数据
     */
    private List<PdsProductManagerSubmitFittingValidateCmd> buildSubmitFittingList(PurchaseSkuImport pi, BuildSubmitCache cache) {
        List<PdsProductManagerSubmitFittingValidateCmd> fittingList = new ArrayList<>();

        // 配件1
        String fittingValidateSelected1 = pi.getFittingValidateSelected1();
        ExcelCheck.checkYesOrNo(fittingValidateSelected1, "配件1是否要填写");
        if (fittingValidateSelected1 != null && fittingValidateSelected1.equals("是")) {
            PdsProductManagerSubmitFittingValidateCmd fitting1 = new PdsProductManagerSubmitFittingValidateCmd();

            // 配件1：图片ID
            if(StringUtils.isNotBlank(pi.getImageIdFittingValidate1())){
                fitting1.setImageIdFittingValidate(pi.getImageIdFittingValidate1());
                cache.addProductCoverImageIdBasicList(pi.getImageIdFittingValidate1());
            }

            // 配件1：配件描述
            fitting1.setDescription(pi.getDescriptionFittingValidate1());
            // 配件1：数量
            fitting1.setQuantity(pi.getQuantityFittingValidate1());
            // 配件1：单位
            fitting1.setUnit(pi.getUnitFittingValidate1());
            // 配件1：适用国家代码集合，多个地区用逗号隔开
            String applicableRegionCodes = pi.getApplicableRegionsIdsFittingValidate1();
            if (applicableRegionCodes != null && !applicableRegionCodes.isEmpty()) {
                // 验证配置1的国家代码集合是否正确
                List<String> countryCodeList = Arrays.stream(applicableRegionCodes.split(",")).toList();
                for(String countryCode: countryCodeList){
                   cache.addCountryRegionCodeBasicList(countryCode);
                }
                fitting1.setCountryCodeList(countryCodeList);
            }
            fittingList.add(fitting1);
        }

        // 配件2
        String fittingValidateSelected2 = pi.getFittingValidateSelected2();
        ExcelCheck.checkYesOrNo(fittingValidateSelected2, "配件2是否要填写");
        if (fittingValidateSelected2 != null && fittingValidateSelected2.equals("是")) {
            PdsProductManagerSubmitFittingValidateCmd fitting2 = new PdsProductManagerSubmitFittingValidateCmd();

            // 配件2：图片ID
            if(StringUtils.isNotBlank(pi.getImageIdFittingValidate2())){
                fitting2.setImageIdFittingValidate(pi.getImageIdFittingValidate2());
                cache.addProductCoverImageIdBasicList(pi.getImageIdFittingValidate2());
            }

            // 配件2：配件描述
            fitting2.setDescription(pi.getDescriptionFittingValidate2());
            // 配件2：数量
            fitting2.setQuantity(pi.getQuantityFittingValidate2());
            // 配件2：单位
            fitting2.setUnit(pi.getUnitFittingValidate2());

            // 配件2：适用国家代码集合，多个地区用逗号隔开
            String applicableRegionCodes = pi.getApplicableRegionsIdsFittingValidate2();
            if (applicableRegionCodes != null && !applicableRegionCodes.isEmpty()) {
                // 验证配置2的国家代码集合是否正确
                List<String> countryCodeList = Arrays.stream(applicableRegionCodes.split(",")).toList();
                for(String countryCode: countryCodeList){
                    cache.addCountryRegionCodeBasicList(countryCode);
                }
                fitting2.setCountryCodeList(countryCodeList);
            }

            fittingList.add(fitting2);
        }

        // 配件3
        String fittingValidateSelected3 = pi.getFittingValidateSelected3();
        ExcelCheck.checkYesOrNo(fittingValidateSelected3, "配件3是否要填写");
        if (fittingValidateSelected3 != null && fittingValidateSelected3.equals("是")) {
            PdsProductManagerSubmitFittingValidateCmd fitting3 = new PdsProductManagerSubmitFittingValidateCmd();

            // 配件3：图片ID
            if(StringUtils.isNotBlank(pi.getImageIdFittingValidate3())){
                fitting3.setImageIdFittingValidate(pi.getImageIdFittingValidate3());
                cache.addProductCoverImageIdBasicList(pi.getImageIdFittingValidate3());
            }

            // 配件3：配件描述
            fitting3.setDescription(pi.getDescriptionFittingValidate3());
            // 配件3：数量
            fitting3.setQuantity(pi.getQuantityFittingValidate3());
            // 配件3：单位
            fitting3.setUnit(pi.getUnitFittingValidate3());

            // 配件3：适用国家代码集合，多个地区用逗号隔开
            String applicableRegionCodes = pi.getApplicableRegionsIdsFittingValidate3();
            if (applicableRegionCodes != null && !applicableRegionCodes.isEmpty()) {
                // 验证配置3的国家代码集合是否正确
                List<String> countryCodeList = Arrays.stream(applicableRegionCodes.split(",")).toList();
                for(String countryCode: countryCodeList){
                    cache.addCountryRegionCodeBasicList(countryCode);
                }
                fitting3.setCountryCodeList(countryCodeList);
            }
            fittingList.add(fitting3);
        }


        return fittingList;
    }

    /**
     * 补充物流信息
     *
     * @param pi 导入的原始数据
     * @return PdsProductManagerSubmitLogisticsCmd格式数据
     */
    private PdsProductManagerSubmitLogisticsCmd buildSubmitLogistics(PurchaseSkuImport pi) {
        PdsProductManagerSubmitLogisticsCmd logistics = new PdsProductManagerSubmitLogisticsCmd();

        // 材质（中文）
        logistics.setChineseMaterial(pi.getChineseMaterialLogistics());
        // 材质（英文）
        logistics.setEnglishMaterial(pi.getEnglishMaterialLogistics());
        // 用途
        logistics.setPurpose(pi.getPurposeLogistics());

        // 敏感属性(选中的),多个用`,`号隔开，说明: 0 锂电池、1 干电池、2 纯电、3 液体、4 凝胶、5 粉末、6 营养颗粒、7 活性炭，egg: 0,1
        List<Integer> hazardousProList = new ArrayList<>();

        //锂电池
        String lithiumBatterySelectedLogistics = pi.getLithiumBatterySelectedLogistics();
        if (lithiumBatterySelectedLogistics != null) {
            ExcelCheck.checkYesOrNo(lithiumBatterySelectedLogistics, "锂电池是否是敏感属性");
            if (lithiumBatterySelectedLogistics.equals("是")) {
                hazardousProList.add(0);

                // 锂电池属性 -> 锂电池名称
                logistics.setLithiumBatteryName(pi.getLithiumBatteryNameLogistics());
                // 锂电池型号
                logistics.setLithiumBatteryModel(pi.getLithiumBatteryModelLogistics());
                // 锂电池额定电压
                logistics.setLithiumRatedVoltage(pi.getLithiumRatedVoltageLogistics());
                // 锂电池额定能量
                logistics.setLithiumRatedEnergy(pi.getLithiumRatedEnergyLogistics());
                // 锂电池额定容量
                logistics.setLithiumRatedCapacity(pi.getLithiumRatedCapacityLogistics());
                // 锂电池质量
                logistics.setLithiumMass(pi.getLithiumMassLogistics());
            }
        }

        // 干电池
        String dryBatterySelectedLogistics = pi.getDryBatterySelectedLogistics();
        if (dryBatterySelectedLogistics != null) {
            ExcelCheck.checkYesOrNo(dryBatterySelectedLogistics, "干电池是否是敏感属性");
            if (dryBatterySelectedLogistics.equals("是")) {
                hazardousProList.add(1);

                // 干电池属性 -> 干电池名称
                logistics.setDryBatteryName(pi.getDryBatteryNameLogistics());
                // 干电池型号
                logistics.setDryBatteryModel(pi.getDryBatteryModelLogistics());
                // 干电池质量
                logistics.setDryMass(pi.getDryMassLogistics());
            }
        }

        // 纯电
        String pureElectricitySelectedLogistics = pi.getPureElectricitySelectedLogistics();
        if (pureElectricitySelectedLogistics != null) {
            ExcelCheck.checkYesOrNo(pureElectricitySelectedLogistics, "纯电是否是敏感属性");
            if (pureElectricitySelectedLogistics.equals("是")) {
                hazardousProList.add(2);

                // 纯电属性 -> 纯电名称
                logistics.setPureElectricityName(pi.getPureElectricityNameLogistics());
                // 纯电型号
                logistics.setPureElectricityModel(pi.getPureElectricityModelLogistics());
                // 纯电额定电压
                logistics.setPureRatedVoltage(pi.getPureRatedVoltageLogistics());
                // 纯电额定能量
                logistics.setPureRatedEnergy(pi.getPureRatedEnergyLogistics());
                // 纯电额定容量
                logistics.setPureRatedCapacity(pi.getPureRatedCapacityLogistics());
                // 纯电质量
                logistics.setPureMass(pi.getPureMassLogistics());
            }
        }

        // 液体
        String liquidSelectedLogistics = pi.getLiquidSelectedLogistics();
        if (liquidSelectedLogistics != null) {
            ExcelCheck.checkYesOrNo(liquidSelectedLogistics, "液体是否是敏感属性");
            if (liquidSelectedLogistics.equals("是")) {
                hazardousProList.add(3);

                // 液体属性 -> 液体名称
                logistics.setLiquidName(pi.getLiquidNameLogistics());
                // 液体容积 (ml)
                logistics.setLiquidVolumeMl(pi.getLiquidVolumeMlLogistics());
                // 液体其他信息
                logistics.setLiquidOther(pi.getLiquidOtherLogistics());
            }
        }

        // 凝胶
        String gelSelectedLogistics = pi.getGelSelectedLogistics();
        if (gelSelectedLogistics != null) {
            ExcelCheck.checkYesOrNo(gelSelectedLogistics, "凝胶是否是敏感属性");
            if (gelSelectedLogistics.equals("是")) {
                hazardousProList.add(4);

                // 凝胶属性 -> 凝胶名称
                logistics.setGelName(pi.getGelNameLogistics());
                // 凝胶容积 (ml)
                logistics.setGelVolumeMl(pi.getGelVolumeMlLogistics());
                // 凝胶其他信息
                logistics.setGelOther(pi.getGelOtherLogistics());
            }
        }

        // 粉末
        String powderSelectedLogistics = pi.getPowderSelectedLogistics();
        if (powderSelectedLogistics != null) {
            ExcelCheck.checkYesOrNo(powderSelectedLogistics, "粉末是否是敏感属性");
            if (powderSelectedLogistics.equals("是")) {
                hazardousProList.add(5);

                // 粉末属性 -> 粉末名称
                logistics.setPowderName(pi.getPowderNameLogistics());
                // 粉末质量
                logistics.setPowderMass(pi.getPowderMassLogistics());
                // 粉末其他信息
                logistics.setPowderOther(pi.getPowderOtherLogistics());
            }
        }

        // 营养颗粒
        String nutritionalPelletsSelectedLogistics = pi.getNutritionalPelletsSelectedLogistics();
        if (nutritionalPelletsSelectedLogistics != null) {
            ExcelCheck.checkYesOrNo(nutritionalPelletsSelectedLogistics, "营养颗粒是否是敏感属性");
            if (nutritionalPelletsSelectedLogistics.equals("是")) {
                hazardousProList.add(6);

                // 营养颗粒属性 -> 营养颗粒名称
                logistics.setNutritionalPelletsName(pi.getNutritionalPelletsNameLogistics());
                // 营养颗粒质量
                logistics.setNutritionalPelletsMass(pi.getNutritionalPelletsMassLogistics());
                // 营养颗粒其他信息
                logistics.setNutritionalPelletsOther(pi.getNutritionalPelletsOtherLogistics());
            }
        }

        // 活性炭
        String activatedCarbonSelectedLogistics = pi.getActivatedCarbonSelectedLogistics();
        if (activatedCarbonSelectedLogistics != null) {
            ExcelCheck.checkYesOrNo(activatedCarbonSelectedLogistics, "活性炭是否是敏感属性");
            if (activatedCarbonSelectedLogistics.equals("是")) {
                hazardousProList.add(7);

                // 活性炭属性 -> 活性炭名称
                logistics.setActivatedCarbonName(pi.getActivatedCarbonNameLogistics());
                // 活性炭质量
                logistics.setActivatedCarbonMass(pi.getActivatedCarbonMassLogistics());
                // 活性炭其他信息
                logistics.setActivatedCarbonOther(pi.getActivatedCarbonOtherLogistics());
            }
        }

        // 其它信息
        List<String> otherInformationList = pi.analyzedOtherInformationListLogistics();
        logistics.setOtherInformationList(otherInformationList);
        logistics.setHazardousPros(StrUtil.join(",", hazardousProList));
        return logistics;
    }

    /**
     * 属性信息-外箱规格
     *
     * @param pi 导入的原始数据
     * @return PdsProductManagerAddProsBoxCmd格式数据
     */
    private PdsProductManagerSubmitProsBoxCmd buildSubmitBoxCmd(PurchaseSkuImport pi) {
        PdsProductManagerSubmitProsBoxCmd box = new PdsProductManagerSubmitProsBoxCmd();
        // 箱规名称
        box.setBoxSpecificationName(pi.getBoxSpecificationNameProsBox());
        // 装箱数量
        box.setNumberOfUnitsPerBox(pi.getNumberOfUnitsPerBoxProsBox());
        // 外箱尺寸-长 (cm)/公制
        box.setBoxLengthMetric(pi.getBoxLengthMetricProsBox());
        // 外箱尺寸-宽 (cm)/公制
        box.setBoxWidthMetric(pi.getBoxWidthMetricProsBox());
        // 外箱尺寸-高 (cm)/公制
        box.setBoxHeightMetric(pi.getBoxHeightMetricProsBox());
        // 整箱毛重 (kg)/公制
        box.setGrossWeightPerBoxMetric(pi.getBoxHeightMetricProsBox());
        // 默认激活
        box.setActive(CommonConstants.ACTIVE);

        return box;
    }

    /**
     * 补充产品管理-属性信息
     *
     * @param pi 导入的原始数据
     * @param cache 缓存数据
     * @return PdsProductManagerSubmitProsCmd格式数据
     */
    private PdsProductManagerSubmitProsCmd buildSubmitProsCmd(PurchaseSkuImport pi, BuildSubmitCache cache) {
        PdsProductManagerSubmitProsCmd pros = new PdsProductManagerSubmitProsCmd();
        // 单品尺寸-长 (cm)/公制
        pros.setProductLengthMetric(pi.getProductLengthMetricPros());
        // 单品尺寸-宽 (cm)/公制
        pros.setProductWidthMetric(pi.getProductWidthMetricPros());
        // 单品尺寸-高 (cm)/公制
        pros.setProductHeightMetric(pi.getProductHeightMetricPros());
        // 单品净重 (kg)/公制
        pros.setProductWeightMetric(pi.getProductWeightMetricPros());
        // 单品数量
        pros.setProductQuantity(pi.getProductQuantityPros());
        // 单品备注
        pros.setProductRemarks(pi.getProductRemarksPros());
        // 包装尺寸-长 (cm)/公制
        pros.setPackagingLengthMetric(pi.getPackagingLengthMetricPros());
        // 包装尺寸-宽 (cm)/公制
        pros.setPackagingWidthMetric(pi.getPackagingWidthMetricPros());
        // 包装尺寸-高 (cm)/公制
        pros.setPackagingHeightMetric(pi.getPackagingHeightMetricPros());
        // 包装净重 (kg)/公制
        pros.setGrossWeightMetric(pi.getGrossWeightMetricPros());

        // 产品单位
        if (StringUtils.isNotBlank(pi.getProductUnitPros())) {
            pros.setProductUnitPros(pi.getProductUnitPros());
            cache.addProductUnitList(pi.getProductUnitPros());
        }

        return pros;
    }

    /**
     * 补充产品管理-基础信息
     *
     * @param pi 导入的原始数据
     * @param cache 缓存数据
     * @return PdsProductManagerSubmitBasicCmd格式数据
     */
    private PdsProductManagerSubmitBasicCmd buildSubmitCmd(PurchaseSkuImport pi, BuildSubmitCache cache) {
        // 1. 验证basic信息
        PdsProductManagerSubmitBasicCmd basic = new PdsProductManagerSubmitBasicCmd();

        // 封面图片
        String imageUrl = pi.getProductCoverImageIdBasic();
        if (StringUtils.isNotBlank(imageUrl)) {
            if (!ImageUrlValidator.isValidHttpImageUrl(imageUrl)) {
                throw new RuntimeException("非法的 HTTP 图片链接");
            }
            basic.setProductCoverImageIdBasic(imageUrl);
            cache.addProductCoverImageIdBasicList(imageUrl);
        }

        // 验证国家代码
        String countryRegionCodeBasic = pi.getCountryRegionCodeBasic();
        if (StringUtils.isNotBlank(countryRegionCodeBasic)) {
            basic.setCountryRegionCodeBasic(countryRegionCodeBasic);
            cache.addCountryRegionCodeBasicList(countryRegionCodeBasic);
        }

        // 验证颜色
        String colorCodeBasic = pi.getColorCodeBasic();
        if (StringUtils.isNotBlank(colorCodeBasic)) {
            basic.setColorCodeBasic(colorCodeBasic);
            cache.addColorCodeBasicList(colorCodeBasic);
        }

        // 产品型号、品牌、品类
        String modelNo = pi.getProductModelNoBasic();
        if (StringUtils.isNotBlank(modelNo)) {
            basic.setProductModelNoBasic(modelNo);
            cache.addProductModelNoBasicList(modelNo);
        }

        // // 销售渠道
        // String salesChannelCode = pi.getSalesChannelCodeBasic();
        // if (StringUtils.isNotBlank(salesChannelCode)) {
        //     basic.setSalesChannelCodeBasic(salesChannelCode);
        //     cache.addSalesChannelCodeBasicList(salesChannelCode);
        // }

        // 渠道类型
        String channelTypeBasic = pi.getChannelTypeBasic();
        if (StringUtils.isNotBlank(channelTypeBasic)) {
            basic.setChannelTypeBasic(channelTypeBasic);
        }

        // 产品描述
        List<String> productDescriptionList = pi.analyzedProductDescriptionListBasic();
        basic.setProductDescriptionList(productDescriptionList);

        // 产品卖点
        List<String> productSellingPointsList = pi.analyzedProductSellingPointsListBasic();
        basic.setProductSellingPointsList(productSellingPointsList);

        // 产品负责人工号
        String productManagerCode = pi.getProductManagerCodeBasic();
        if (StringUtils.isNotBlank(productManagerCode)) {
            basic.setProductManagerCodeBasic(productManagerCode);
            cache.addProductManagerCodeBasicList(productManagerCode);
        }
        // 商务负责人工号
        String businessManagerCode = pi.getBusinessManagerCodeBasic();
        if (StringUtils.isNotBlank(businessManagerCode)) {
            basic.setBusinessManagerCodeBasic(businessManagerCode);
            cache.addBusinessManagerCodeBasicList(businessManagerCode);
        }
        // 采购负责人工号
        String procurementManagerCode = pi.getProcurementManagerCodeBasic();
        if (StringUtils.isNotBlank(procurementManagerCode)) {
            basic.setProcurementManagerCodeBasic(procurementManagerCode);
            cache.addProcurementManagerCodeBasicList(procurementManagerCode);
        }
        // 计划负责人工号
        String planningManagerCode = pi.getPlanningManagerCodeBasic();
        if (StringUtils.isNotBlank(planningManagerCode)) {
            basic.setPlanningManagerCodeBasic(planningManagerCode);
            cache.addPlanningManagerCodeBasicList(planningManagerCode);
        }
        // 合规负责人工号
        String complianceManagerCode = pi.getComplianceManagerCodeBasic();
        if (StringUtils.isNotBlank(complianceManagerCode)) {
            basic.setComplianceManagerCodeBasic(complianceManagerCode);
            cache.addComplianceManagerCodeBasicList(complianceManagerCode);
        }

        // 包材负责人工号
        String packingMaterialManagerCode = pi.getPackingMaterialManagerCodeBasic();
        if (StringUtils.isNotBlank(packingMaterialManagerCode)) {
            basic.setPackingMaterialManagerCodeBasic(packingMaterialManagerCode);
            cache.addPackingMaterialManagerCodeBasicList(packingMaterialManagerCode);
        }

        // 产品类型
        if (StringUtils.isNotBlank(pi.getProductTypeBasic())) {
            basic.setProductTypeBasic(pi.getProductTypeBasic());
            cache.addProductTypeBasicList(pi.getProductTypeBasic());
        }

        // 是否含适配器
        String adapterIncluded = pi.getAdapterIncludedBasic();
        if (StringUtils.isNotBlank(adapterIncluded)) {
            ExcelCheck.checkYesOrNo(adapterIncluded, "是否含适配器");
            if (adapterIncluded.equals("是")) {
                basic.setAdapterIncluded(1);
            } else if (adapterIncluded.equals("否")) {
                basic.setAdapterIncluded(0);
            }
        } else {
            throw new RuntimeException("是否含适配器不能为空");
        }

        // 其它，普通数据格式校验
        basic.setVersion(pi.getVersionBasic());
        basic.setAttributeEncoding(pi.getAttributeEncodingBasic());
        basic.setChineseName(pi.getChineseNameBasic());
        basic.setEnglishName(pi.getEnglishNameBasic());
        basic.setMarketName(pi.getMarketNameBasic());
        basic.setRemark(pi.getRemarkBasic());
        // 采购SKU
        basic.setPurchaseSku(pi.getPurchaseSkuBasic());
        return basic;
    }

    /**
     * 验证新增数据格式是否符合规范
     *
     * @param language  语言
     * @param submitCmd 数据
     */
    public void verifyDataAdd(LanguageEnum language, PdsProductManagerSubmitCmd submitCmd) {
        // 数据格式校验
        submitCmd.validateFields(language);

        // 基础信息-字典检验
        PdsProductManagerSubmitBasicCmd basic = submitCmd.getBasic();
        if (CommonConstants.ACTION_TYPE_SUBMIT.equals(submitCmd.getStatus())) {
            transService.transOne(basic);
            basic.checkDict(language);
        }

        // 属性信息-字典检验 跟 业务逻辑限制
        PdsProductManagerSubmitProsCmd pros = submitCmd.getPros();
        if (CommonConstants.ACTION_TYPE_SUBMIT.equals(submitCmd.getStatus())) {
            transService.transOne(pros);
            pros.checkDict(language);
        }
        pros.validateDimensions(language);
        pros.validateWeights(language);

        // 外箱规格-校验, 业务逻辑限制
        List<PdsProductManagerSubmitProsBoxCmd> boxList = pros.getBoxList();
        if (boxList != null) {
            for (PdsProductManagerSubmitProsBoxCmd box : boxList) {
                box.validateDimensions(language);
            }
        }
    }

    /**
     * 验证编辑数据格式是否符合规范
     *
     * @param language 语言
     * @param editCmd  数据
     */
    public void verifyDataEdit(LanguageEnum language, PdsProductManagerUpdateCmd editCmd) {
        Integer reviewStatusHis = null;
        if(editCmd.getBasic()!=null && editCmd.getBasic().getId()!=null){
            reviewStatusHis = basicMapper.selectReviewStatus(editCmd.getBasic().getId());
            if (reviewStatusHis == null) {
                throw new ErrorCodeException(PdsErrorCode.PRODUCT_MANAGER_REVIEW_STATUS_NOT_EXIST);
            }
        }

        // 数据格式校验
        editCmd.validateFields(language, reviewStatusHis);

        // 基础信息-字典检验
        PdsProductManagerUpdateBasicCmd basic = editCmd.getBasic();
        if (CommonConstants.ACTION_TYPE_SUBMIT.equals(editCmd.getStatus())) {
            transService.transOne(basic);
            basic.checkDict(language);
        }

        // 属性信息-字典检验 跟 业务逻辑限制
        PdsProductManagerUpdateProsCmd pros = editCmd.getPros();
        if (CommonConstants.ACTION_TYPE_SUBMIT.equals(editCmd.getStatus())) {
            transService.transOne(pros);
            pros.checkDict(language);
        }
        pros.validateDimensions(language);
        pros.validateWeights(language);

        // 外箱规格-校验, 业务逻辑限制
        List<PdsProductManagerUpdateProsBoxCmd> boxList = pros.getBoxList();
        if (boxList != null) {
            for (PdsProductManagerUpdateProsBoxCmd box : boxList) {
                box.validateDimensions(language);
            }
        }
    }


    /**
     * 获取基本信息-封面图片
     *
     * @param id 产品管理主键ID
     */
    public FileDetailResponse getBasicInfoImage(Integer id) {
        PdsProductManagerBasicPo basicPo = basicMapper.selectById(id);
        if (basicPo != null) {
            String productCoverImageId = basicPo.getProductCoverImageId();
            return pdsCommonSrv.getFileInfo(productCoverImageId);
        }
        return null;
    }

    /**
     * 获取基本信息
     *
     * @param id 产品管理主键ID
     */
    public PdsProductManagerBasicPo getBasicById(Integer id) {
        return basicMapper.selectById(id);
    }

    /**
     * 验证SKU是否重复
     *
     * @param req 验证SKU请求
     */
    public boolean verifySku(PdsProductManagerVerifySkuRequest req) {
        // TODO 后端暂时先验证唯一性,后续后端也需要生成这个sku，跟前端的匹配
        List<PdsProductManagerBasicPo> basicPoList = basicMapper.selectList(new LambdaQueryWrapper<PdsProductManagerBasicPo>()
                .eq(PdsProductManagerBasicPo::getPurchaseSku, req.getPurchaseSku()));
        if (basicPoList == null || basicPoList.isEmpty()) {
            return true;
        }
        return false;
    }

    /**
     * 模拟审批
     *
     * @param id           主键ID
     * @param reviewStatus 审批状态
     * @return 是否成功
     */
    public Boolean simulatedApproval(Integer id, Integer reviewStatus) {
        PdsManagerReviewStatus status = PdsManagerReviewStatus.getEnum(reviewStatus);
        if (status == null) {
            throw new ErrorCodeException(PdsErrorCode.PRODUCT_MANAGER_REVIEW_STATUS_NOT_EXIST);
        }

        PdsProductManagerBasicPo hisPo = basicMapper.selectById(id);
        if (hisPo == null) {
            throw new ErrorCodeException(PdsErrorCode.PRODUCT_MANAGER_ID_NOT_EXIST);
        }

        PdsProductManagerBasicPo po = new PdsProductManagerBasicPo();
        po.setId(id);
        po.setReviewStatus(reviewStatus);
        if (PdsManagerReviewStatus.APPROVED == status) {
            po.setReviewTime(LocalDateTime.now());
            hisPo.setReviewTime(po.getReviewTime());
            hisPo.setUpdateTime(LocalDateTime.now());
            hisPo.setReviewStatus(po.getReviewStatus());
        }

        boolean result = basicMapper.updateById(po) > 0;
        if (result && PdsManagerReviewStatus.APPROVED == status) {
            // TODO 审批通过，需要同步信息到srm的产品商务管理
            ProductManagerBasicRequest requestMq = PdsProductManagerPoConverter.INSTANCE.toRequestMq(hisPo);
            pdsSyncBmProducer.send(requestMq);
        }
        return result;
    }


    /**
     * 同步srm的产品商务管理数据
     * @param request 产品商务管理数据
     * @param tag 标签
     */
    public void syncProductManager(ProductBusinessManagerRequest request, String tag) {
        try{
            // 产品状态/补货状态字典
            List<Dict.ParamItem> productStatusItemList = pdsCommonSrv.getDict("product_status");
            if(request==null){
                return;
            }
            if(request.getProductManagerId()!=null){
                PdsProductManagerBasicPo basicPo = this.getBasicById(request.getProductManagerId());
                if(basicPo!=null){
                    boolean contain = pdsCommonSrv.containDictKey(request.getReplenishmentStatus(), productStatusItemList);
                    if (contain) {
                        PdsProductManagerBasicPo po = new PdsProductManagerBasicPo();
                        po.setId(basicPo.getId());
                        po.setUpdateTime(LocalDateTime.now());
                        po.setProductStatus(Integer.parseInt(request.getReplenishmentStatus()));

                        basicMapper.updateByPrimaryKey(po);
                        log.info("消费产品商务管理数据成功，tag={},srmReqSuccess={}", tag, 1);
                    } else {
                        throw new RuntimeException("补货状态不存在: " + request.getReplenishmentStatus());
                    }
                }
            }
        }catch (Exception e){
            log.error("同步srm的产品商务管理数据失败，tag={},request={}", tag, request, e);
        }
    }

    /**
     * 查询产品管理，传入用户ID数组，输出匹配负责人信息的psku
     * @param request 请求参数
     * @return psku列表
     */
    public R<PdsProductManagerSkuQueryViewResponse> viewPskuList(PdsProductManagerSkuQueryViewRequest request) {
        PdsProductManagerSkuQueryViewResponse response = new PdsProductManagerSkuQueryViewResponse();

        if (request == null) {
            response.setPskuList(Collections.emptyList());
            return R.success(response);
        }

        // 构造查询条件
        LambdaQueryWrapper<PdsProductManagerBasicPo> queryWrapper = Wrappers.lambdaQuery();

        // 如果 idList 不为空，才添加相关条件
        if (request.getIdList() != null && !request.getIdList().isEmpty()) {
            queryWrapper.and(wrapper -> wrapper
                    .in(PdsProductManagerBasicPo::getProductManagerId, request.getIdList())
                    .or().in(PdsProductManagerBasicPo::getBusinessManagerId, request.getIdList())
                    .or().in(PdsProductManagerBasicPo::getProcurementManagerId, request.getIdList())
                    .or().in(PdsProductManagerBasicPo::getPlanningManagerId, request.getIdList())
                    .or().in(PdsProductManagerBasicPo::getComplianceManagerId, request.getIdList())
                    .or().in(PdsProductManagerBasicPo::getPackingMaterialManagerId, request.getIdList())
            );
        }

        // 始终添加的固定条件
        queryWrapper
                .eq(PdsProductManagerBasicPo::getReviewStatus, PdsManagerReviewStatus.APPROVED.getCode())
                .eq(PdsProductManagerBasicPo::getDeleted, CommonConstants.NOT_DELETE);

        // 执行查询
        List<PdsProductManagerBasicPo> productList = basicMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(productList)) {
            response.setPskuList(Collections.emptyList());
            return R.success(response);
        }

        // 提取 purchaseSku
        List<String> pskuList =  productList.stream()
                .map(PdsProductManagerBasicPo::getPurchaseSku)
                .distinct()
                .toList();
        response.setPskuList(pskuList);

        return R.success(response);
    }

}
