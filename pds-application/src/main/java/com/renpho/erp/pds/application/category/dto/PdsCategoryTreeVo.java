package com.renpho.erp.pds.application.category.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 品类导出格式.
 * <AUTHOR>
 * @since 2024.9.20
 */
@Data
public class PdsCategoryTreeVo {

    /** 1级品类名称 */
    @ExcelProperty(value = "excel.category.name.level_one", index = 0)
    private String nameLevelOne;

    /** 1级品类编码 */
    @ExcelProperty(value = "excel.category.code.level_one", index = 1)
    private String codeLevelOne;

    /** 2级品类名称 */
    @ExcelProperty(value = "excel.category.name.level_two", index = 2)
    private String nameLevelTwo;

    /** 2级品类编码 */
    @ExcelProperty(value = "excel.category.code.level_two", index = 3)
    private String codeLevelTwo;

    /** 3级品类名称 */
    @ExcelProperty(value = "excel.category.name.level_three", index = 4)
    private String nameLevelThree;

    /** 3级品类编码 */
    @ExcelProperty(value = "excel.category.code.level_three", index = 5)
    private String codeLevelThree;

    /** 备注 */
    @ExcelProperty(value = "excel.category.remark", index = 6)
    private String remark;

    /** 状态 */
    @ExcelProperty(value = "excel.category.status", index = 7)
    private Integer status;

}
