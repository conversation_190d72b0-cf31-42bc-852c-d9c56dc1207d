package com.renpho.erp.pds.application.common.oplog.model;

import lombok.Data;

/**
 * 产品管理-授权-导入-日志.
 *
 * <AUTHOR>
 * @since 2024.12.05
 */
@Data
public class ProductManagerAuthorizeModel {

    /**
     * 产品管理主键ID
     */
    private Integer productManagerId;

    /**
     * 授权ID
     */
    private Integer authorizeId;

    public ProductManagerAuthorizeModel(Integer authorizeId, Integer productManagerId){
        this.authorizeId = authorizeId;
        this.productManagerId = productManagerId;
    }

}
