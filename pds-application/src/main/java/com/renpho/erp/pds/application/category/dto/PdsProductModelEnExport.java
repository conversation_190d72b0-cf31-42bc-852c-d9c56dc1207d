package com.renpho.erp.pds.application.category.dto;

import com.renpho.erp.pds.infrastructure.utils.poi.Excel;
import lombok.Data;

/**
 * 产品型号.
 */
@Data
public class PdsProductModelEnExport {
    @Excel(name = "Prod. mode")
    private String modelNo;

    @Excel(name = "Cat. code")
    private String brandName;

    @Excel(name = "Category")
    private String cateName;

    @Excel(name = "PM")
    private String pmUserName;

    @Excel(name = "Oper.")
    private String updateByName;

    @Excel(name = "Updated")
    private String updateTime;
}
