package com.renpho.erp.pds.application.product.manager.dto;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.jmolecules.ddd.types.AggregateRoot;
import org.jmolecules.ddd.types.Identifiable;
import org.jmolecules.ddd.types.Identifier;

import java.util.List;


@Data
public class PdsProductManagerUpdateRequest implements AggregateRoot<PdsProductManagerUpdateRequest, PdsProductManagerUpdateRequest.PdsProductManagerID> {
	/** 基本信息 */
	private PdsProductManagerUpdateBasicRequest basic;

	/** 属性信息 */
	private PdsProductManagerUpdateProsRequest pros;

	/** 物流信息 */
	private PdsProductManagerUpdateLogisticsRequest logistics;

	/** 配置信息 */
	private List<PdsProductManagerUpdateFittingRequest> fittingList;

	private PdsProductManagerID id;

	public PdsProductManagerUpdateRequest() {

	}

	@RequiredArgsConstructor(staticName = "of")
	public static class PdsProductManagerID implements Identifiable<Integer>, Identifier {
		private final Integer id;

		@Override
		public Integer getId() {
			return id;
		}
	}
}
