package com.renpho.erp.pds.application.product.manager.dto;

import lombok.Data;


@Data
public class PdsProductManagerUpdateProsBoxRequest {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 箱规名称
     */
    private String boxSpecificationName;

    /**
     * 装箱数量
     */
    private Integer numberOfUnitsPerBox;

    /**
     * 外箱尺寸-长 (cm)/公制
     */
    private Double boxLengthMetric;

    /**
     * 外箱尺寸-宽 (cm)/公制
     */
    private Double boxWidthMetric;

    /**
     * 外箱尺寸-高 (cm)/公制
     */
    private Double boxHeightMetric;

    /**
     * 整箱毛重 (kg)/公制
     */
    private Double grossWeightPerBoxMetric;

    /**
     * 是否为生效外箱规格: 0=InActive，1=Active
     */
    private Integer active;
}