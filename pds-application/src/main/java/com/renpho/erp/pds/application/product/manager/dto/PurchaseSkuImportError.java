package com.renpho.erp.pds.application.product.manager.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.renpho.erp.pds.infrastructure.utils.poi.Excel;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 产品管理-采购SKU导入-错误信息
 *
 * @since 2024/11/08
 */
@Data
public class PurchaseSkuImportError {

    // 基础信息
    /**
     * 产品封面图片链接
     */
    @ExcelProperty(value = "excel.product_cover_image_id", index = 0)
    private String productCoverImageIdBasic;

    /**
     * 国家Code
     */
    @ExcelProperty(value = "excel.country_region_code", index = 1)
    private String countryRegionCodeBasic;

    /**
     * 产品型号
     */
    @ExcelProperty(value = "excel.product_model_no", index = 2)
    private String productModelNoBasic;

    /**
     * 颜色Code
     */
    @ExcelProperty(value = "excel.color_code", index = 3)
    private String colorCodeBasic;

    // /**
    //  * 销售渠道Code
    //  */
    // @ExcelProperty(value = "excel.sales_channel_code", index = 4)
    // private String salesChannelCodeBasic;
    /**
     * 销售渠道Code
     */
    @ExcelProperty(value = "excel.sales_channel_type", index = 4)
    private String salesChannelCodeBasic;

    /**
     * 版本编号
     */
    @ExcelProperty(value = "excel.version", index = 5)
    private String versionBasic;

    /**
     * 采购SKU
     */
    @ExcelProperty(value = "excel.purchase_sku", index = 6)
    private String purchaseSkuBasic;

    /**
     * 属性编码
     */
    @ExcelProperty(value = "excel.attribute_encoding", index = 7)
    private String attributeEncodingBasic;

    /**
     * 中文名称
     */
    @ExcelProperty(value = "excel.chinese_name", index = 8)
    private String chineseNameBasic;

    /**
     * 英文名称
     */
    @ExcelProperty(value = "excel.english_name", index = 9)
    private String englishNameBasic;

    /**
     * 市场名称
     */
    @ExcelProperty(value = "excel.market_name", index = 10)
    private String marketNameBasic;

    /**
     * 产品描述1
     */
    @ContentStyle(wrapped = BooleanEnum.TRUE)
    @ExcelProperty(value = "excel.product_description", index = 11)
    private String productDescriptionBasic;

    /**
     * 产品卖点1
     */
    @ContentStyle(wrapped = BooleanEnum.TRUE)
    @ExcelProperty(value = "excel.product_selling_points", index = 12)
    private String productSellingPointsBasic;

    /**
     * 备注
     */
    @ExcelProperty(value = "excel.remark", index = 13)
    private String remarkBasic;

    /**
     * 产品负责人工号
     */
    @ExcelProperty(value = "excel.product_manager_code", index = 14)
    private String productManagerCodeBasic;

    /**
     * 商务负责人工号
     */
    @ExcelProperty(value = "excel.business_manager_code", index = 15)
    private String businessManagerCodeBasic;

    /**
     * 采购负责人工号
     */
    @ExcelProperty(value = "excel.procurement_manager_code", index = 16)
    private String procurementManagerCodeBasic;

    /**
     * 计划负责人工号
     */
    @ExcelProperty(value = "excel.planning_manager_code", index = 17)
    private String planningManagerCodeBasic;

    /**
     * 合规负责人工号
     */
    @ExcelProperty(value = "excel.compliance_manager_code", index = 18)
    private String complianceManagerCodeBasic;

    /**
     * 包材负责人工号
     */
    @ExcelProperty(value = "excel.packing_material_manager_code", index = 99)
    private String packingMaterialManagerCodeBasic;

    /**
     * 产品类型, 见字典 product_type
     */
    @ExcelProperty(value = "excel.product_type", index = 19)
    private String productTypeBasic;

    // 属性信息
    /**
     * 单品尺寸-长 (cm)/公制
     */
    @ExcelProperty(value = "excel.product_length_metric", index = 20)
    private Double productLengthMetricPros;

    /**
     * 单品尺寸-宽 (cm)/公制
     */
    @ExcelProperty(value = "excel.product_width_metric", index = 21)
    private Double productWidthMetricPros;

    /**
     * 单品尺寸-高 (cm)/公制
     */
    @ExcelProperty(value = "excel.product_height_metric", index = 22)
    private Double productHeightMetricPros;

    /**
     * 单品净重 (kg)/公制
     */
    @ExcelProperty(value = "excel.product_weight_metric", index = 23)
    private Double productWeightMetricPros;

    /**
     * 产品数量
     */
    @ExcelProperty(value = "excel.product_quantity", index = 24)
    private Integer productQuantityPros;

    /**
     * 单品/产品备注
     */
    @ExcelProperty(value = "excel.product_remarks", index = 25)
    private String productRemarksPros;

    /**
     * 包装尺寸(即彩盒尺寸)-长 (cm)/公制
     */
    @ExcelProperty(value = "excel.packaging_length_metric", index = 26)
    private Double packagingLengthMetricPros;

    /**
     * 包装尺寸(即彩盒尺寸)-宽 (cm)/公制
     */
    @ExcelProperty(value = "excel.packaging_width_metric", index = 27)
    private Double packagingWidthMetricPros;

    /**
     * 包装尺寸(即彩盒尺寸)-高 (cm)/公制
     */
    @ExcelProperty(value = "excel.packaging_height_metric", index = 28)
    private Double packagingHeightMetricPros;

    /**
     * 单品毛重 (kg)/公制
     */
    @ExcelProperty(value = "excel.gross_weight_metric", index = 29)
    private Double grossWeightMetricPros;

    /**
     * 产品单位,字典 product_unit
     */
    @ExcelProperty(value = "excel.product_unit", index = 30)
    private String productUnitPros;


    //属性-外箱规格
    /**
     * 箱规名称
     */
    @ExcelProperty(value = "excel.box_specification_name", index = 31)
    private String boxSpecificationNameProsBox;

    /**
     * 装箱数量
     */
    @ExcelProperty(value = "excel.number_of_units_per_box", index = 32)
    private Integer numberOfUnitsPerBoxProsBox;

    /**
     * 外箱尺寸-长 (cm)/公制
     */
    @ExcelProperty(value = "excel.box_length_metric", index = 33)
    private Double boxLengthMetricProsBox;

    /**
     * 外箱尺寸-宽 (cm)/公制
     */
    @ExcelProperty(value = "excel.box_width_metric", index = 34)
    private Double boxWidthMetricProsBox;

    /**
     * 外箱尺寸-高 (cm)/公制
     */
    @ExcelProperty(value = "excel.box_height_metric", index = 35)
    private Double boxHeightMetricProsBox;

    /**
     * 整箱毛重 (kg)/公制
     */
    @ExcelProperty(value = "excel.gross_weight_per_box_metric", index = 36)
    private Double grossWeightPerBoxMetricProsBox;


    //物流信息
    /**
     * 材质（中文）
     */
    @ExcelProperty(value = "excel.chinese_material", index = 37)
    private String chineseMaterialLogistics;

    /**
     * 材质（英文）
     */
    @ExcelProperty(value = "excel.english_material", index = 38)
    private String englishMaterialLogistics;

    /**
     * 用途
     */
    @ExcelProperty(value = "excel.purpose", index = 39)
    private String purposeLogistics;

    /**
     * 锂电池是否选中: 是、否
     */
    @ExcelProperty(value = "excel.lithium_battery_selected", index = 40)
    private String lithiumBatterySelectedLogistics;

    /**
     * 锂电池名称
     */
    @ExcelProperty(value = "excel.lithium_battery_name", index = 41)
    private String lithiumBatteryNameLogistics;

    /**
     * 锂电池型号
     */
    @ExcelProperty(value = "excel.lithium_battery_model", index = 42)
    private String lithiumBatteryModelLogistics;

    /**
     * 锂电池额定电压
     */
    @ExcelProperty(value = "excel.lithium_rated_voltage", index = 43)
    private String lithiumRatedVoltageLogistics;

    /**
     * 锂电池额定容量
     */
    @ExcelProperty(value = "excel.lithium_rated_capacity", index = 44)
    private String lithiumRatedCapacityLogistics;

    /**
     * 锂电池额定能量
     */
    @ExcelProperty(value = "excel.lithium_rated_energy", index = 45)
    private String lithiumRatedEnergyLogistics;

    /**
     * 锂电池质量
     */
    @ExcelProperty(value = "excel.lithium_mass", index = 46)
    private String lithiumMassLogistics;

    /**
     * 纯电电池是否选中: 是、否
     */
    @ExcelProperty(value = "excel.pure_electricity_selected", index = 47)
    private String pureElectricitySelectedLogistics;

    /**
     * 纯电电池名称
     */
    @ExcelProperty(value = "excel.pure_electricity_name", index = 48)
    private String pureElectricityNameLogistics;

    /**
     * 纯电电池型号
     */
    @ExcelProperty(value = "excel.pure_electricity_model", index = 49)
    private String pureElectricityModelLogistics;

    /**
     * 纯电额定电压
     */
    @ExcelProperty(value = "excel.pure_rated_voltage", index = 50)
    private String pureRatedVoltageLogistics;

    /**
     * 纯电额定容量
     */
    @ExcelProperty(value = "excel.pure_rated_capacity", index = 51)
    private String pureRatedCapacityLogistics;

    /**
     * 纯电额定能量
     */
    @ExcelProperty(value = "excel.pure_rated_energy", index = 52)
    private String pureRatedEnergyLogistics;

    /**
     * 纯电池质量
     */
    @ExcelProperty(value = "excel.pure_mass", index = 53)
    private String pureMassLogistics;

    /**
     * 干电池是否选中: 是、否
     */
    @ExcelProperty(value = "excel.dry_battery_selected", index = 54)
    private String dryBatterySelectedLogistics;

    /**
     * 干电池名称
     */
    @ExcelProperty(value = "excel.dry_battery_name", index = 55)
    private String dryBatteryNameLogistics;

    /**
     * 干电池型号
     */
    @ExcelProperty(value = "excel.dry_battery_model", index = 56)
    private String dryBatteryModelLogistics;

    /**
     * 干电池质量
     */
    @ExcelProperty(value = "excel.dry_mass", index = 57)
    private String dryMassLogistics;

    /**
     * 液体是否选中: 0否，1是
     */
    @ExcelProperty(value = "excel.liquid_selected", index = 58)
    private String liquidSelectedLogistics;

    /**
     * 液体名称
     */
    @ExcelProperty(value = "excel.liquid_name", index = 59)
    private String liquidNameLogistics;

    /**
     * 液体容积 (ml)
     */
    @ExcelProperty(value = "excel.liquid_volume_ml", index = 60)
    private String liquidVolumeMlLogistics;

    /**
     * 液体其他信息
     */
    @ExcelProperty(value = "excel.liquid_other", index = 61)
    private String liquidOtherLogistics;

    /**
     * 凝胶是否选中: 是、否
     */
    @ExcelProperty(value = "excel.gel_selected", index = 62)
    private String gelSelectedLogistics;

    /**
     * 凝胶名称
     */
    @ExcelProperty(value = "excel.gel_name", index = 63)
    private String gelNameLogistics;

    /**
     * 凝胶容积 (ml)
     */
    @ExcelProperty(value = "excel.gel_volume_ml", index = 64)
    private String gelVolumeMlLogistics;

    /**
     * 凝胶其他信息
     */
    @ExcelProperty(value = "excel.gel_other", index = 65)
    private String gelOtherLogistics;

    /**
     * 粉末是否选中: 是、否
     */
    @ExcelProperty(value = "excel.powder_selected", index = 66)
    private String powderSelectedLogistics;

    /**
     * 粉末名称
     */
    @ExcelProperty(value = "excel.powder_name", index = 67)
    private String powderNameLogistics;

    /**
     * 粉末质量
     */
    @ExcelProperty(value = "excel.powder_mass", index = 68)
    private String powderMassLogistics;

    /**
     * 粉末其他信息
     */
    @ExcelProperty(value = "excel.powder_other", index = 69)
    private String powderOtherLogistics;

    /**
     * 营养颗粒是否选中: 是、否
     */
    @ExcelProperty(value = "excel.nutritional_pellets_selected", index = 70)
    private String nutritionalPelletsSelectedLogistics;

    /**
     * 营养颗粒名称
     */
    @ExcelProperty(value = "excel.nutritional_pellets_name", index = 71)
    private String nutritionalPelletsNameLogistics;

    /**
     * 营养颗粒质量
     */
    @ExcelProperty(value = "excel.nutritional_pellets_mass", index = 72)
    private String nutritionalPelletsMassLogistics;

    /**
     * 营养颗粒其他信息
     */
    @ExcelProperty(value = "excel.nutritional_pellets_other", index = 73)
    private String nutritionalPelletsOtherLogistics;

    /**
     * 活性炭是否选中: 是、否
     */
    @ExcelProperty(value = "excel.activated_carbon_selected", index = 74)
    private String activatedCarbonSelectedLogistics;

    /**
     * 活性炭名称
     */
    @ExcelProperty(value = "excel.activated_carbon_name", index = 75)
    private String activatedCarbonNameLogistics;

    /**
     * 活性炭质量
     */
    @ExcelProperty(value = "excel.activated_carbon_mass", index = 76)
    private String activatedCarbonMassLogistics;

    /**
     * 活性炭其他信息
     */
    @ExcelProperty(value = "excel.activated_carbon_other", index = 77)
    private String activatedCarbonOtherLogistics;

    // 物流额外属性
    /**
     * 其他信息 1
     */
    @ContentStyle(wrapped = BooleanEnum.TRUE)
    @ExcelProperty(value = "excel.other_information", index = 78)
    private String otherInformationLogistics;


    //配件信息
    /**
     * 是否含适配器：是、否
     */
    @ExcelProperty(value = "excel.adapter_included", index = 79)
    private String adapterIncludedBasic;
    /**
     * 配件1是否要填写: 是、否
     */
    @ExcelProperty(value = "excel.fitting_validate_selected_1", index = 80)
    private String fittingValidateSelected1;

    /**
     * 配件1：配件图片
     */
    @ExcelProperty(value = "excel.image_id_fitting_1", index = 81)
    private String imageIdFittingValidate1;

    /**
     * 配件1：配件描述
     */
    @ExcelProperty(value = "excel.description_fitting_1", index = 82)
    private String descriptionFittingValidate1;

    /**
     * 配件1：数量
     */
    @ExcelProperty(value = "excel.quantity_fitting_1", index = 83)
    private Integer quantityFittingValidate1;

    /**
     * 配件1：单位
     */
    @ExcelProperty(value = "excel.unit_fitting_1", index = 84)
    private String unitFittingValidate1;

    /**
     * 配件1：适用国家代码集合，多个地区用逗号隔开
     */
    @ExcelProperty(value = "excel.applicable_regions_ids_fitting_1", index = 85)
    private String applicableRegionsIdsFittingValidate1;

    /**
     * 配件2是否要填写: 是、否
     */
    @ExcelProperty(value = "excel.fitting_validate_selected_2", index = 86)
    private String fittingValidateSelected2;

    /**
     * 配件2：配件图片
     */
    @ExcelProperty(value = "excel.image_id_fitting_2", index = 87)
    private String imageIdFittingValidate2;

    /**
     * 配件2：配件描述
     */
    @ExcelProperty(value = "excel.description_fitting_2", index = 88)
    private String descriptionFittingValidate2;

    /**
     * 配件2：数量
     */
    @ExcelProperty(value = "excel.quantity_fitting_2", index = 89)
    private Integer quantityFittingValidate2;

    /**
     * 配件2：单位
     */
    @ExcelProperty(value = "excel.unit_fitting_2", index = 90)
    private String unitFittingValidate2;

    /**
     * 配件2：适用国家代码集合，多个地区用逗号隔开
     */
    @ExcelProperty(value = "excel.applicable_regions_ids_fitting_2", index = 91)
    private String applicableRegionsIdsFittingValidate2;

    /**
     * 配件3是否要填写: 是、否
     */
    @ExcelProperty(value = "excel.fitting_validate_selected_3", index = 92)
    private String fittingValidateSelected3;

    /**
     * 配件3：配件图片
     */
    @ExcelProperty(value = "excel.image_id_fitting_3", index = 93)
    private String imageIdFittingValidate3;

    /**
     * 配件3：配件描述
     */
    @ExcelProperty(value = "excel.description_fitting_3", index = 94)
    private String descriptionFittingValidate3;

    /**
     * 配件3：数量
     */
    @ExcelProperty(value = "excel.quantity_fitting_3", index = 95)
    private Integer quantityFittingValidate3;

    /**
     * 配件3：单位
     */
    @ExcelProperty(value = "excel.unit_fitting_3", index = 96)
    private String unitFittingValidate3;

    /**
     * 配件3：适用国家代码集合，多个地区用逗号隔开
     */
    @ExcelProperty(value = "excel.applicable_regions_ids_fitting_3", index = 97)
    private String applicableRegionsIdsFittingValidate3;

    /**
     * 错误信息
     */
    @Excel(name = "错误信息")
    @ExcelProperty(value = "excel.error_msg", index = 98)
    private String errorMsg;

    public void appendErrorMsg(String newErrorMes) {
        if (StringUtils.isNoneBlank(this.errorMsg)) {
            this.errorMsg = this.errorMsg + " " + newErrorMes;
        } else {
            this.errorMsg = newErrorMes;
        }
    }

}
