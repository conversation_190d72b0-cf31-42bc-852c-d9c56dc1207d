package com.renpho.erp.pds.application.category.dto;

import com.renpho.erp.pds.infrastructure.utils.poi.Excel;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 产品型号-导入模板.
 */
@Data
public class PdsProductModelImportErrorExport {
    @Excel(name = "产品型号")
    private String modelNo;

    @Excel(name = "产品经理工号")
    private String pmUserCode;

    @Excel(name = "错误信息")
    private String errorMsg;

    public void appendErrorMsg(String newErrorMes) {
        if (StringUtils.isNoneBlank(this.errorMsg)) {
            this.errorMsg = this.errorMsg + " " + newErrorMes;
        } else {
            this.errorMsg = newErrorMes;
        }
    }
}
