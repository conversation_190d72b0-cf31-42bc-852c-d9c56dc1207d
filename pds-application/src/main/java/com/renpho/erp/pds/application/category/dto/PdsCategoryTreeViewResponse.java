package com.renpho.erp.pds.application.category.dto;

import lombok.Data;

import java.util.List;

/**
 * 品牌表, 列表树状,主数据.
 *
 * <AUTHOR>
 * @since 2024.9.30
 */
@Data
public class PdsCategoryTreeViewResponse {

    /**
     * 主键, 添加不需要，更新才需要
     */
    private Integer id;

    /**
     * 品类代码
     */
    private String cateCode;

    /**
     * 父级品类主键ID
     */
    private Integer parentId;

    /**
     * Menu Status 选项为 Active 和 Inactive 单选，默认选中Active
     */
    private Integer status;

    /**
     * 子节点，最多3层
     */
    private List<PdsCategoryTreeViewResponse> children;

    /**
     * 名称，解析完多语言
     */
    private String name;


}