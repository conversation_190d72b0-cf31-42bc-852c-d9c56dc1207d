package com.renpho.erp.pds.application.product.manager.dto;

import com.renpho.erp.pds.domain.product.manager.authorize.AttachmentInfoDto;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;


@Data
public class PdsProductManagerUpdateBasicRequest {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 产品封面图片
     */
    private String productCoverImageId;

    /**
     * 国家/地区ID
     */
    private Integer countryRegionId;

    /**
     * 产品型号ID
     */
    private Integer productModelId;

    /**
     * 品牌ID
     */
    private Integer brandId;

    /**
     * 品类ID
     */
    private Integer categoryId;

    /**
     * 颜色ID
     */
    private Integer colorId;

    /**
     * 属性编码
     */
    private String attributeEncoding;

    // /**
    //  * 销售渠道ID
    //  */
    // private Integer salesChannelId;

    /**
     * 渠道类型
     */
    private Integer channelType;

    /**
     * 版本编号
     */
    private String version;

    /**
     * 采购SKU
     */
    private String purchaseSku;

    /**
     * 中文名称
     */
    private String chineseName;

    /**
     * 英文名称
     */
    private String englishName;

    /**
     * 市场名称
     */
    private String marketName;

    /**
     * 产品描述
     */
    private List<String> productDescriptionList;

    /**
     * 产品卖点
     */
    private List<String> productSellingPointsList;

    /**
     * 附件
     */
    private List<AttachmentInfoDto> attachmentList;

    /**
     * 备注
     */
    private String remark;

    /**
     * 产品负责人ID
     */
    private Integer productManagerId;

    /**
     * 商务负责人ID
     */
    private Integer businessManagerId;

    /**
     * 采购负责人ID
     */
    private Integer procurementManagerId;

    /**
     * 计划负责人ID
     */
    private Integer planningManagerId;

    /**
     * 合规负责人ID
     */
    private Integer complianceManagerId;

    /**
     * 包材负责人ID
     */
    private Integer packingMaterialManagerId;

    /**
     * 配件信息: 是否含适配器 (0: 否, 1: 是)
     */
    private Integer adapterIncluded;

    /**
     * 审核状态:0,草稿; 1,待审核;2,审核通过;3,审核不通过
     */
    private Integer reviewStatus;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 产品类型
     */
    private Integer productType;

    /**
     * 产品状态:0,正常; 1,正在补货;2,淘汰
     */
    private Integer productStatus;

}