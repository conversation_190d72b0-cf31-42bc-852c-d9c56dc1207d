package com.renpho.erp.pds.application.product.manager.dto;

import lombok.Data;

/**
 * sku校验
 *
 * <AUTHOR>
 * @since 2024/11/12
 */
@Data
public class PdsProductManagerVerifySkuRequest {

    /**
     * 字典需要，不需要传，忽略该字段
     */
    private transient Integer id;

    /**
     * product_type 产品类型字典
     */
    private Integer productType;

    /**
     * 国家代码
     */
    private String countryCode;

    /**
     * 产品型号
     */
    private String productModeNo;

    /**
     * 颜色代码
     */
    private String colorCode;

    /**
     * 属性编码，允许数字与符号组合、最多支持4字符
     */
    private String attributeEncoding;

    /**
     * 销售渠道代码
     */
    private Integer salesChannelCode;

    /**
     * 版本编号
     */
    private String version;

    /**
     * 前端生成的SKU
     */
    private String purchaseSku;

}
