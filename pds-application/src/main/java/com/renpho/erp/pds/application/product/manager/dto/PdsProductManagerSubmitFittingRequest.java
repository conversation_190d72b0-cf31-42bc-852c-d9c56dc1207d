package com.renpho.erp.pds.application.product.manager.dto;

import lombok.Data;

/**
 * 产品管理-配件信息
 *
 * <AUTHOR>
 * @since 2024/11/04
 */
@Data
public class PdsProductManagerSubmitFittingRequest {
    /**
     * 配件图片，最大20M，支持png、jpg、jpeg格式 ; 可以为空
     */
    private String imageId;

    /**
     * 配件描述，支持中文英文，最大1000字符 ; 非空
     */
    private String description;

    /**
     * 数量，范围1-9999 ; 非空
     */
    private Integer quantity;

    /**
     * 单位，字符限制32 ; 可以为空
     */
    private String unit;

    /**
     * 适用国家/地区 ID集合，多个地区用逗号隔开 ; 可以为空
     */
    private String applicableRegionsIds;

}