package com.renpho.erp.pds.application.product.manager.authorize.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fhs.trans.service.impl.TransService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.renpho.erp.ftm.client.response.FileDetailResponse;
import com.renpho.erp.oplog.log.OpLog;
import com.renpho.erp.oplog.log.enums.BusinessType;
import com.renpho.erp.pds.application.category.service.PdsCategorySrv;
import com.renpho.erp.pds.application.color.service.PdsColorSrv;
import com.renpho.erp.pds.application.common.oplog.model.ProductManagerAuthorizeModel;
import com.renpho.erp.pds.application.common.oplog.module.ProductManagerAuthorizeImportSnapt;
import com.renpho.erp.pds.application.common.oplog.module.ProductManagerAuthorizeModules;
import com.renpho.erp.pds.application.common.service.PdsCommonSrv;
import com.renpho.erp.pds.application.country.service.PdsCountryRegionSrv;
import com.renpho.erp.pds.application.product.manager.authorize.dto.*;
import com.renpho.erp.pds.application.product.manager.dto.PdsProductManagerProductQueryViewResponse;
import com.renpho.erp.pds.application.product.manager.dto.PdsProductManagerSkuQueryViewRequest;
import com.renpho.erp.pds.application.product.manager.dto.PdsProductManagerSkuQueryViewResponse;
import com.renpho.erp.pds.application.product.model.service.oplog.PdsSystemModule;
import com.renpho.erp.pds.domain.color.LanguageColor;
import com.renpho.erp.pds.domain.common.*;
import com.renpho.erp.pds.domain.country.LanguageCountryRegion;
import com.renpho.erp.pds.domain.product.manager.authorize.PersonnelAuthorized;
import com.renpho.erp.pds.domain.product.manager.authorize.RoleTypeEnum;
import com.renpho.erp.pds.infrastructure.exception.PdsErrorCode;
import com.renpho.erp.pds.infrastructure.feignclient.RemoteFileFeign;
import com.renpho.erp.pds.infrastructure.feignclient.RemoteUserDetailsFeign;
import com.renpho.erp.pds.infrastructure.persistence.constant.IsDeletedEnum;
import com.renpho.erp.pds.infrastructure.persistence.dto.*;
import com.renpho.erp.pds.infrastructure.persistence.mapper.PdsProductManagerAuthorizeMapper;
import com.renpho.erp.pds.infrastructure.persistence.mapper.PdsProductManagerBasicMapper;
import com.renpho.erp.pds.infrastructure.persistence.po.PdsProductManagerAuthorizePo;
import com.renpho.erp.pds.infrastructure.persistence.po.PdsProductManagerBasicPo;

import com.renpho.erp.pds.infrastructure.persistence.service.RedisService;
import com.renpho.erp.pds.infrastructure.utils.LocalHashMapUtils;
import com.renpho.erp.pds.infrastructure.utils.PagingUtils;
import com.renpho.erp.security.util.SecurityUtils;
import com.renpho.erp.smc.client.dto.OumLabelUserSimpleVo;
import com.renpho.erp.smc.client.dto.OumUserInfoRes;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import com.renpho.karma.exception.ErrorCodeException;
import jakarta.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 产品管理授权接口.
 *
 * <AUTHOR>
 * @since 2024.10.23
 */
@Slf4j
@Service
public class PdsProductManagerAuthorizeSrv {

    @Resource
    private PdsProductManagerAuthorizeMapper pdsProductManagerAuthorizeMapper;

    @Resource
    private PdsProductManagerBasicMapper pdsProductManagerBasicMapper;

    @Resource
    private PdsCategorySrv pdsCategorySrv;

    @Resource
    private PdsCountryRegionSrv pdsCountryRegionSrv;

    @Resource
    private PdsColorSrv pdsColorSrv;

    @Resource
    private PdsCommonSrv pdsCommonSrv;

    @Resource
    private RemoteFileFeign remoteFileFeign;

    @Resource
    private TransService transService;

    @Resource
    private RemoteUserDetailsFeign remoteUserDetails;

    @Resource
    private RedisService redisService;

    /**
     * 产品管理列表查询接口
     *
     * @param request 参数
     * @return 结果集
     */
    public Paging<ProductManagerFilterAuthorizeResponse> list(ProductManagerFilterAuthorizeRequest request) {
        // 使用 PageHelper 进行分页
        int pageNum = request.getPageIndex();
        int pageSize = request.getPageSize();
        PageHelper.startPage(pageNum, pageSize);

        if (StringUtils.isNotBlank(request.getAuthorizationManagerTimeStart()) && StringUtils.isNotBlank(request.getAuthorizationManagerTimeEnd())) {
            request.setAuthorizationManagerTimeStart(request.getAuthorizationManagerTimeStart() + " 00:00:00");
            request.setAuthorizationManagerTimeEnd(request.getAuthorizationManagerTimeEnd() + " 23:59:59");
        }

        if (StringUtils.isNotBlank(request.getAuthorizationPersonnelTimeStart()) && StringUtils.isNotBlank(request.getAuthorizationPersonnelTimeEnd())) {
            request.setAuthorizationPersonnelTimeStart(request.getAuthorizationPersonnelTimeStart() + " 00:00:00");
            request.setAuthorizationPersonnelTimeEnd(request.getAuthorizationPersonnelTimeEnd() + " 23:59:59");
        }

//        // 不需要转换了，前端直接传递第三级ID，转换出来的三级品类ID条件
//        List<Integer> categoryThreeLevelIds = pdsCategorySrv.convertCategoryIdsToCategoryThreeLevelIds(request.getCategoryConditions());
//        request.setCategoryThreeLevelIds(categoryThreeLevelIds);

        List<ProductManagerFilterAuthorizeResponse> authorizePage = pdsProductManagerAuthorizeMapper.selectProductManagerList(request);
        return PagingUtils.toPaging(new PageInfo<>(authorizePage));
    }


    /**
     * 查询产品管理列表，带过滤条件(导出明细)
     *
     * @param filter 产品管理过滤条件
     * @return 产品管理授权明细数据列表
     */
    public List<ProductManagerFilterAuthorizeDetailResponse> listDetail(ProductManagerFilterAuthorizeRequest filter) {
        if (StringUtils.isNotBlank(filter.getAuthorizationManagerTimeStart()) && StringUtils.isNotBlank(filter.getAuthorizationManagerTimeEnd())) {
            filter.setAuthorizationManagerTimeStart(filter.getAuthorizationManagerTimeStart() + " 00:00:00");
            filter.setAuthorizationManagerTimeEnd(filter.getAuthorizationManagerTimeEnd() + " 23:59:59");
        }

        if (StringUtils.isNotBlank(filter.getAuthorizationPersonnelTimeStart()) && StringUtils.isNotBlank(filter.getAuthorizationPersonnelTimeEnd())) {
            filter.setAuthorizationPersonnelTimeStart(filter.getAuthorizationPersonnelTimeStart() + " 00:00:00");
            filter.setAuthorizationPersonnelTimeEnd(filter.getAuthorizationPersonnelTimeEnd() + " 23:59:59");
        }

        List<ProductManagerFilterAuthorizeDetailResponse> authorizeExports = new ArrayList<>();
        // 已被处理的授权记录
        Map<Integer, Boolean> authorizedMap = new HashMap<>();

        // 1.列表已经同时包含运营管理跟运营人员 (存在授权数据)
        List<ProductManagerFilterAuthorizeDetailResponse> authorizeDetailList = pdsProductManagerAuthorizeMapper.selectProductManagerListDetail(filter);

        // 先过滤出运营管理
        for (ProductManagerFilterAuthorizeDetailResponse authorizeDetail : authorizeDetailList) {
            if (RoleTypeEnum.MANAGER.getValue().equals(authorizeDetail.getRoleType())) {
                authorizeExports.add(authorizeDetail);
                authorizedMap.put(authorizeDetail.getAuthorizedId(), true); //标记已处理的授权记录
            }
        }
        // 再过滤出运营人员
        for (ProductManagerFilterAuthorizeDetailResponse authorizeDetail : authorizeDetailList) {
            if (RoleTypeEnum.STAFF.getValue().equals(authorizeDetail.getRoleType())) {
                for (ProductManagerFilterAuthorizeDetailResponse authorizeManager : authorizeExports) {
                    // 属于同一条产品sku的授权、角色是运营人员 并且 当前授权人等于运营管理
                    if (authorizeManager.getId().equals(authorizeDetail.getId()) && authorizeManager.getAuthorizedUserId().equals(authorizeDetail.getAuthorizeUserId())) {
                        PersonnelAuthorized personnelAuthorized = new PersonnelAuthorized();
                        personnelAuthorized.setAuthorizedUserId(authorizeDetail.getAuthorizedUserId());
                        personnelAuthorized.setCreateTime(authorizeDetail.getAuthorizedTime());

                        authorizeManager.addPersonnelAuthorized(personnelAuthorized);
                        authorizedMap.put(authorizeDetail.getAuthorizedId(), true); //标记已处理的授权记录
                        break;
                    }
                }
            }
        }

        // 最后处理未被处理的授权记录(缺了运营管理，只有运营人员的记录)
        for (ProductManagerFilterAuthorizeDetailResponse authorizeDetail : authorizeDetailList) {
            if (authorizedMap.containsKey(authorizeDetail.getAuthorizedId())) {
                continue;
            }
            authorizeExports.add(authorizeDetail);
        }

        // 2.处理不存在授权记录的数据
        List<ProductManagerFilterAuthorizeDetailResponse> authorizeDetailListNull = pdsProductManagerAuthorizeMapper.selectProductManagerListDetailNull(filter);
        authorizeExports.addAll(authorizeDetailListNull);

        // 补充用户信息
        HashSet<Integer> userIds = new HashSet<>();
        HashMap<Integer, String> userDescMap = new HashMap<>();
        for (ProductManagerFilterAuthorizeDetailResponse authorizeDetailResponse : authorizeExports) {
            // 补充运营人员信息
            List<PersonnelAuthorized> personnelList = authorizeDetailResponse.getPersonnelList();
            for (PersonnelAuthorized personnelAuthorized : personnelList) {
                if (personnelAuthorized.getAuthorizedUserId() != null) {
                    userIds.add(personnelAuthorized.getAuthorizedUserId());
                }
            }

            if (authorizeDetailResponse.getAuthorizedUserId() != null) {
                userIds.add(authorizeDetailResponse.getAuthorizedUserId());
            }
            if (authorizeDetailResponse.getAuthorizeUserId()!= null) {
                userIds.add(authorizeDetailResponse.getAuthorizeUserId());
            }
        }

        List<OumUserInfoRes> userInfos = pdsCommonSrv.getUserListInfo(userIds.stream().toList());
        for (OumUserInfoRes userInfo : userInfos) {
            String userDesc = userInfo.getName() + " (" + userInfo.getCode() + ")";
            userDescMap.put(userInfo.getId(), userDesc);
        }

        for (ProductManagerFilterAuthorizeDetailResponse authorizeDetailResponse : authorizeExports) {
            authorizeDetailResponse.buildUserDesc(userDescMap);
        }

        return authorizeExports;
    }

    /**
     * 验证是否存在
     * @param productManagerBasicId 产品管理主键ID
     * @param authorizedUserId      被授权用户ID
     * @param roleType              角色类型
     * @return 存在返回true
     */
    public boolean isExist(Integer productManagerBasicId, Integer authorizedUserId, Integer roleType) {
        // 验证缓存是否存在
        Long expire=redisService.getExpire(RedisCacheKey.getPdsPmAuthorizeKey(productManagerBasicId,authorizedUserId,roleType));
        boolean isExist = false;
        if(expire!=null && expire.intValue()>0){
            isExist = true;
        } else{
            PdsProductManagerAuthorizePo his = findPdsProductManagerAuthorize(productManagerBasicId, authorizedUserId, roleType);
            if(his!=null){
                isExist = true;
            }
        }
        return isExist;
    }

    /**
     * 产品管理添加授权接口
     *
     * @param request 授权信息
     * @return 数据库主键ID集合
     */
    @Transactional
    @LockRedisson(keys = {"'PDS:PM:AUTHORIZE:LOCK:' + #request.id"}, expire = 10000, acquireTimeout = 5000)
    public List<Integer> add(PdsProductManagerAuthorizeAddRequest request) {
        if (request.getUserIds() == null || request.getUserIds().isEmpty()) {
            throw new ErrorCodeException(PdsErrorCode.PRODUCT_MANAGER_AUTHORIZED_PERSONNEL_CANNOT_BE_EMPTY);
        }

        List<Integer> ids = new ArrayList<Integer>();
        PdsProductManagerBasicPo basicPo = this.getPdsProductManagerBasicPoById(request.getId());
        if (basicPo == null) {
            throw new ErrorCodeException(PdsErrorCode.PRODUCT_MANAGER_ID_NOT_EXIST);
        }

        Integer userId = SecurityUtils.getUserId();
        RoleTypeEnum roleType = RoleTypeEnum.getEnum(request.getRoleType());

        // 日志埋点
        LocalHashMapUtils.put("product_manager_id", basicPo.getId());

        for (Integer userIdItem : request.getUserIds()) {
            boolean exist = isExist(basicPo.getId(), userIdItem, roleType.getValue());
            if (exist) {
                continue;
            }

            OumUserInfoRes userInfoRes = pdsCommonSrv.getUserInfoById(userIdItem);
            if (userInfoRes == null) {
                throw new ErrorCodeException(PdsErrorCode.PRODUCT_MANAGER_ILLEGAL_USER_ID);
            }

            PdsProductManagerAuthorizePo authorizePo = new PdsProductManagerAuthorizePo();
            authorizePo.setProductManagerId(basicPo.getId());
            authorizePo.setAuthorizeUserId(userId);
            authorizePo.setAuthorizedUserId(userIdItem);
            authorizePo.setRoleType(roleType.getValue());
            pdsProductManagerAuthorizeMapper.insert(authorizePo);

            // 保证事务成功入库可查
            redisService.setCacheObject(RedisCacheKey.getPdsPmAuthorizeKey(basicPo.getId(), userIdItem, roleType.getValue()), true, 5L, TimeUnit.SECONDS);

            ids.add(authorizePo.getId());
        }

        // 日志埋点
        LocalHashMapUtils.put("ids", ids);

        // 更新产品管理授权状态
        updateAuthorizeStatus(basicPo.getId());
        return ids;
    }

    /**
     * 产品管理取消授权接口
     *
     * @param request 授权信息
     * @return 结果
     */
    @Transactional
    @LockRedisson(keys = {"'PDS:PM:AUTHORIZE:LOCK:' + #request.id"})
    public List<Integer> cancel(PdsProductManagerAuthorizeCancelRequest request) {
        // 受影响id集合
        List<Integer> ids = new ArrayList<Integer>();
        PdsProductManagerBasicPo basicPo = this.getPdsProductManagerBasicPoById(request.getId());

        // 日志埋点
        LocalHashMapUtils.put("product_manager_id", basicPo.getId());

        // 处理 MANAGER 和 STAFF 两种角色
        cancelAuthorizationForRole(request.getUserIdsManager(), basicPo, RoleTypeEnum.MANAGER.getValue(), ids);
        cancelAuthorizationForRole(request.getUserIdsStaff(), basicPo, RoleTypeEnum.STAFF.getValue(), ids);

        // 更新产品管理授权状态
        updateAuthorizeStatus(basicPo.getId());

        // 日志埋点
        LocalHashMapUtils.put("ids", ids);

        return ids;
    }

    /**
     * 取消指定角色的授权
     *
     * @param userIds  授权用户列表
     * @param basicPo  产品管理基础信息
     * @param roleType 角色类型
     * @param ids      受影响ID集合
     */
    private void cancelAuthorizationForRole(List<Integer> userIds, PdsProductManagerBasicPo basicPo, Integer roleType, List<Integer> ids) {
        for (Integer userIdItem : userIds) {
            PdsProductManagerAuthorizePo his = findPdsProductManagerAuthorize(basicPo.getId(), userIdItem, roleType);
            if (his == null) {
                continue;
            }
            LambdaUpdateWrapper<PdsProductManagerAuthorizePo> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(PdsProductManagerAuthorizePo::getId, his.getId())
                    .set(PdsProductManagerAuthorizePo::getDeleted, IsDeletedEnum.DELETE.getValue());
            int count = pdsProductManagerAuthorizeMapper.update(null, updateWrapper);
            if (count > 0) {
                ids.add(his.getId());
            }
        }
    }


    /**
     * 根据定义的条件更新指定 ID 的 pds_product_manager_basic 表中的 authorize_status 字段：
     * 1. 如果指定产品管理的待授权更新数据量等于 1，则终止后续逻辑（authorize_status = 0，表示无授权记录）。
     * 2. 如果指定产品管理的已授权更新数据量等于 1，则终止后续逻辑（authorize_status = 2，表示所有管理者已授权）。
     * 3. 如果上述两步都未更新，则直接更新 authorize_status 为 1（表示待确认）。
     *
     * @param productManagerId 指定的产品管理主键ID。
     */
    private void updateAuthorizeStatus(int productManagerId) {
        // 更新 authorize_status 为 0，表示无授权记录的情况
        int updatedToZero = pdsProductManagerAuthorizeMapper.updateAuthorizeStatusToZero(productManagerId);
        if (updatedToZero == 1) {
            return;
        }

        // 更新 authorize_status 为 2，表示所有管理者已授权
        int updatedToTwo = pdsProductManagerAuthorizeMapper.updateAuthorizeStatusToTwo(productManagerId);
        if (updatedToTwo == 1) {
            return;
        }

        // 如果前两步都没有更新，则更新 authorize_status 为 1，表示待确认
        pdsProductManagerAuthorizeMapper.updateAuthorizeStatusToOne(productManagerId);
    }

    /**
     * 根据ID获取产品管理基本信息
     *
     * @param id 产品管理ID
     * @return 产品管理基本信息
     */
    private PdsProductManagerBasicPo getPdsProductManagerBasicPoById(Integer id) {
        if (id == null) {
            throw new ErrorCodeException(PdsErrorCode.PRODUCT_MANAGER_PRODUCT_MANAGEMENT_INFO_DOES_NOT_EXIST);
        }
        return pdsProductManagerBasicMapper.selectById(id);
    }

    /**
     * 根据产品管理ID和角色类型查询授权信息
     *
     * @param productManagerBasicId 产品管理ID
     * @param authorizedUserId      被授权用户ID
     * @param roleType              角色类型
     * @return 授权信息
     */
    private PdsProductManagerAuthorizePo findPdsProductManagerAuthorize(Integer productManagerBasicId, Integer authorizedUserId, Integer roleType) {
        LambdaQueryWrapper<PdsProductManagerAuthorizePo> queryWrapper = new LambdaQueryWrapper<PdsProductManagerAuthorizePo>();
        queryWrapper.eq(PdsProductManagerAuthorizePo::getProductManagerId, productManagerBasicId);
        queryWrapper.eq(PdsProductManagerAuthorizePo::getRoleType, roleType);
        queryWrapper.eq(PdsProductManagerAuthorizePo::getAuthorizedUserId, authorizedUserId);
        queryWrapper.eq(PdsProductManagerAuthorizePo::getDeleted, IsDeletedEnum.NOT_DELETE.getValue());
        List<PdsProductManagerAuthorizePo> pos = pdsProductManagerAuthorizeMapper.selectList(queryWrapper);
        if (pos != null && !pos.isEmpty()) {
            return pos.get(0);
        }
        return null;
    }

    /**
     * 根据采购SKU查询产品管理基础信息
     *
     * @param purchaseSku 采购SKU
     * @return 产品管理基础信息
     */
    private PdsProductManagerBasicPo findPdsProductManagerBySku(String purchaseSku) {
        LambdaQueryWrapper<PdsProductManagerBasicPo> queryWrapper = new LambdaQueryWrapper<PdsProductManagerBasicPo>();
        queryWrapper.eq(PdsProductManagerBasicPo::getPurchaseSku, purchaseSku);
        queryWrapper.eq(PdsProductManagerBasicPo::getDeleted, IsDeletedEnum.NOT_DELETE.getValue());
        return pdsProductManagerBasicMapper.selectOne(queryWrapper);
    }

    /**
     * 获取所有产品管理者ID（去重）
     *
     * @return 产品管理者ID列表
     */
    public List<OumUserInfoRes> getDistinctProductManagerIds() {
        List<Integer> ids = pdsProductManagerBasicMapper.selectDistinctProductManagerIds();
        if (ids == null || ids.isEmpty()) {
            return Collections.emptyList();
        }
        return pdsCommonSrv.getUserListInfo(ids);
    }

    /**
     * 根据角色类型查询已授权的用户 ID 列表。
     *
     * @param roleType         角色:0,运营管理;1,运营人员。
     * @param productManagerId 产品管理主键ID
     * @return 已授权的用户 ID 列表。
     */
    public List<OumUserInfoRes> getDistinctAuthorizedIdsByRoleType(RoleTypeEnum roleType, Integer productManagerId) {
        List<Integer> ids = pdsProductManagerAuthorizeMapper.getDistinctAuthorizedIdsByRoleType(roleType.getValue(), productManagerId);
        if (ids == null || ids.isEmpty()) {
            return Collections.emptyList();
        }
        return pdsCommonSrv.getUserListInfo(ids);
    }


    /**
     * 导入确认授权数据校验（运营人员）
     *
     * @param importDataList 导入数据
     * @param currentUserId  当前用户ID
     * @return 授权结果
     */
    public ImportChangePMAVerify importChangePMAPersonnelVerify(List<PdsProductAuthorizePersonnelImportExport> importDataList, Integer currentUserId) {
        ImportChangePMAVerify verify = new ImportChangePMAVerify();
        Integer roleType = RoleTypeEnum.STAFF.getValue();

        //TODO 添加角色标签限制：过滤出属于自己的下属，不是自己的下属无法操作
        Map<String, OumLabelUserSimpleVo> labelUserMap = pdsCommonSrv.getLabelUserMap(currentUserId, CommonConstants.LABEL_PERSONNEL);

        for (PdsProductAuthorizePersonnelImportExport importData : importDataList) {
            PdsProductAuthorizeModelImportErrorExport errorExport = new PdsProductAuthorizeModelImportErrorExport();
            errorExport.setPurchaseSku(importData.getPurchaseSku());
            errorExport.setPersonnelCode(importData.getPersonnelCode());
            try {
                if (StringUtils.isBlank(importData.getPurchaseSku())) {
                    errorExport.setErrorMsg("采购SKU参数不能为空");
                    verify.addPdsProductAuthorizeModelImportErrorExport(errorExport);
                    continue;
                }
                if (importData.getPersonnelCode() == null) {
                    errorExport.setErrorMsg("被授权人信息不能为空");
                    verify.addPdsProductAuthorizeModelImportErrorExport(errorExport);
                    continue;
                }

                OumUserInfoRes userInfoRes = pdsCommonSrv.getUserInfo(importData.getPersonnelCode());
                if (userInfoRes == null) {
                    errorExport.setErrorMsg("非法授权人信息");
                    verify.addPdsProductAuthorizeModelImportErrorExport(errorExport);
                    continue;
                }

                PdsProductManagerBasicPo basicPo = this.findPdsProductManagerBySku(importData.getPurchaseSku());
                if (basicPo == null) {
                    errorExport.setErrorMsg("采购SKU不存在");
                    verify.addPdsProductAuthorizeModelImportErrorExport(errorExport);
                    continue;
                }

                // 标签限制
                OumLabelUserSimpleVo labelUserSimpleVo = labelUserMap.get(importData.getPersonnelCode());
                if (labelUserSimpleVo == null) {
                    errorExport.setErrorMsg("只能操作自己的下属，该工号不属于自己的下属： " + importData.getPersonnelCode());
                    verify.addPdsProductAuthorizeModelImportErrorExport(errorExport);
                    continue;
                } else {
                    UserStatusEnum userStatus = UserStatusEnum.getUserStatus(labelUserSimpleVo.getStatus());
                    if (!UserStatusEnum.ON.equals(userStatus)) {
                        errorExport.setErrorMsg("只能操作在职员工，工号： " + importData.getPersonnelCode());
                        verify.addPdsProductAuthorizeModelImportErrorExport(errorExport);
                        continue;
                    }
                }

                // 日志埋点
                LocalHashMapUtils.put("product_manager_id", basicPo.getId());

                boolean exist = isExist(basicPo.getId(), userInfoRes.getId(), roleType);
                if (exist) {
                    errorExport.setErrorMsg("授权信息已存在");
                    verify.addPdsProductAuthorizeModelImportErrorExport(errorExport);
                    continue;
                }

                PdsProductManagerAuthorizePo authorizePo = new PdsProductManagerAuthorizePo();
                authorizePo.setProductManagerId(basicPo.getId());
                authorizePo.setAuthorizeUserId(currentUserId);
                authorizePo.setAuthorizedUserId(userInfoRes.getId());
                authorizePo.setRoleType(roleType);
                verify.addPdsProductManagerAuthorizePo(authorizePo);
                verify.addImportRight(errorExport);

                // 保证事务成功入库可查
                redisService.setCacheObject(RedisCacheKey.getPdsPmAuthorizeKey(basicPo.getId(), userInfoRes.getId(), roleType), true, 10L, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.error("导入确认授权数据校验异常", e);
                errorExport.setErrorMsg(e.getMessage());
                verify.addPdsProductAuthorizeModelImportErrorExport(errorExport);
            }
        }

        return verify;
    }

    /**
     * 导入产品授权数据校验(运营管理)
     *
     * @param importDataList 导入数据
     * @param currentUserId  当前用户ID
     * @return 授权结果
     */
    public ImportChangePMAVerify importChangePMAManagerVerify(List<PdsProductAuthorizeManagerImportExport> importDataList, Integer currentUserId) {
        ImportChangePMAVerify verify = new ImportChangePMAVerify();
        Integer roleType = RoleTypeEnum.MANAGER.getValue();

        //TODO 添加角色标签限制：过滤出属于自己的下属，不是自己的下属无法操作
        Map<String, OumLabelUserSimpleVo> labelUserMap = pdsCommonSrv.getLabelUserMap(currentUserId, CommonConstants.LABEL_PM);
        for (PdsProductAuthorizeManagerImportExport importData : importDataList) {
            PdsProductAuthorizeModelImportErrorExport errorExport = new PdsProductAuthorizeModelImportErrorExport();
            errorExport.setPurchaseSku(importData.getPurchaseSku());
            errorExport.setManagerCode(importData.getManagerCode());
            try {
                if (StringUtils.isBlank(importData.getPurchaseSku())) {
                    errorExport.setErrorMsg("采购SKU参数不能为空");
                    verify.addPdsProductAuthorizeModelImportErrorExport(errorExport);
                    continue;
                }
                if (importData.getManagerCode() == null) {
                    errorExport.setErrorMsg("被授权人信息不能为空");
                    verify.addPdsProductAuthorizeModelImportErrorExport(errorExport);
                    continue;
                }
                OumUserInfoRes userInfoRes = pdsCommonSrv.getUserInfo(importData.getManagerCode());
                if (userInfoRes == null) {
                    errorExport.setErrorMsg("非法授权人信息");
                    verify.addPdsProductAuthorizeModelImportErrorExport(errorExport);
                    continue;
                }

                PdsProductManagerBasicPo basicPo = this.findPdsProductManagerBySku(importData.getPurchaseSku());
                if (basicPo == null) {
                    errorExport.setErrorMsg("采购SKU不存在");
                    verify.addPdsProductAuthorizeModelImportErrorExport(errorExport);
                    continue;
                }

                // 标签限制
                OumLabelUserSimpleVo labelUserSimpleVo = labelUserMap.get(importData.getManagerCode());
                if (labelUserSimpleVo == null) {
                    errorExport.setErrorMsg("只能操作自己的下属，该工号不属于自己的下属： " + importData.getManagerCode());
                    verify.addPdsProductAuthorizeModelImportErrorExport(errorExport);
                    continue;
                } else {
                    UserStatusEnum userStatus = UserStatusEnum.getUserStatus(labelUserSimpleVo.getStatus());
                    if (!UserStatusEnum.ON.equals(userStatus)) {
                        errorExport.setErrorMsg("只能操作在职员工，工号： " + importData.getManagerCode());
                        verify.addPdsProductAuthorizeModelImportErrorExport(errorExport);
                        continue;
                    }
                }

                // 日志埋点
                LocalHashMapUtils.put("product_manager_id", basicPo.getId());

                boolean exist = isExist(basicPo.getId(), userInfoRes.getId(), roleType);
                if (exist) {
                    errorExport.setErrorMsg("授权信息已存在");
                    verify.addPdsProductAuthorizeModelImportErrorExport(errorExport);
                    continue;
                }

                PdsProductManagerAuthorizePo authorizePo = new PdsProductManagerAuthorizePo();
                authorizePo.setProductManagerId(basicPo.getId());
                authorizePo.setAuthorizeUserId(currentUserId);
                authorizePo.setAuthorizedUserId(userInfoRes.getId());
                authorizePo.setRoleType(roleType);
                verify.addPdsProductManagerAuthorizePo(authorizePo);
                verify.addImportRight(errorExport);

                // 保证事务成功入库可查
                redisService.setCacheObject(RedisCacheKey.getPdsPmAuthorizeKey(basicPo.getId(), userInfoRes.getId(), roleType), true, 10L, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.error("导入确认授权数据校验异常", e);
                errorExport.setErrorMsg(e.getMessage());
                verify.addPdsProductAuthorizeModelImportErrorExport(errorExport);
            }
        }

        return verify;
    }

    /**
     * 导入产品授权数据存储
     *
     * @param updatePoList 更新数据
     * @return 存储结果(主键ID集合)
     */
    @LockRedisson(keys = {"PDS:PM:AUTHORIZE:LOCK:import"})
    public List<ProductManagerAuthorizeModel> importChangePMAStorage(List<PdsProductManagerAuthorizePo> updatePoList, ImportChangePMAVerify verify) {
        List<ProductManagerAuthorizeModel> ids = new ArrayList<ProductManagerAuthorizeModel>();
        if (updatePoList == null || updatePoList.isEmpty()) {
            return ids;
        }
        Set<Integer> idSet = new HashSet<Integer>();
        for (int i = 0; i < updatePoList.size(); i++) {
            try{
                PdsProductManagerAuthorizePo po = updatePoList.get(i);
                PdsProductAuthorizeModelImportErrorExport importData = verify.getImportRightList().get(i);
                boolean exist = isExist(po.getId(), po.getAuthorizedUserId(), po.getRoleType());
                if (exist) {
                    PdsProductAuthorizeModelImportErrorExport errorExport = new PdsProductAuthorizeModelImportErrorExport();
                    errorExport.setPurchaseSku(importData.getPurchaseSku());
                    errorExport.setManagerCode(importData.getManagerCode());
                    errorExport.setErrorMsg("授权信息已存在");
                    verify.addPdsProductAuthorizeModelImportErrorExport(errorExport);
                    continue;
                }
                pdsProductManagerAuthorizeMapper.insert(po);
                ids.add(new ProductManagerAuthorizeModel(po.getId(),po.getProductManagerId()));
                idSet.add(po.getProductManagerId());

                // 保证事务成功入库可查
                redisService.setCacheObject(RedisCacheKey.getPdsPmAuthorizeKey(po.getId(), po.getAuthorizedUserId(), po.getRoleType()), true, 10L, TimeUnit.SECONDS);
            }catch (Exception e){
                log.error("导入产品授权数据存储异常", e);
            }
        }

        // 日志埋点
        LocalHashMapUtils.put("ids", ids);

        // 更新产品管理授权状态
        for (Integer id : idSet) {
            updateAuthorizeStatus(id);
        }

        return ids;
    }

    /**
     * 返回数据涉及到ID的都要转成name，并且按照多语言方式
     *
     * @param records  数据列表
     * @param language 语言
     */
    public void buildNames(List<? extends ProductManagerFilterAuthorizeResponse> records, LanguageEnum language) {
        // 数字字典
        transService.transBatch(records);
        for (ProductManagerFilterAuthorizeResponse record : records) {
            if (StringUtils.isNotBlank(record.getProductManagerId())) {
                OumUserInfoRes userInfoRes = pdsCommonSrv.getUserInfoById(Integer.valueOf(record.getProductManagerId()));
                if (userInfoRes != null) {
                    String userDesc = pdsCommonSrv.getUserDesc(userInfoRes);
                    record.setProductManagerName(userDesc);
                }
            }
            if (record.getCategoryId() != null) {
                StringBuilder treeName = new StringBuilder();
                pdsCategorySrv.getCategoryTreeName(record.getCategoryId(), language, treeName);
                record.setCategoryName(treeName.toString());
            }
            if (record.getColorId() != null) {
                List<LanguageColor> languageColors = pdsColorSrv.getLanguages(record.getColorId());
                String name = LanguageEnum.getLanguage(languageColors, language);
                record.setColorName(name);
            }
            if (record.getCountryRegionId() != null) {
                List<LanguageCountryRegion> languageCountryRegions = pdsCountryRegionSrv.getLanguages(record.getCountryRegionId());
                String name = LanguageEnum.getLanguage(languageCountryRegions, language);
                record.setCountryRegionName(name);
            }
            if (StringUtils.isNotBlank(record.getProductCoverImageId())) {
                FileDetailResponse responseR = this.pdsCommonSrv.getFileInfo(record.getProductCoverImageId());
                if (responseR != null) {
                    record.setProductCoverImageFileInfo(responseR);
                }
            }
            if (StringUtils.isNotBlank(record.getChineseName()) && StringUtils.isNotBlank(record.getEnglishName())) {
                if (language == LanguageEnum.China) {
                    record.setProductName(record.getChineseName());
                } else {
                    record.setProductName(record.getEnglishName());
                }
            }
        }
    }

    /**
     * 补充授权信息
     *
     * @param records 数据列表
     */
    public void buildAuthorizeInfo(List<? extends ProductManagerFilterAuthorizeResponse> records) {
        for (ProductManagerFilterAuthorizeResponse record : records) {
            List<OumUserInfoRes> operationsManagers = getDistinctAuthorizedIdsByRoleType(RoleTypeEnum.MANAGER, record.getId());
            record.setOperationsManagers(operationsManagers);
            List<OumUserInfoRes> operationsPersons = getDistinctAuthorizedIdsByRoleType(RoleTypeEnum.STAFF, record.getId());
            record.setOperationsPersons(operationsPersons);
        }
    }

    /**
     * 转成导出数据
     *
     * @param listDetail 数据列表
     * @return 导出数据
     */
    public List<PdsProductAuthorizeManagerExport> listDetailToExport(List<ProductManagerFilterAuthorizeDetailResponse> listDetail) {
        List<PdsProductAuthorizeManagerExport> exports = new ArrayList<>();

        // 降序排序
        listDetail.sort(Comparator.comparing(ProductManagerFilterAuthorizeDetailResponse::getUpdateTime).reversed());
        for (ProductManagerFilterAuthorizeDetailResponse detail : listDetail) {
            List<PdsProductAuthorizeManagerExport> export = mapToExportList(detail);
            exports.addAll(export);
        }
        return exports;
    }

    /**
     * 授权数据导出平铺
     *
     * @param detail 数据
     * @return 导出平铺
     */
    public List<PdsProductAuthorizeManagerExport> mapToExportList(ProductManagerFilterAuthorizeDetailResponse detail) {
        List<PdsProductAuthorizeManagerExport> exportList = new ArrayList<>();

        // 遍历每个授权人员，生成对应的导出记录
        if (detail.getPersonnelList() != null && !detail.getPersonnelList().isEmpty()) {
            for (PersonnelAuthorized personnel : detail.getPersonnelList()) {
                PdsProductAuthorizeManagerExport export = new PdsProductAuthorizeManagerExport();
                export.setPurchaseSku(detail.getPurchaseSku());
                export.setModelNo(detail.getModelNo());
                export.setColorName(detail.getColorName());
                export.setProductName(detail.getProductName());
                export.setCategoryName(detail.getCategoryName());
                export.setBrandName(detail.getBrandName());
                export.setCountryRegionName(detail.getCountryRegionName());
                export.setProductStatus(detail.getProductStatusName());
                export.setProductType(detail.getProductTypeName());
                export.setProductManagerName(detail.getProductManagerName());
                export.setOperationsManagerName(detail.getAuthorizedUserName());
                export.setAuthorizedTime(detail.getAuthorizedTime());

                // 设置每个授权人员的名字和创建时间
                export.setAuthorizedUserName(personnel.getAuthorizedUserName());
                export.setCreateTime(personnel.getCreateTime());

                exportList.add(export);
            }
        } else {
            // 若无授权人员，则生成一条基础信息记录
            PdsProductAuthorizeManagerExport export = new PdsProductAuthorizeManagerExport();
            export.setPurchaseSku(detail.getPurchaseSku());
            export.setModelNo(detail.getModelNo());
            export.setColorName(detail.getColorName());
            export.setProductName(detail.getProductName());
            export.setCategoryName(detail.getCategoryName());
            export.setBrandName(detail.getBrandName());
            export.setCountryRegionName(detail.getCountryRegionName());
            export.setProductType(detail.getProductTypeName());
            export.setProductStatus(detail.getProductStatusName());
            export.setProductManagerName(detail.getProductManagerName());

            // 需要先判断角色
            if (RoleTypeEnum.MANAGER.getValue().equals(detail.getRoleType())) {
                export.setOperationsManagerName(detail.getAuthorizedUserName());
                export.setAuthorizedTime(detail.getAuthorizedTime());
            } else {
                export.setOperationsManagerName(detail.getAuthorizeUserName());
                export.setAuthorizedUserName(detail.getAuthorizedUserName());
                export.setCreateTime(detail.getAuthorizedTime());
            }

            exportList.add(export);
        }

        return exportList;
    }

    /**
     * <br/>重新构建授权信息： id-id 转成 name-name
     * <br/>重新构建授权时间： xx授权：2024-10-30 04:17:08
     *
     * @param records 需要重新构建授权信息的记录列表
     */
    public void reBuildAuthorizeInfos(List<ProductManagerFilterAuthorizeResponse> records) {
        Set<Integer> idList = new HashSet<>();  // 用于批量获取用户信息
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        // authorizeTimes
        List<AuthorizeModelResponse> managerIdListTimeInfo = new ArrayList<>();
        List<AuthorizeModelResponse> personIdListTimeInfo = new ArrayList<>();

        // authorizeInfos
        List<List<PdsProductManagerAuthorizeResponse>> authorizeListInfo = new ArrayList<>();

        // 1. 授权时间拼接
        for (ProductManagerFilterAuthorizeResponse record : records) {
            Integer productManagerId = record.getId();

            // authorizeTimes
            AuthorizeModelResponse operationsManagerId = pdsProductManagerAuthorizeMapper.getOperationsManagerIdLastByProductManagerId(productManagerId);
            AuthorizeModelResponse operationsPersonId = pdsProductManagerAuthorizeMapper.getOperationsPersonIdLastByProductManagerId(productManagerId);
            if (operationsManagerId != null) {
                idList.add(operationsManagerId.getAuthorizedUserId());
                idList.add(operationsManagerId.getAuthorizeUserId());
            }
            if (operationsPersonId != null) {
                idList.add(operationsPersonId.getAuthorizedUserId());
                idList.add(operationsPersonId.getAuthorizeUserId());
            }
            managerIdListTimeInfo.add(operationsManagerId);
            personIdListTimeInfo.add(operationsPersonId);

            // authorizeInfos
            List<PdsProductManagerAuthorizeResponse> authorizeList = pdsProductManagerAuthorizeMapper.getAuthorizeListByProductManagerId(productManagerId);
            for (PdsProductManagerAuthorizeResponse authorizeResponse : authorizeList) {
                idList.add(authorizeResponse.getAuthorizeUserId());
                idList.add(authorizeResponse.getAuthorizedUserId());
            }
            authorizeListInfo.add(authorizeList);
        }

        // 节省效率，批量获取用户信息
        Map<Integer, OumUserInfoRes> userMap = pdsCommonSrv.getUserMap(idList.stream().toList());
        for (int i = 0; i < records.size(); i++) {
            ProductManagerFilterAuthorizeResponse record = records.get(i);
            AuthorizeModelResponse operationsManagerId = managerIdListTimeInfo.get(i);
            AuthorizeModelResponse operationsPersonId = personIdListTimeInfo.get(i);

            //产品授权：确认授权：
            List<String> authorizeInfos = new ArrayList<>();
            List<String> authorizeTimes = new ArrayList<>();

            if (operationsManagerId != null) {
                OumUserInfoRes operationsManagerLast = userMap.get(operationsManagerId.getAuthorizedUserId());
                OumUserInfoRes operationsManagerFirst = userMap.get(operationsManagerId.getAuthorizeUserId());
                if (operationsManagerLast != null) {
                    String descLast = pdsCommonSrv.getUserDesc(operationsManagerLast);
                    String descFirst = pdsCommonSrv.getUserDesc(operationsManagerFirst);
                    authorizeInfos.add(descFirst + " -> " + descLast);
                    authorizeTimes.add("产品授权：" + sdf.format(operationsManagerId.getCreateTime()));
                } else {
                    authorizeInfos.add("--");
                    authorizeTimes.add("产品授权：--");
                }
            } else {
                authorizeInfos.add("--");
                authorizeTimes.add("产品授权：--");
            }

            if (operationsPersonId != null) {
                OumUserInfoRes operationsPersonLast = userMap.get(operationsPersonId.getAuthorizedUserId());
                OumUserInfoRes operationsPersonFirst = userMap.get(operationsPersonId.getAuthorizeUserId());
                if (operationsPersonLast != null) {
                    String descLast = pdsCommonSrv.getUserDesc(operationsPersonLast);
                    String descFirst = pdsCommonSrv.getUserDesc(operationsPersonFirst);
                    authorizeInfos.add(descFirst + " -> " + descLast);
                    authorizeTimes.add("确认授权：" + sdf.format(operationsPersonId.getCreateTime()));
                } else {
                    authorizeInfos.add("--");
                    authorizeTimes.add("确认授权：--");
                }
            } else {
                authorizeInfos.add("--");
                authorizeTimes.add("确认授权：--");
            }

            record.buildAuthorizeInfos(authorizeInfos);
            record.bulidAuthorizeTimes(authorizeTimes);
        }

        // 2. 授权信息拼接
        for (int i = 0; i < records.size(); i++) {
            ProductManagerFilterAuthorizeResponse record = records.get(i);

            // 用于拼接授权信息,egg: 测试2 (FT000002) -> : 测试2 (FT000002) -> 用户姓名14 (FT200004)
            Map<String, Integer> mapAuthorize = new LinkedHashMap<>();
            List<String> authorizeInfos = new LinkedList<>();

            List<PdsProductManagerAuthorizeResponse> authorizeList = authorizeListInfo.get(i);

            if (authorizeList.isEmpty()) {
                authorizeInfos.add("--");
            } else {
                for (PdsProductManagerAuthorizeResponse detail : authorizeList) {
                    if (RoleTypeEnum.MANAGER.getValue().equals(detail.getRoleType())) {
                        OumUserInfoRes userInfoRes = userMap.get(detail.getAuthorizedUserId());
                        if (userInfoRes != null) {
                            String descFirst = pdsCommonSrv.getUserDesc(userInfoRes);
                            String mapKey = descFirst + " -> ";
                            authorizeInfos.add(mapKey);

                            if (!mapAuthorize.containsKey(mapKey)) {
                                mapAuthorize.put(mapKey, 1);
                            } else {
                                mapAuthorize.put(mapKey, mapAuthorize.get(mapKey) + 1);
                            }
                        }
                    } else {
                        OumUserInfoRes userInfoManager = userMap.get(detail.getAuthorizeUserId());
                        OumUserInfoRes userInfoPerson = userMap.get(detail.getAuthorizedUserId());
                        if (userInfoManager != null && userInfoPerson != null) {
                            String descFirst = pdsCommonSrv.getUserDesc(userInfoManager);
                            String mapKey = descFirst + " -> ";
                            String descLast = pdsCommonSrv.getUserDesc(userInfoPerson);
                            String mapValue = mapKey + descLast;
                            authorizeInfos.add(mapValue);

                            if (!mapAuthorize.containsKey(mapKey)) {
                                mapAuthorize.put(mapKey, 1);
                            } else {
                                mapAuthorize.put(mapKey, mapAuthorize.get(mapKey) + 1);
                            }
                        }
                    }
                }
            }
            // 删除单独的运营管理授权信息(如果运营管理已存在授权运营人员的记录)
            for (String mapKey : mapAuthorize.keySet()) {
                Integer count = mapAuthorize.get(mapKey);
                if (count > 1) {
                    authorizeInfos.remove(mapKey);
                }
            }
            record.buildAuthorizeInfos(authorizeInfos);
        }

    }

    /**
     * 根据id集合获取授权信息
     *
     * @param ids id集合
     * @return 授权信息
     */
    public List<PdsProductManagerAuthorizePo> getPdsProductManagerAuthorizePos(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            return Collections.emptyList();
        }
        //TODO 这两种写法有坑，nacos配置会加上语句 AND is_deleted = 0
        //LambdaQueryWrapper<PdsProductManagerAuthorizePo> queryWrapper = new LambdaQueryWrapper<PdsProductManagerAuthorizePo>();
        //queryWrapper.in(PdsProductManagerAuthorizePo::getId, ids);
        //pdsProductManagerAuthorizeMapper.selectBatchIds(ids);
        //pdsProductManagerAuthorizeMapper.selectList(queryWrapper);

        // 只能通过xml或者@Select 手写sql方式避开这个 mybatis-plus.global-config.db-config.logic-delete-field
        return pdsProductManagerAuthorizeMapper.selectByIdsWithoutLogicDelete(ids);
    }

    /**
     * 根据id和用户id集合获取授权信息
     *
     * @param id       产品管理主键id
     * @param userIds  用户id集合
     * @param roleType 角色类型
     * @return 授权信息
     */
    public List<PdsProductManagerAuthorizePo> getPdsProductManagerAuthorizePos(Integer id, List<Integer> userIds, Integer roleType) {
        PdsProductManagerBasicPo basicPo = this.getPdsProductManagerBasicPoById(id);
        if (basicPo == null || basicPo.getId() == null) {
            return Collections.emptyList();
        }

        List<PdsProductManagerAuthorizePo> poList = new ArrayList<>();
        for (Integer userIdItem : userIds) {
            PdsProductManagerAuthorizePo his = findPdsProductManagerAuthorize(basicPo.getId(), userIdItem, roleType);
            if (his == null) {
                continue;
            }
            poList.add(his);
        }
        return poList;
    }

    /**
     * 根据产品管理主键id获取授权详情
     *
     * @param productManagerId 产品管理主键id
     * @return 授权详情
     */
    public List<PdsProductManagerAuthorizeDetailResponse> getAuthorizeDetails(Integer productManagerId) {
        // 查询授权信息列表
        LambdaQueryWrapper<PdsProductManagerAuthorizePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PdsProductManagerAuthorizePo::getProductManagerId, productManagerId)
                .eq(PdsProductManagerAuthorizePo::getDeleted, CommonConstants.NOT_DELETE);

        List<PdsProductManagerAuthorizePo> authorizeList = pdsProductManagerAuthorizeMapper.selectList(queryWrapper);
        if (authorizeList == null || authorizeList.isEmpty()) {
            return Collections.emptyList();
        }

        // 获取所有相关用户的详细信息
        Set<Integer> userIds = authorizeList.stream()
                .map(PdsProductManagerAuthorizePo::getAuthorizedUserId)
                .collect(Collectors.toSet());

        Map<Integer, OumUserInfoRes> userMap = pdsCommonSrv.getUserMap(userIds.stream().toList());

        // 构建运营管理人信息并关联其授权的运营人员
        Map<Integer, PdsProductManagerAuthorizeDetailResponse> managerDetailsMap = new HashMap<>();

        // 最终输出
        List<PdsProductManagerAuthorizeDetailResponse> details = new ArrayList<>();

        for (PdsProductManagerAuthorizePo auth : authorizeList) {
            if (auth.getRoleType().equals(RoleTypeEnum.MANAGER.getValue())) {
                // 运营管理人
                PdsProductManagerAuthorizeDetailResponse detail = new PdsProductManagerAuthorizeDetailResponse();
                detail.setOperationManagerUserId(auth.getAuthorizedUserId());
                detail.setOperationManagerCreateTime(auth.getCreateTime());

                String managerName = pdsCommonSrv.getUserDesc(userMap.get(auth.getAuthorizedUserId()));
                detail.setOperationManagerName(managerName);

                // 将运营管理人信息存入 map，以便后续关联运营人员
                managerDetailsMap.put(auth.getAuthorizedUserId(), detail);
            } else if (auth.getRoleType().equals(RoleTypeEnum.STAFF.getValue())) {
                // 运营人员，查找其授权的运营管理人
                PdsProductManagerAuthorizeDetailResponse managerDetail = managerDetailsMap.get(auth.getAuthorizeUserId());
                if (managerDetail != null) {
                    // 如果找到了对应的运营管理人，关联运营人员信息
                    managerDetail.setOperationPersonnelUserId(auth.getAuthorizedUserId());
                    managerDetail.setOperationPersonnelCreateTime(auth.getCreateTime());

                    String personnelName = pdsCommonSrv.getUserDesc(userMap.get(auth.getAuthorizedUserId()));
                    managerDetail.setOperationPersonnelName(personnelName);

                    PdsProductManagerAuthorizeDetailResponse detail = new PdsProductManagerAuthorizeDetailResponse();
                    BeanUtils.copyProperties(managerDetail, detail);
                    details.add(detail);
                } else {
                    // 运营人员
                    PdsProductManagerAuthorizeDetailResponse detail = new PdsProductManagerAuthorizeDetailResponse();
                    detail.setOperationPersonnelUserId(auth.getAuthorizedUserId());
                    detail.setOperationPersonnelCreateTime(auth.getCreateTime());
                    String personnelName = pdsCommonSrv.getUserDesc(userMap.get(auth.getAuthorizedUserId()));
                    detail.setOperationPersonnelName(personnelName);
                    details.add(detail);
                }
            }
        }

        return details;
    }


    /**
     * 根据产品管理主键id获取授权详情
     *
     * @param id 产品管理主键id
     * @return 授权详情
     */
    public PdsProductManagerAuthorizePo getPdsProductManagerAuthorizePo(Integer id) {
        return pdsProductManagerAuthorizeMapper.selectById(id);
    }

    @OpLog(snaptSource = ProductManagerAuthorizeImportSnapt.class, title = "产品管理-导入产品授权", businessType = BusinessType.Import_Product_Authorization,
            systemModule = PdsSystemModule.class, businessModule = ProductManagerAuthorizeModules.class)
    public ProductManagerAuthorizeModel importProductAuthorizePo(ProductManagerAuthorizeModel mainCmd) {
        return mainCmd;
    }

    @OpLog(snaptSource = ProductManagerAuthorizeImportSnapt.class, title = "产品管理-导入确认授权", businessType = BusinessType.Import_Confirmation_Authorization,
            systemModule = PdsSystemModule.class, businessModule = ProductManagerAuthorizeModules.class)
    public ProductManagerAuthorizeModel importConfirmAuthorizePo(ProductManagerAuthorizeModel mainCmd) {
        return mainCmd;
    }

    /**
     * 查询产品管理，传入用户ID数组，输出匹配负责人信息的psku
     * @param request 请求参数
     * @return psku列表
     */
    public R<PdsProductManagerSkuQueryViewResponse> viewPskuList(PdsProductManagerSkuQueryViewRequest request) {
        PdsProductManagerSkuQueryViewResponse response = new PdsProductManagerSkuQueryViewResponse();

        if (request == null ) {
            response.setPskuList(Collections.emptyList());
            return R.success(response);
        }

        // 构造查询条件
        LambdaQueryWrapper<PdsProductManagerAuthorizePo> queryWrapper = Wrappers.lambdaQuery();

        if (request.getIdList() != null && !request.getIdList().isEmpty()) {
            queryWrapper.in(PdsProductManagerAuthorizePo::getAuthorizedUserId, request.getIdList());
        }
        queryWrapper.eq(PdsProductManagerAuthorizePo::getDeleted, CommonConstants.NOT_DELETE);

        // 执行查询
        List<PdsProductManagerAuthorizePo> productList = pdsProductManagerAuthorizeMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(productList)) {
            response.setPskuList(Collections.emptyList());
            return R.success(response);
        }

        // 提取 purchaseSku
        List<Integer> pmIdList =  productList.stream()
                .map(PdsProductManagerAuthorizePo::getProductManagerId)
                .distinct()
                .toList();

        LambdaQueryWrapper<PdsProductManagerBasicPo> basicQueryWrapper = Wrappers.lambdaQuery();
        basicQueryWrapper
                .in(PdsProductManagerBasicPo::getId, pmIdList)
                .eq(PdsProductManagerBasicPo::getReviewStatus, PdsManagerReviewStatus.APPROVED.getCode())
                .eq(PdsProductManagerBasicPo::getDeleted, CommonConstants.NOT_DELETE);
        List<PdsProductManagerBasicPo> basicList = pdsProductManagerBasicMapper.selectList(basicQueryWrapper);

        List<String> purchaseSkuList = basicList.stream()
                .map(PdsProductManagerBasicPo::getPurchaseSku)
                .distinct()
                .toList();
        response.setPskuList(purchaseSkuList);

        return R.success(response);
    }

    /**
     * 查询产品授权，传入用户ID数组，输出匹配授权用户的psku、ID集合
     * @param request 请求参数
     * @return psku、ID集合
     */
    public R<List<PdsProductManagerProductQueryViewResponse>> viewProductList(PdsProductManagerSkuQueryViewRequest request) {
        if (request == null ) {
            return R.success(new ArrayList<>());
        }

        // 构造查询条件
        LambdaQueryWrapper<PdsProductManagerAuthorizePo> queryWrapper = Wrappers.lambdaQuery();

        if (request.getIdList() != null && !request.getIdList().isEmpty()) {
            queryWrapper.in(PdsProductManagerAuthorizePo::getAuthorizedUserId, request.getIdList());
        }
        queryWrapper.eq(PdsProductManagerAuthorizePo::getDeleted, CommonConstants.NOT_DELETE);

        // 执行查询
        List<PdsProductManagerAuthorizePo> productList = pdsProductManagerAuthorizeMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(productList)) {
            return R.success(new ArrayList<>());
        }

        // 提取 purchaseSku
        List<Integer> pmIdList =  productList.stream()
                .map(PdsProductManagerAuthorizePo::getProductManagerId)
                .distinct()
                .toList();

        LambdaQueryWrapper<PdsProductManagerBasicPo> basicQueryWrapper = Wrappers.lambdaQuery();
        basicQueryWrapper
                .in(PdsProductManagerBasicPo::getId, pmIdList)
                .eq(PdsProductManagerBasicPo::getReviewStatus, PdsManagerReviewStatus.APPROVED.getCode())
                .eq(PdsProductManagerBasicPo::getDeleted, CommonConstants.NOT_DELETE);
        List<PdsProductManagerBasicPo> basicList = pdsProductManagerBasicMapper.selectList(basicQueryWrapper);

        List<PdsProductManagerProductQueryViewResponse> purchaseProductList = new ArrayList<>(basicList.stream()
                .filter(po -> po.getPurchaseSku() != null)
                .collect(Collectors.toMap(
                        PdsProductManagerBasicPo::getPurchaseSku, // key: purchaseSku
                        po -> {
                            PdsProductManagerProductQueryViewResponse data = new PdsProductManagerProductQueryViewResponse();
                            data.setPsku(po.getPurchaseSku());
                            data.setId(po.getId());
                            return data;
                        },
                        (existing, replacement) -> existing // 如果重复，保留第一个
                ))
                .values());



        return R.success(purchaseProductList);
    }
}
