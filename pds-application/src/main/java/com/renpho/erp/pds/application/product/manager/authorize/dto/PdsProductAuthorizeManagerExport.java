package com.renpho.erp.pds.application.product.manager.authorize.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 产品授权-运营管理导出数据结构.
 *
 * @since 2024.10.28
 */
@Data
public class PdsProductAuthorizeManagerExport {

    /**
     * 采购SKU
     */
    @ExcelProperty(value = "excel.authorize.purchase_sku", index = 0)
    private String purchaseSku;

    /**
     * 产品型号
     */
    @ExcelProperty(value = "excel.authorize.model_no", index = 1)
    private String modelNo;

    /**
     * 颜色
     */
    @ExcelProperty(value = "excel.authorize.color_name", index = 2)
    private String colorName;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "excel.authorize.product_name", index = 3)
    private String productName;

    /**
     * 品类
     */
    @ExcelProperty(value = "excel.authorize.category_name", index = 4)
    private String categoryName;

    /**
     * 品牌
     */
    @ExcelProperty(value = "excel.authorize.brand_name", index = 5)
    private String brandName;

    /**
     * 类型
     */
    @ExcelProperty(value = "excel.authorize.product_type", index = 6)
    private String productType;

    /**
     * 地区
     */
    @ExcelProperty(value = "excel.authorize.country_region_name", index = 7)
    private String countryRegionName;

    /**
     * 产品状态
     */
    @ExcelProperty(value = "excel.authorize.product_status", index = 8)
    private String productStatus;

    /**
     * 产品经理
     */
    @ExcelProperty(value = "excel.authorize.product_manager_name", index = 9)
    private String productManagerName;

    /**
     * 授权运营管理
     */
    @ExcelProperty(value = "excel.authorize.operations_manager_name", index = 10)
    private String operationsManagerName;

    /**
     * 授权运营人员
     */
    @ExcelProperty(value = "excel.authorize.authorized_user_name", index = 11)
    private String authorizedUserName;

    /**
     * 授权时间
     */
    @ExcelProperty(value = "excel.authorize.authorized_time", format = "yyyy-MM-dd HH:mm:ss", index = 12)
    private Date authorizedTime;

    /**
     * 确认授权时间
     */
    @ExcelProperty(value = "excel.authorize.create_time", format = "yyyy-MM-dd HH:mm:ss", index = 13)
    private Date createTime;
}


