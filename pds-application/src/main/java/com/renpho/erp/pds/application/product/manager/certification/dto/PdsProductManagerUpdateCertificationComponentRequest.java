package com.renpho.erp.pds.application.product.manager.certification.dto;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 产品管理-认证-部件表.
 *
 * <AUTHOR>
 * @since 2024.11.18
 */
@Data
public class PdsProductManagerUpdateCertificationComponentRequest {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 关联的产品管理主键ID
     */
    private Integer productManagerId;

    /**
     * 部件类型
     */
    private String componentType;

    /**
     * 认证型号
     */
    private String certificationModel;

    /**
     * 认证名称
     */
    private String certificationName;

    /**
     * 认证标准
     */
    private String certificationStandard;

    /**
     * 有效时间（开始）
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date effectiveTimeStart;

    /**
     * 有效时间（结束）
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date effectiveTimeEnd;

    /**
     * 附件,最多10个附件
     */
    private String attachmentIds;

    /**
     * 附件,最多10个附件
     */
    private List<String> attachmentIdList;

    /**
     * 备注
     */
    private String remark;

    /**
     * 数据状态 0 已有/ 1 缺失
     */
    private Integer dataStatus;

    public String getAttachmentIds() {
        if (attachmentIdList != null) {
            return JSON.toJSONString(attachmentIdList);
        }
        return null;
    }
}

