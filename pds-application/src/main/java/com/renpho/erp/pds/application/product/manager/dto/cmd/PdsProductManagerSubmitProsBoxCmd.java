package com.renpho.erp.pds.application.product.manager.dto.cmd;

import com.renpho.erp.pds.domain.common.LanguageEnum;
import lombok.Data;

/**
 * 产品管理-属性信息-新增箱规信息
 *
 * <AUTHOR> @since 2024/11/04
 */
@Data
public class PdsProductManagerSubmitProsBoxCmd {

    /**
     * 产品管理-外箱规格-主键ID, 注意: 更新操作时候，如果ID为空则表示新增，如果ID不为空则表示更新
     */
    private Integer id;

    /**
     * 箱规名称
     */
    private String boxSpecificationName;

    /**
     * 装箱数量
     */
    private Integer numberOfUnitsPerBox;

    /**
     * 外箱尺寸-长 (cm)/公制
     */
    private Double boxLengthMetric;

    /**
     * 外箱尺寸-宽 (cm)/公制
     */
    private Double boxWidthMetric;

    /**
     * 外箱尺寸-高 (cm)/公制
     */
    private Double boxHeightMetric;

    /**
     * 整箱毛重 (kg)/公制
     */
    private Double grossWeightPerBoxMetric;

    /**
     * 是否为生效外箱规格: 0=InActive，1=Active
     */
    private Integer active;

    /**
     * 校验字段内容是否合法
     *
     * @param language 语言类型
     * @param isStrict 是否严格模式
     */
    public void validateFields(LanguageEnum language, boolean isStrict) {
        validateLengthField(isStrict, language, boxSpecificationName, 64,
                "箱规名称不能为空", "Box specification name cannot be null",
                "箱规名称不能超过64个字符", "Box specification name cannot exceed 64 characters");
        validateRangeField(isStrict, language, numberOfUnitsPerBox, 1, 9999,
                "装箱数量不能为空", "Number of units per box cannot be null",
                "装箱数量仅支持正整数，且最小值为1", "Number of units per box must be a positive integer, minimum 1",
                "装箱数量仅支持正整数，且最大值为9999", "Number of units per box must be a positive integer, maximum 9999");
        validateDecimalField(isStrict, language, boxLengthMetric, 0.01, 99999.99, 5, 2,
                "外箱尺寸-长不能为空", "Box length cannot be null",
                "外箱尺寸-长必须大于0", "Box length must be greater than 0",
                "外箱尺寸-长不能超过99999.99", "Box length cannot exceed 99999.99",
                "外箱尺寸-长仅支持数字输入，小数最多保留2位", "Box length must be a number with up to 2 decimal places");
        validateDecimalField(isStrict, language, boxWidthMetric, 0.01, 99999.99, 5, 2,
                "外箱尺寸-宽不能为空", "Box width cannot be null",
                "外箱尺寸-宽必须大于0", "Box width must be greater than 0",
                "外箱尺寸-宽不能超过99999.99", "Box width cannot exceed 99999.99",
                "外箱尺寸-宽仅支持数字输入，小数最多保留2位", "Box width must be a number with up to 2 decimal places");
        validateDecimalField(isStrict, language, boxHeightMetric, 0.01, 99999.99, 5, 2,
                "外箱尺寸-高不能为空", "Box height cannot be null",
                "外箱尺寸-高必须大于0", "Box height must be greater than 0",
                "外箱尺寸-高不能超过99999.99", "Box height cannot exceed 99999.99",
                "外箱尺寸-高仅支持数字输入，小数最多保留2位", "Box height must be a number with up to 2 decimal places");
        validateDecimalField(isStrict, language, grossWeightPerBoxMetric, 0.01, 99999.99, 5, 2,
                "整箱毛重不能为空", "Gross weight per box cannot be null",
                "整箱毛重必须大于0", "Gross weight per box must be greater than 0",
                "整箱毛重不能超过99999.99", "Gross weight per box cannot exceed 99999.99",
                "整箱毛重仅支持数字输入，小数最多保留2位", "Gross weight per box must be a number with up to 2 decimal places");
    }

    /**
     * 校验外箱尺寸：长≥宽≥高
     */
    public void validateDimensions(LanguageEnum language) {
//        if (boxLengthMetric != null && boxWidthMetric != null && boxHeightMetric != null) {
//            if (!(boxLengthMetric >= boxWidthMetric && boxWidthMetric >= boxHeightMetric)) {
//                throwValidationException(language,
//                        "外箱尺寸必须满足长≥宽≥高",
//                        "Box dimensions must satisfy length ≥ width ≥ height"
//                );
//            }
//        }
    }

    private void validateLengthField(boolean isStrict, LanguageEnum language, String field, int maxLength,
                                     String nullMessageZh, String nullMessageEn,
                                     String lengthMessageZh, String lengthMessageEn) {
        if (isStrict || (field != null && !field.isEmpty())) {
            if (field == null || field.isEmpty()) {
                throwValidationException(language, nullMessageZh, nullMessageEn);
            }
            if (field.length() > maxLength) {
                throwValidationException(language, lengthMessageZh, lengthMessageEn);
            }
        }
    }

    private void validateRangeField(boolean isStrict, LanguageEnum language, Integer field, int min, int max,
                                    String nullMessageZh, String nullMessageEn,
                                    String minMessageZh, String minMessageEn,
                                    String maxMessageZh, String maxMessageEn) {
        if (isStrict || field != null) {
            if (field == null) {
                throwValidationException(language, nullMessageZh, nullMessageEn);
            }
            if (field < min) {
                throwValidationException(language, minMessageZh, minMessageEn);
            }
            if (field > max) {
                throwValidationException(language, maxMessageZh, maxMessageEn);
            }
        }
    }

    private void validateDecimalField(boolean isStrict, LanguageEnum language, Double field, double min, double max, int integerDigits, int fractionDigits,
                                      String nullMessageZh, String nullMessageEn,
                                      String minMessageZh, String minMessageEn,
                                      String maxMessageZh, String maxMessageEn,
                                      String formatMessageZh, String formatMessageEn) {
        if (isStrict || field != null) {
            if (field == null) {
                throwValidationException(language, nullMessageZh, nullMessageEn);
            }
            if (field < min) {
                throwValidationException(language, minMessageZh, minMessageEn);
            }
            if (field > max) {
                throwValidationException(language, maxMessageZh, maxMessageEn);
            }
            String[] parts = field.toString().split("\\.");
            if (parts[0].length() > integerDigits || (parts.length > 1 && parts[1].length() > fractionDigits)) {
                throwValidationException(language, formatMessageZh, formatMessageEn);
            }
        }
    }

    private void throwValidationException(LanguageEnum language, String chineseMessage, String englishMessage) {
        if (language == LanguageEnum.China) {
            throw new IllegalArgumentException(chineseMessage);
        } else {
            throw new IllegalArgumentException(englishMessage);
        }
    }
}
