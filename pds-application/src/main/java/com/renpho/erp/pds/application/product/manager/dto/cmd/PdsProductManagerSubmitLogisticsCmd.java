package com.renpho.erp.pds.application.product.manager.dto.cmd;

import com.renpho.erp.pds.domain.common.LanguageEnum;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.Arrays;
import java.util.List;

/**
 * 产品管理-物流信息
 *
 * <AUTHOR>
 * @since 2024/11/04
 */
@Data
public class PdsProductManagerSubmitLogisticsCmd {

    // 正则表达式：支持中文、英文、符号，限制80字符
    private static final java.util.regex.Pattern VALID_PATTERN = java.util.regex.Pattern.compile("^[\\p{L}\\p{N}\\p{Punct}\\s]{1,80}$");

    /**
     * 产品管理-物流信息-主键ID
     */
    private Integer id;

    /**
     * 材质（中文），非空，格式不限制，限制64字符
     */
    private String chineseMaterial;

    /**
     * 材质（英文），非空，限制不能包含中文输入，限制64字符
     */
    private String englishMaterial;

    /**
     * 用途，非空，限制1000字符
     */
    private String purpose;

    /**
     * 其他信息，非空，每行输入框限制80字符、添加最多10行
     */
    private List<String> otherInformationList;

    /**
     * 敏感属性(选中的),多个用`,`号隔开，说明: 0 锂电池、1 干电池、2 纯电、3 液体、4 凝胶、5 粉末、6 营养颗粒、7 活性炭，egg: 0,1
     */
    private String hazardousPros;

    /**
     * 锂电池名称，支持中文/英文/符号输入，限制80字符
     */
    private String lithiumBatteryName;

    /**
     * 锂电池型号，支持中文/英文/符号输入，限制80字符
     */
    private String lithiumBatteryModel;

    /**
     * 锂电池额定电压，支持中文/英文/符号输入，限制80字符
     */
    private String lithiumRatedVoltage;

    /**
     * 锂电池额定容量，支持中文/英文/符号输入，限制80字符
     */
    private String lithiumRatedCapacity;

    /**
     * 锂电池额定能量，支持中文/英文/符号输入，限制80字符
     */
    private String lithiumRatedEnergy;

    /**
     * 锂电池质量，支持中文/英文/符号输入，限制80字符
     */
    private String lithiumMass;

    /**
     * 锂电池UN编码
     */
    private String lithiumUn;

    /**
     * 锂电池电芯数量
     */
    private String lithiumCellCount;

    /**
     * 干电池电池数量
     */
    private String dryCount;

    /**
     * 纯电电池名称，支持中文/英文/符号输入，限制80字符
     */
    private String pureElectricityName;

    /**
     * 纯电电池型号，支持中文/英文/符号输入，限制80字符
     */
    private String pureElectricityModel;

    /**
     * 纯电额定电压，支持中文/英文/符号输入，限制80字符
     */
    private String pureRatedVoltage;

    /**
     * 纯电额定容量，支持中文/英文/符号输入，限制80字符
     */
    private String pureRatedCapacity;

    /**
     * 纯电额定能量，支持中文/英文/符号输入，限制80字符
     */
    private String pureRatedEnergy;

    /**
     * 纯电池质量，支持中文/英文/符号输入，限制80字符
     */
    private String pureMass;

    /**
     * 干电池名称，支持中文/英文/符号输入，限制80字符
     */
    private String dryBatteryName;

    /**
     * 干电池型号，支持中文/英文/符号输入，限制80字符
     */
    private String dryBatteryModel;

    /**
     * 干电池质量，支持中文/英文/符号输入，限制80字符
     */
    private String dryMass;

    /**
     * 液体名称，支持中文/英文/符号输入，限制80字符
     */
    private String liquidName;

    /**
     * 液体容积 (ml)，支持中文/英文/符号输入，限制80字符
     */
    private String liquidVolumeMl;

    /**
     * 液体成份信息，支持中文/英文/符号输入，限制80字符
     */
    private String liquidOther;

    /**
     * 凝胶名称，支持中文/英文/符号输入，限制80字符
     */
    private String gelName;

    /**
     * 凝胶容积 (ml)，支持中文/英文/符号输入，限制80字符
     */
    private String gelVolumeMl;

    /**
     * 凝胶成份信息，支持中文/英文/符号输入，限制80字符
     */
    private String gelOther;

    /**
     * 粉末名称，支持中文/英文/符号输入，限制80字符
     */
    private String powderName;

    /**
     * 粉末质量，支持中文/英文/符号输入，限制80字符
     */
    private String powderMass;

    /**
     * 粉末成份信息，支持中文/英文/符号输入，限制80字符
     */
    private String powderOther;

    /**
     * 营养颗粒名称，支持中文/英文/符号输入，限制80字符
     */
    private String nutritionalPelletsName;

    /**
     * 营养颗粒质量，支持中文/英文/符号输入，限制80字符
     */
    private String nutritionalPelletsMass;

    /**
     * 营养颗粒成份信息，支持中文/英文/符号输入，限制80字符
     */
    private String nutritionalPelletsOther;

    /**
     * 活性炭名称，支持中文/英文/符号输入，限制80字符
     */
    private String activatedCarbonName;

    /**
     * 活性炭质量，支持中文/英文/符号输入，限制80字符
     */
    private String activatedCarbonMass;

    /**
     * 活性炭成份信息，支持中文/英文/符号输入，限制80字符
     */
    private String activatedCarbonOther;

    /**
     * 参数校验
     * @param isStrict 是否严格校验
     * @return 校验结果，true 校验通过，false 校验失败
     */
    public boolean validateFields(LanguageEnum language, boolean isStrict) {
        // 基本字段校验
        validatePatternField(isStrict, language, chineseMaterial, "^.{1,64}$",
                "材质（中文）不能为空", "Material (Chinese) cannot be null",
                "材质（中文）不能超过64字符", "Material (Chinese) must not exceed 64 characters");

        validatePatternField(isStrict, language, englishMaterial, "^[^\\p{IsHan}]{1,64}$",
                "材质（英文）不能为空", "Material (English) cannot be null",
                "材质（英文）不能包含中文字符，限制64字符", "Material (English) must not contain Chinese characters and be up to 64 characters");

        validatePatternField(isStrict, language, purpose, "^.{0,1000}$",
                "用途不能为空", "Purpose cannot be null",
                "用途不能超过1000字符", "Purpose up to 1000 characters");
        if(otherInformationList!=null && !otherInformationList.isEmpty()){
            validateListField(isStrict, language, otherInformationList, 1, 10,
                    "其他信息不能为空", "Other information cannot be null",
                    "其他信息最多只能包含10行", "Other information can contain at most 10 rows",
                    80, "每行的其他信息不能超过80个字符", "Each line of other information must not exceed 80 characters");
        }

        if (hazardousPros == null || hazardousPros.isEmpty()) {
            return true; // 如果没有选中敏感属性，不需要校验其他字段
        }

        List<String> selectedPros = Arrays.asList(hazardousPros.split(","));

        try {
            // 校验选中项
            if (selectedPros.contains("0")) {
                validateLithiumFields(language);
            }
            if (selectedPros.contains("1")) {
                validateDryBatteryFields(language);
            }
            if (selectedPros.contains("2")) {
                validatePureElectricityFields(language);
            }
            if (selectedPros.contains("3")) {
                validateLiquidFields(language);
            }
            if (selectedPros.contains("4")) {
                validateGelFields(language);
            }
            if (selectedPros.contains("5")) {
                validatePowderFields(language);
            }
            if (selectedPros.contains("6")) {
                validateNutritionalPelletsFields(language);
            }
            if (selectedPros.contains("7")) {
                validateActivatedCarbonFields(language);
            }
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException(e.getMessage());
        }

        return true;
    }
    private void validatePatternField(boolean isStrict, LanguageEnum language, String field, String pattern,
                                      String nullMessageZh, String nullMessageEn,
                                      String formatMessageZh, String formatMessageEn) {
        if (isStrict || field != null) {
            if (field == null) {
                throwValidationException(language, nullMessageZh, nullMessageEn);
            }
            if (!field.matches(pattern)) {
                throwValidationException(language, formatMessageZh, formatMessageEn);
            }
        }
    }

    private void validateListField(boolean isStrict, LanguageEnum language, List<String> list, int min, int max,
                                   String nullMessageZh, String nullMessageEn,
                                   String maxMessageZh, String maxMessageEn,
                                   int maxLength, String lineMessageZh, String lineMessageEn) {
        if (isStrict || (list != null && !list.isEmpty())) {
            if (list == null || list.isEmpty()) {
                throwValidationException(language, nullMessageZh, nullMessageEn);
            }
            if (list.size() > max) {
                throwValidationException(language, maxMessageZh, maxMessageEn);
            }
            for (String line : list) {
                if (line != null && line.length() > maxLength) {
                    throwValidationException(language, lineMessageZh, lineMessageEn);
                }
            }
        }
    }


    private void validateLithiumFields(LanguageEnum language) {
        if (!isValidField(lithiumBatteryName) || !isValidField(lithiumBatteryModel) ||
                !isValidField(lithiumRatedVoltage) || !isValidField(lithiumRatedCapacity) ||
                !isValidField(lithiumRatedEnergy) || !isValidField(lithiumMass)) {
            throwValidationException(language, "锂电池信息不完整或格式错误", "Lithium battery information is incomplete or invalid");
        }
    }

    private void validateDryBatteryFields(LanguageEnum language) {
        if (!isValidField(dryBatteryName) || !isValidField(dryBatteryModel) ||
                !isValidField(dryMass)) {
            throwValidationException(language, "干电池信息不完整或格式错误", "Dry battery information is incomplete or invalid");
        }
    }

    private void validatePureElectricityFields(LanguageEnum language) {
        if (!isValidField(pureElectricityName) || !isValidField(pureElectricityModel) ||
                !isValidField(pureRatedVoltage) || !isValidField(pureRatedCapacity) ||
                !isValidField(pureRatedEnergy) || !isValidField(pureMass)) {
            throwValidationException(language, "纯电池信息不完整或格式错误", "Pure electricity information is incomplete or invalid");
        }
    }

    private void validateLiquidFields(LanguageEnum language) {
        if (!isValidField(liquidName) || !isValidField(liquidVolumeMl) ||
                !isValidField(liquidOther)) {
            throwValidationException(language, "液体信息不完整或格式错误", "Liquid information is incomplete or invalid");
        }
    }

    private void validateGelFields(LanguageEnum language) {
        if (!isValidField(gelName) || !isValidField(gelVolumeMl) ||
                !isValidField(gelOther)) {
            throwValidationException(language, "凝胶信息不完整或格式错误", "Gel information is incomplete or invalid");
        }
    }

    private void validatePowderFields(LanguageEnum language) {
        if (!isValidField(powderName) || !isValidField(powderMass) ||
                !isValidField(powderOther)) {
            throwValidationException(language, "粉末信息不完整或格式错误", "Powder information is incomplete or invalid");
        }
    }

    private void validateNutritionalPelletsFields(LanguageEnum language) {
        if (!isValidField(nutritionalPelletsName) || !isValidField(nutritionalPelletsMass) ||
                !isValidField(nutritionalPelletsOther)) {
            throwValidationException(language, "营养颗粒信息不完整或格式错误", "Nutritional pellets information is incomplete or invalid");
        }
    }

    private void validateActivatedCarbonFields(LanguageEnum language) {
        if (!isValidField(activatedCarbonName) || !isValidField(activatedCarbonMass) ||
                !isValidField(activatedCarbonOther)) {
            throwValidationException(language, "活性炭信息不完整或格式错误", "Activated carbon information is incomplete or invalid");
        }
    }

    private void throwValidationException(LanguageEnum language, String chineseMessage, String englishMessage) {
        if (language == LanguageEnum.China) {
            throw new IllegalArgumentException(chineseMessage);
        } else {
            throw new IllegalArgumentException(englishMessage);
        }
    }

    private boolean isValidField(String field) {
        return field != null && VALID_PATTERN.matcher(field).matches();
    }


}