package com.renpho.erp.pds.application.category.dto;

import lombok.Data;

/**
 * 品类分层导出基类.
 * <AUTHOR>
 * @since 2024.9.25
 */
@Data
public class PdsCategoryCodeLevelVo {

    private String codeLevelOne;
    private String nameLevelOne;
    private String codeLevelTwo;
    private String nameLevelTwo;
    private String codeLevelThree;
    private String nameLevelThree;
    private String remark;
    private int status;

    public PdsCategoryCodeLevelVo(String codeLevelOne, String nameLevelOne,
                                  String codeLevelTwo, String nameLevelTwo,
                                  String codeLevelThree, String nameLevelThree, String remark, int status) {
        this.codeLevelOne = codeLevelOne;
        this.nameLevelOne = nameLevelOne;
        this.codeLevelTwo = codeLevelTwo;
        this.nameLevelTwo = nameLevelTwo;
        this.codeLevelThree = codeLevelThree;
        this.nameLevelThree = nameLevelThree;
        this.remark = remark;
        this.status = status;
    }

}
