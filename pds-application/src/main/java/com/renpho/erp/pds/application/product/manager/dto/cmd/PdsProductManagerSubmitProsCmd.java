package com.renpho.erp.pds.application.product.manager.dto.cmd;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.renpho.erp.pds.domain.common.LanguageEnum;
import jakarta.validation.Valid;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 产品管理-属性信息
 *
 * <AUTHOR> @since 2024/11/04
 */
@Data
public class PdsProductManagerSubmitProsCmd implements VO {

    /**
     * 产品管理-属性信息-主键ID
     */
    private Integer id;

    /**
     * 单品尺寸-长 (cm)/公制
     */
    private Double productLengthMetric;

    /**
     * 单品尺寸-宽 (cm)/公制
     */
    private Double productWidthMetric;

    /**
     * 单品尺寸-高 (cm)/公制
     */
    private Double productHeightMetric;

    /**
     * 单品净重 (kg)/公制
     */
    private Double productWeightMetric;

    /**
     * 单品数量
     */
    private Integer productQuantity;

    /**
     * 单品备注
     */
    private String productRemarks;

    /**
     * 包装尺寸(即彩盒尺寸)-长 (cm)/公制
     */
    private Double packagingLengthMetric;

    /**
     * 包装尺寸(即彩盒尺寸)-宽 (cm)/公制
     */
    private Double packagingWidthMetric;

    /**
     * 包装尺寸(即彩盒尺寸)-高 (cm)/公制
     */
    private Double packagingHeightMetric;

    /**
     * 单品毛重 (kg)/公制
     */
    private Double grossWeightMetric;

    /**
     * 产品单位,字典
     */
    @Trans(type = TransType.DICTIONARY, key = "product_unit", ref = "productUnitName")
    private Integer productUnit;

    /**
     * 产品单位名称,来源于productUnit
     */
    private String productUnitName;

    /** 外箱规格 */
    @Valid
    private List<PdsProductManagerSubmitProsBoxCmd> boxList;


    /* 仅仅导入缓存使用 */

    /**
     * 产品单位,字典 product_unit
     */
    private String productUnitPros;

    /**
     * 校验字典是否合法
     */
    public void checkDict(LanguageEnum language){
        if(StringUtils.isBlank(productUnitName)){
            throwValidationException(language,
                    "产品单位参数不合法",
                    "Product unit parameters are illegal"
            );
        }
    }

    public void addBox(PdsProductManagerSubmitProsBoxCmd box){
        if(boxList == null){
            boxList = new ArrayList<>();
        }
        boxList.add(box);
    }

    /**
     * 校验产品尺寸：长≥宽≥高
     */
    public void validateDimensions(LanguageEnum language) {
//        if (productLengthMetric != null && productWidthMetric != null && productHeightMetric != null) {
//            if (!(productLengthMetric >= productWidthMetric && productWidthMetric >= productHeightMetric)) {
//                throwValidationException(language,
//                        "单品尺寸必须满足长≥宽≥高",
//                        "Product dimensions must satisfy length ≥ width ≥ height"
//                );
//            }
//        }
//        if (packagingLengthMetric != null && packagingWidthMetric != null && packagingHeightMetric != null) {
//            if (!(packagingLengthMetric >= packagingWidthMetric && packagingWidthMetric >= packagingHeightMetric)) {
//                throwValidationException(language,
//                        "包装尺寸(即彩盒尺寸)必须满足长≥宽≥高",
//                        "Packaging dimensions must satisfy length ≥ width ≥ height"
//                );
//            }
//        }
    }

    /**
     * 校验重量关系：箱单毛重 ≥ 单品毛重 ≥ 单品净重
     */
    public void validateWeights(LanguageEnum language) {
//        // 检查单品毛重和单品净重关系
//        if (grossWeightMetric != null && productWeightMetric != null) {
//            if (!(grossWeightMetric >= productWeightMetric)) {
//                throwValidationException(language,
//                        "单品毛重必须大于等于单品净重",
//                        "Gross weight must be greater than or equal to product weight"
//                );
//            }
//        }
//
//        // 检查每个箱单的毛重和单品毛重关系
//        if (boxList != null) {
//            for (PdsProductManagerSubmitProsBoxCmd box : boxList) {
//                Double grossWeightPerBoxMetric = box.getGrossWeightPerBoxMetric();
//                if (grossWeightPerBoxMetric != null && grossWeightMetric != null) {
//                    if (!(grossWeightPerBoxMetric >= grossWeightMetric)) {
//                        throwValidationException(language,
//                                "箱单毛重必须大于等于单品毛重",
//                                "Box gross weight must be greater than or equal to product gross weight"
//                        );
//                    }
//                }
//            }
//        }
    }

    /**
     * 校验字段内容是否合法
     *
     * @param language 语言类型
     * @param isStrict 是否严格模式
     */
    public void validateFields(LanguageEnum language, boolean isStrict) {
        validateDecimalField(isStrict, language, productLengthMetric, 0.01, 99999.99, 5, 2,
                "单品尺寸-长不能为空", "Product length cannot be null",
                "单品尺寸-长必须大于0", "Product length must be greater than 0",
                "单品尺寸-长不能超过99999.99", "Product length cannot exceed 99999.99",
                "单品尺寸-长仅支持数字输入，小数最多保留2位", "Product length must be a number with up to 2 decimal places");
        validateDecimalField(isStrict, language, productWidthMetric, 0.01, 99999.99, 5, 2,
                "单品尺寸-宽不能为空", "Product width cannot be null",
                "单品尺寸-宽必须大于0", "Product width must be greater than 0",
                "单品尺寸-宽不能超过99999.99", "Product width cannot exceed 99999.99",
                "单品尺寸-宽仅支持数字输入，小数最多保留2位", "Product width must be a number with up to 2 decimal places");
        validateDecimalField(isStrict, language, productHeightMetric, 0.01, 99999.99, 5, 2,
                "单品尺寸-高不能为空", "Product height cannot be null",
                "单品尺寸-高必须大于0", "Product height must be greater than 0",
                "单品尺寸-高不能超过99999.99", "Product height cannot exceed 99999.99",
                "单品尺寸-高仅支持数字输入，小数最多保留2位", "Product height must be a number with up to 2 decimal places");
        validateDecimalField(isStrict, language, productWeightMetric, 0.001, 9999.999, 4, 3,
                "单品净重不能为空", "Product weight cannot be null",
                "单品净重必须大于0", "Product weight must be greater than 0",
                "单品净重不能超过9999.999", "Product weight cannot exceed 9999.999",
                "单品净重仅支持数字输入，小数最多保留3位", "Product weight must be a number with up to 3 decimal places");
        validateRangeField(isStrict, language, productQuantity, 1, 9999,
                "单品数量不能为空", "Product quantity cannot be null",
                "单品数量仅支持正整数，且最小值为1", "Product quantity must be a positive integer, minimum 1",
                "单品数量仅支持正整数，且最大值为9999", "Product quantity must be a positive integer, maximum 9999");
        if(StringUtils.isNotBlank(productRemarks)){
            validateLengthField(isStrict, language, productRemarks, 200,
                    "单品备注不能为空", "Product remarks cannot be null",
                    "单品备注不能超过200个字符", "Product remarks cannot exceed 200 characters");
        }
        validateDecimalField(isStrict, language, packagingLengthMetric, 0.01, 99999.99, 5, 2,
                "包装尺寸(即彩盒尺寸)-长不能为空", "Package Dimensions length cannot be null",
                "包装尺寸(即彩盒尺寸)-长必须大于0", "Package Dimensions length must be greater than 0",
                "包装尺寸(即彩盒尺寸)-长不能超过99999.99", "Package Dimensions length cannot exceed 99999.99",
                "包装尺寸(即彩盒尺寸)-长仅支持数字输入，小数最多保留2位", "Package Dimensions length must be a number with up to 2 decimal places");
        validateDecimalField(isStrict, language, packagingWidthMetric, 0.01, 99999.99, 5, 2,
                "包装尺寸(即彩盒尺寸)-宽不能为空", "Package Dimensions width cannot be null",
                "包装尺寸(即彩盒尺寸)-宽必须大于0", "Package Dimensions width must be greater than 0",
                "包装尺寸(即彩盒尺寸)-宽不能超过99999.99", "Package Dimensions width cannot exceed 99999.99",
                "包装尺寸(即彩盒尺寸)-宽仅支持数字输入，小数最多保留2位", "Package Dimensions width must be a number with up to 2 decimal places");
        validateDecimalField(isStrict, language, packagingHeightMetric, 0.01, 99999.99, 5, 2,
                "包装尺寸(即彩盒尺寸)-高不能为空", "Package Dimensions height cannot be null",
                "包装尺寸(即彩盒尺寸)-高必须大于0", "Package Dimensions height must be greater than 0",
                "包装尺寸(即彩盒尺寸)-高不能超过99999.99", "Package Dimensions height cannot exceed 99999.99",
                "包装尺寸(即彩盒尺寸)-高仅支持数字输入，小数最多保留2位", "Package Dimensions height must be a number with up to 2 decimal places");
        validateDecimalField(isStrict, language, grossWeightMetric, 0.001, 9999.999, 4, 3,
                "单品毛重不能为空", "Gross weight cannot be null",
                "单品毛重必须大于0", "Gross weight must be greater than 0",
                "单品毛重不能超过9999.999", "Gross weight cannot exceed 9999.999",
                "单品毛重仅支持数字输入，小数最多保留3位", "Gross weight must be a number with up to 3 decimal places");

        validateListField(isStrict, language, boxList, 1, 20,
                "外箱规格不能为空", "Box specifications cannot be null",
                "外箱规格最多包含20个条目", "Box specifications can contain at most 20 entries",
                (box) -> box.validateFields(language, isStrict));
    }

    private void validateLengthField(boolean isStrict, LanguageEnum language, String field, int maxLength,
                                     String nullMessageZh, String nullMessageEn,
                                     String lengthMessageZh, String lengthMessageEn) {
        if (isStrict || (field != null && !field.isEmpty())) {
            if (field == null || field.isEmpty()) {
                throwValidationException(language, nullMessageZh, nullMessageEn);
            }
            if (field.length() > maxLength) {
                throwValidationException(language, lengthMessageZh, lengthMessageEn);
            }
        }
    }

    private void validateRangeField(boolean isStrict, LanguageEnum language, Integer field, int min, int max,
                                    String nullMessageZh, String nullMessageEn,
                                    String minMessageZh, String minMessageEn,
                                    String maxMessageZh, String maxMessageEn) {
        if (isStrict || field != null) {
            if (field == null) {
                throwValidationException(language, nullMessageZh, nullMessageEn);
            }
            if (field < min) {
                throwValidationException(language, minMessageZh, minMessageEn);
            }
            if (field > max) {
                throwValidationException(language, maxMessageZh, maxMessageEn);
            }
        }
    }

    private void validateDecimalField(boolean isStrict, LanguageEnum language, Double field, double min, double max, int integerDigits, int fractionDigits,
                                      String nullMessageZh, String nullMessageEn,
                                      String minMessageZh, String minMessageEn,
                                      String maxMessageZh, String maxMessageEn,
                                      String formatMessageZh, String formatMessageEn) {
        if (isStrict || field != null) {
            if (field == null) {
                throwValidationException(language, nullMessageZh, nullMessageEn);
            }
            if (field < min) {
                throwValidationException(language, minMessageZh, minMessageEn);
            }
            if (field > max) {
                throwValidationException(language, maxMessageZh, maxMessageEn);
            }
            String[] parts = field.toString().split("\\.");
            if (parts[0].length() > integerDigits || (parts.length > 1 && parts[1].length() > fractionDigits)) {
                throwValidationException(language, formatMessageZh, formatMessageEn);
            }
        }
    }

    private <T> void validateListField(boolean isStrict, LanguageEnum language, List<T> list, int min, int max,
                                       String nullMessageZh, String nullMessageEn,
                                       String sizeMessageZh, String sizeMessageEn,
                                       java.util.function.Consumer<T> validateItem) {
        if (isStrict || (list != null && !list.isEmpty())) {
            if (list == null || list.isEmpty()) {
                throwValidationException(language, nullMessageZh, nullMessageEn);
            }
            if (list.size() < min || list.size() > max) {
                throwValidationException(language, sizeMessageZh, sizeMessageEn);
            }
            for (T item : list) {
                validateItem.accept(item);
            }
        }
    }

    private void throwValidationException(LanguageEnum language, String chineseMessage, String englishMessage) {
        if (language == LanguageEnum.China) {
            throw new IllegalArgumentException(chineseMessage);
        } else {
            throw new IllegalArgumentException(englishMessage);
        }
    }
}
