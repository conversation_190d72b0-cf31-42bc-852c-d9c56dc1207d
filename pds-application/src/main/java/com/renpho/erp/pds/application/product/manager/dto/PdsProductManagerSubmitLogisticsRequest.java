package com.renpho.erp.pds.application.product.manager.dto;

import lombok.Data;

import java.util.List;


@Data
public class PdsProductManagerSubmitLogisticsRequest {
    /**
     * 材质（中文）
     */
    private String chineseMaterial;

    /**
     * 材质（英文）
     */
    private String englishMaterial;

    /**
     * 用途
     */
    private String purpose;

    /**
     * 敏感属性(选中的),多个用`,`号隔开，说明: 0 锂电池、1 干电池、2 纯电、3 液体、4 凝胶、5 粉末、6 营养颗粒、7 活性炭，egg: 0,1
     */
    private String hazardousPros;

    /**
     * 锂电池名称，支持中文/英文/符号输入，限制80字符
     */
    private String lithiumBatteryName;

    /**
     * 锂电池型号，支持中文/英文/符号输入，限制80字符
     */
    private String lithiumBatteryModel;

    /**
     * 锂电池额定电压，支持中文/英文/符号输入，限制80字符
     */
    private String lithiumRatedVoltage;

    /**
     * 锂电池额定容量，支持中文/英文/符号输入，限制80字符
     */
    private String lithiumRatedCapacity;

    /**
     * 锂电池额定能量，支持中文/英文/符号输入，限制80字符
     */
    private String lithiumRatedEnergy;

    /**
     * 锂电池质量，支持中文/英文/符号输入，限制80字符
     */
    private String lithiumMass;

    /**
     * 锂电池UN编码
     */
    private String lithiumUn;

    /**
     * 锂电池电芯数量
     */
    private String lithiumCellCount;

    /**
     * 干电池电池数量
     */
    private String dryCount;

    /**
     * 纯电电池名称，支持中文/英文/符号输入，限制80字符
     */
    private String pureElectricityName;

    /**
     * 纯电电池型号，支持中文/英文/符号输入，限制80字符
     */
    private String pureElectricityModel;

    /**
     * 纯电额定电压，支持中文/英文/符号输入，限制80字符
     */
    private String pureRatedVoltage;

    /**
     * 纯电额定容量，支持中文/英文/符号输入，限制80字符
     */
    private String pureRatedCapacity;

    /**
     * 纯电额定能量，支持中文/英文/符号输入，限制80字符
     */
    private String pureRatedEnergy;

    /**
     * 纯电池质量，支持中文/英文/符号输入，限制80字符
     */
    private String pureMass;

    /**
     * 干电池名称，支持中文/英文/符号输入，限制80字符
     */
    private String dryBatteryName;

    /**
     * 干电池型号，支持中文/英文/符号输入，限制80字符
     */
    private String dryBatteryModel;

    /**
     * 干电池质量，支持中文/英文/符号输入，限制80字符
     */
    private String dryMass;

    /**
     * 液体名称，支持中文/英文/符号输入，限制80字符
     */
    private String liquidName;

    /**
     * 液体容积 (ml)，支持中文/英文/符号输入，限制80字符
     */
    private String liquidVolumeMl;

    /**
     * 液体其他信息，支持中文/英文/符号输入，限制80字符
     */
    private String liquidOther;

    /**
     * 凝胶名称，支持中文/英文/符号输入，限制80字符
     */
    private String gelName;

    /**
     * 凝胶容积 (ml)，支持中文/英文/符号输入，限制80字符
     */
    private String gelVolumeMl;

    /**
     * 凝胶其他信息，支持中文/英文/符号输入，限制80字符
     */
    private String gelOther;

    /**
     * 粉末名称，支持中文/英文/符号输入，限制80字符
     */
    private String powderName;

    /**
     * 粉末质量，支持中文/英文/符号输入，限制80字符
     */
    private String powderMass;

    /**
     * 粉末其他信息，支持中文/英文/符号输入，限制80字符
     */
    private String powderOther;

    /**
     * 营养颗粒名称，支持中文/英文/符号输入，限制80字符
     */
    private String nutritionalPelletsName;

    /**
     * 营养颗粒质量，支持中文/英文/符号输入，限制80字符
     */
    private String nutritionalPelletsMass;

    /**
     * 营养颗粒其他信息，支持中文/英文/符号输入，限制80字符
     */
    private String nutritionalPelletsOther;

    /**
     * 活性炭名称，支持中文/英文/符号输入，限制80字符
     */
    private String activatedCarbonName;

    /**
     * 活性炭质量，支持中文/英文/符号输入，限制80字符
     */
    private String activatedCarbonMass;

    /**
     * 活性炭其他信息，支持中文/英文/符号输入，限制80字符
     */
    private String activatedCarbonOther;

    /**
     * 其他信息，text格式，非必填
     */
    private List<String> otherInformationList;
}