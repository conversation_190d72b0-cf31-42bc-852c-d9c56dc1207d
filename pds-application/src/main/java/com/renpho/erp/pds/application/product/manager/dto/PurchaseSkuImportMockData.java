package com.renpho.erp.pds.application.product.manager.dto;

import java.util.ArrayList;
import java.util.List;

/**
 * 产品管理-采购SKU导入-模拟数据
 *
 * @since 2024/11/08
 */
public class PurchaseSkuImportMockData {

    public static List<PurchaseSkuImport> getMockData() {
        List<PurchaseSkuImport> skuDatas = new ArrayList<>();

        // 创建第一个SKU数据
        PurchaseSkuImport sku1 = new PurchaseSkuImport();

        // 基础信息
        sku1.setProductCoverImageIdBasic("https://renpho-new-erp-parent.s3.cn-northwest-1.amazonaws.com.cn/dev/pds/product/pds/67208d25e4b0807dff7a2050.png");
        sku1.setCountryRegionCodeBasic("AC");
        sku1.setProductModelNoBasic("006-AAB04");
        sku1.setColorCodeBasic("DDDG");
        // sku1.setSalesChannelCodeBasic("AMZ");
        sku1.setChannelTypeBasic("线上平台");
        sku1.setVersionBasic("V1");
        sku1.setPurchaseSkuBasic("PUS-ES-28ML-BK-A30");
        sku1.setAttributeEncodingBasic("01");
        sku1.setChineseNameBasic("导入的产品");
        sku1.setEnglishNameBasic("importProduct");
        sku1.setMarketNameBasic("Market Product 1");
        sku1.setProductDescriptionBasic("描述1");
        sku1.setProductSellingPointsBasic("卖点1");
        sku1.setRemarkBasic("这是一个备注");
        sku1.setProductManagerCodeBasic("FT000002");
        sku1.setBusinessManagerCodeBasic("FT000002");
        sku1.setProcurementManagerCodeBasic("FT000002");
        sku1.setPlanningManagerCodeBasic("FT000002");
        sku1.setComplianceManagerCodeBasic("FT000002");
        sku1.setPackingMaterialManagerCodeBasic("FT000002");
        sku1.setAdapterIncludedBasic("是");
        sku1.setProductTypeBasic("成品");

        // 属性信息
        sku1.setProductLengthMetricPros(20.5);
        sku1.setProductWidthMetricPros(10.2);
        sku1.setProductHeightMetricPros(5.5);
        sku1.setProductWeightMetricPros(1.2);
        sku1.setProductQuantityPros(100);
        sku1.setProductRemarksPros("单品备注信息");
        sku1.setPackagingLengthMetricPros(21.5);
        sku1.setPackagingWidthMetricPros(11.0);
        sku1.setPackagingHeightMetricPros(6.0);
        sku1.setGrossWeightMetricPros(1.5);
        sku1.setProductUnitPros("件");

        // 属性-外箱规格信息
        sku1.setBoxSpecificationNameProsBox("Box Spec 1");
        sku1.setNumberOfUnitsPerBoxProsBox(10);
        sku1.setBoxLengthMetricProsBox(30.0);
        sku1.setBoxWidthMetricProsBox(20.0);
        sku1.setBoxHeightMetricProsBox(15.0);
        sku1.setGrossWeightPerBoxMetricProsBox(15.0);

        // 物流信息
        sku1.setChineseMaterialLogistics("金属");
        sku1.setEnglishMaterialLogistics("Metal");
        sku1.setPurposeLogistics("家用");
        sku1.setOtherInformationLogistics("其他信息1");
        sku1.setLithiumBatterySelectedLogistics("是");
        sku1.setLithiumBatteryNameLogistics("锂电池001");
        sku1.setLithiumBatteryModelLogistics("LB001");
        sku1.setLithiumRatedVoltageLogistics("3.7V");
        sku1.setLithiumRatedCapacityLogistics("2000mAh");
        sku1.setLithiumRatedEnergyLogistics("7.4Wh");
        sku1.setLithiumMassLogistics("50g");
        sku1.setPureElectricitySelectedLogistics("是");
        sku1.setPureElectricityNameLogistics("纯电池001");
        sku1.setPureElectricityModelLogistics("PE001");
        sku1.setPureRatedVoltageLogistics("5V");
        sku1.setPureRatedCapacityLogistics("1500mAh");
        sku1.setPureRatedEnergyLogistics("7.5Wh");
        sku1.setPureMassLogistics("60g");
        sku1.setDryBatterySelectedLogistics("是");
        sku1.setDryBatteryNameLogistics("干电池");
        sku1.setDryBatteryModelLogistics("DB001");
        sku1.setDryMassLogistics("20g");
        sku1.setLiquidSelectedLogistics("是");
        sku1.setLiquidNameLogistics("液体A");
        sku1.setLiquidVolumeMlLogistics("100ml");
        sku1.setLiquidOtherLogistics("无其他信息");
        sku1.setGelSelectedLogistics("是");
        sku1.setGelNameLogistics("凝胶A");
        sku1.setGelVolumeMlLogistics("50ml");
        sku1.setGelOtherLogistics("无其他信息");
        sku1.setPowderSelectedLogistics("是");
        sku1.setPowderNameLogistics("粉末A");
        sku1.setPowderMassLogistics("200g");
        sku1.setPowderOtherLogistics("无其他信息");
        sku1.setNutritionalPelletsSelectedLogistics("是");
        sku1.setNutritionalPelletsNameLogistics("颗粒A");
        sku1.setNutritionalPelletsMassLogistics("100g");
        sku1.setNutritionalPelletsOtherLogistics("无其他信息");
        sku1.setActivatedCarbonSelectedLogistics("是");
        sku1.setActivatedCarbonNameLogistics("活性炭A");
        sku1.setActivatedCarbonMassLogistics("150g");
        sku1.setActivatedCarbonOtherLogistics("无其他信息");

        // 配件信息
        sku1.setFittingValidateSelected1("是");
        sku1.setImageIdFittingValidate1("https://renpho-new-erp-parent.s3.cn-northwest-1.amazonaws.com.cn/dev/pds/product/pds/67208d25e4b0807dff7a2050.png");
        sku1.setDescriptionFittingValidate1("配件描述1");
        sku1.setQuantityFittingValidate1(10);
        sku1.setUnitFittingValidate1("件");
        sku1.setApplicableRegionsIdsFittingValidate1("UK,US");

        sku1.setFittingValidateSelected2("是");
        sku1.setImageIdFittingValidate2("https://renpho-new-erp-parent.s3.cn-northwest-1.amazonaws.com.cn/dev/pds/product/pds/67208d25e4b0807dff7a2050.png");
        sku1.setDescriptionFittingValidate2("配件描述2");
        sku1.setQuantityFittingValidate2(5);
        sku1.setUnitFittingValidate2("件");
        sku1.setApplicableRegionsIdsFittingValidate2("UK,AC");

        sku1.setFittingValidateSelected3("是");
        sku1.setImageIdFittingValidate3("https://renpho-new-erp-parent.s3.cn-northwest-1.amazonaws.com.cn/dev/pds/product/pds/67208d25e4b0807dff7a2050.png");
        sku1.setDescriptionFittingValidate3("配件描述3");
        sku1.setQuantityFittingValidate3(15);
        sku1.setUnitFittingValidate3("件");
        sku1.setApplicableRegionsIdsFittingValidate3("AC,UK");

        skuDatas.add(sku1);

        return skuDatas;
    }
}

