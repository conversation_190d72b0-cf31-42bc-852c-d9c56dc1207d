package com.renpho.erp.pds.application.category.dto;

import com.renpho.erp.pds.domain.category.LanguageCategory;
import lombok.Data;

import java.util.List;

/**
 * 品牌表.
 *
 * <AUTHOR>
 * @since 2024.9.14
 */
@Data
public class PdsCategoryResponse {

    /**
     * 主键, 添加不需要，更新才需要
     */
    private Integer id;

    /**
     * 品类代码
     */
    private String cateCode;

    /**
     * 父级品类主键ID
     */
    private Integer parentId;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 备注
     */
    private String remark;

    /**
     * Menu Status 选项为 Active 和 Inactive 单选，默认选中Active
     */
    private Integer status;

    /**
     * 多语言
     */
    private List<LanguageCategory> names;

}