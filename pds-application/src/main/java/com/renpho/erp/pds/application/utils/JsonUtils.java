package com.renpho.erp.pds.application.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;

public class JsonUtils {

    /**
     * 判断一个字符串是否是合法的 JSON
     *
     * @param jsonString 待验证的字符串
     * @return true 如果是合法的 JSON，否则返回 false
     */
    public static boolean isValidJson(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return false; // 空字符串或 null 不是 JSON
        }

        try {
            // 尝试解析为 JSON 对象
            Object json = JSON.parse(jsonString);
            if (json instanceof JSONObject || json instanceof JSONArray) {
                return true; // 是合法的 JSON
            } else {
                return false; // JSON 必须是对象或数组
            }
        } catch (JSONException e) {
            return false; // 解析失败，说明不是 JSON
        }
    }
}

