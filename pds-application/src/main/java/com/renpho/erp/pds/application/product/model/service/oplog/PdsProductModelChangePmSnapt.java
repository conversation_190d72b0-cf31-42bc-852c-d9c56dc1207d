package com.renpho.erp.pds.application.product.model.service.oplog;

import com.alibaba.fastjson.JSONObject;
import com.renpho.erp.oplog.log.SnapshotDatatSource;
import com.renpho.erp.pds.application.product.model.service.PdsProductModelSrv;
import com.renpho.erp.pds.infrastructure.persistence.po.PdsProductModelPo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 产品型号-日志-导入变更负责人
 */
@Service
public class PdsProductModelChangePmSnapt implements SnapshotDatatSource {
    @Resource
    private PdsProductModelSrv pdsProductModelSrv;

    @Override
    public JSONObject getOldData(Object[] args) {
        return getData(args);
    }

    @Override
    public JSONObject getNewData(Object[] args, JSONObject result) {
        return getData(args);
    }


    private JSONObject getData(Object[] args) {
        if (args != null && args.length > 0) {
            JSONObject oldData = JSONObject.parseObject(JSONObject.toJSONString(args[0]));
            Integer id = oldData.getInteger("id");
            PdsProductModelPo po = pdsProductModelSrv.pdsProductModelDetail(id);
            return (JSONObject) JSONObject.toJSON(po);
        }
        return new JSONObject();
    }

    @Override
    public String getBsId(Object[] args, JSONObject result) {
        return String.valueOf(((PdsProductModelPo) args[0]).getId());
    }
}
