package com.renpho.erp.pds.application.category.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.renpho.erp.pds.infrastructure.utils.poi.Excel;
import lombok.Data;

/**
 * 产品型号-导入模板.
 */
@Data
public class PdsProductModelImportExport {
    @Excel(name = "产品型号")
    @ExcelProperty(value = "产品型号", index = 0)
    private String modelNo;

    @Excel(name = "产品经理工号")
    @ExcelProperty(value = "产品经理工号", index = 1)
    private String pmUserCode;
}
