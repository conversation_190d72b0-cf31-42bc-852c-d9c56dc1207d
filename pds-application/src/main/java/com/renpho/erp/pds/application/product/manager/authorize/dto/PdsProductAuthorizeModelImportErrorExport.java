package com.renpho.erp.pds.application.product.manager.authorize.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.renpho.erp.pds.infrastructure.utils.poi.Excel;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 产品型号-导入模板.
 */
@Data
public class PdsProductAuthorizeModelImportErrorExport {

    /**
     * 采购SKU
     */
    @Excel(name = "采购SKU")
    @ExcelProperty(value = "excel.purchase_sku", index = 0)
    private String purchaseSku;

    /**
     * 运营管理工号
     */
    @Excel(name = "运营管理工号")
    @ExcelProperty(value = "excel.manager_code", index = 1)
    private String managerCode;

    /**
     * 运营人员工号
     */
    @Excel(name = "运营人员工号")
    @ExcelProperty(value = "excel.personnel_code", index = 2)
    private String personnelCode;

    /**
     * 错误信息
     */
    @Excel(name = "错误信息")
    @ExcelProperty(value = "excel.error_msg", index = 3)
    private String errorMsg;

    public void appendErrorMsg(String newErrorMes) {
        if (StringUtils.isNoneBlank(this.errorMsg)) {
            this.errorMsg = this.errorMsg + " " + newErrorMes;
        } else {
            this.errorMsg = newErrorMes;
        }
    }
}
