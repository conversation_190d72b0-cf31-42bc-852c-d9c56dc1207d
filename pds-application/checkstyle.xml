<?xml version="1.0"?>
<!DOCTYPE module PUBLIC
        "-//Checkstyle//DTD Checkstyle Configuration 1.3//EN"
        "https://checkstyle.org/dtds/configuration_1_3.dtd">
<module name="Checker">

    <!-- 设定不同规则的严重性 -->
    <module name="TreeWalker">
        <!-- 将代码缩进设为“错误” -->
        <module name="Indentation">
            <property name="severity" value="warning"/>
        </module>

        <!-- 类注释检查，取消句号结尾限制 -->
        <module name="JavadocStyle">
            <property name="checkFirstSentence" value="false"/>
            <property name="severity" value="warning"/>
        </module>

        <!-- 方法长度限制 -->
        <module name="MethodLength">
            <property name="severity" value="error"/>
            <property name="max" value="200"/>
        </module>
    </module>

    <!-- 将行长度设为“告警” -->
    <module name="LineLength">
        <property name="severity" value="warning"/>
        <property name="max" value="240"/>
    </module>

</module>
