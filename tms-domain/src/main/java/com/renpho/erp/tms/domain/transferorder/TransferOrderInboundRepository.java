package com.renpho.erp.tms.domain.transferorder;

import org.jmolecules.ddd.types.Repository;

import java.util.List;

/**
 * 调拨单入库仓储接口
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
public interface TransferOrderInboundRepository extends Repository<TransferOrderInbound, TransferOrderInboundId> {

    /**
     * 保存调拨单入库记录
     *
     * @param transferOrderInbound 调拨单入库记录
     * @return 调拨单入库ID
     */
    TransferOrderInboundId save(TransferOrderInbound transferOrderInbound);

    /**
     * 更新调拨单入库记录
     *
     * @param transferOrderInbound 调拨单入库记录
     */
    void update(TransferOrderInbound transferOrderInbound);

    /**
     * 批量保存调拨单入库记录
     *
     * @param transferOrderInbounds 调拨单入库记录列表
     */
    void batchSave(List<TransferOrderInbound> transferOrderInbounds);

    /**
     * 删除调拨单入库记录
     *
     * @param transferOrderInboundId 调拨单入库ID
     */
    void delete(TransferOrderInboundId transferOrderInboundId);
}
