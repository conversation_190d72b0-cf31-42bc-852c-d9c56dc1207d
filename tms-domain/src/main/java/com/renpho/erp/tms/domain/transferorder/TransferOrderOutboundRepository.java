package com.renpho.erp.tms.domain.transferorder;

import org.jmolecules.ddd.types.Repository;

import java.util.List;

/**
 * 调拨单出库仓储接口
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
public interface TransferOrderOutboundRepository extends Repository<TransferOrderOutbound, TransferOrderOutboundId> {

    /**
     * 保存调拨单出库记录
     *
     * @param transferOrderOutbound 调拨单出库记录
     * @return 调拨单出库ID
     */
    TransferOrderOutboundId save(TransferOrderOutbound transferOrderOutbound);

    /**
     * 更新调拨单出库记录
     *
     * @param transferOrderOutbound 调拨单出库记录
     */
    void update(TransferOrderOutbound transferOrderOutbound);

    /**
     * 批量保存调拨单出库记录
     *
     * @param transferOrderOutbounds 调拨单出库记录列表
     */
    void batchSave(List<TransferOrderOutbound> transferOrderOutbounds);

    /**
     * 删除调拨单出库记录
     *
     * @param transferOrderOutboundId 调拨单出库ID
     */
    void delete(TransferOrderOutboundId transferOrderOutboundId);
}
