package com.renpho.erp.tms.domain.transferorder;

import org.jmolecules.ddd.types.Repository;

import java.util.List;

/**
 * 调拨单仓储接口
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
public interface TransferOrderRepository extends Repository<TransferOrder, TransferOrderId> {

    /**
     * 保存调拨单
     *
     * @param transferOrder 调拨单
     * @return 调拨单ID
     */
    TransferOrderId save(TransferOrder transferOrder);

    /**
     * 更新调拨单
     *
     * @param transferOrder 调拨单
     */
    void update(TransferOrder transferOrder);

    /**
     * 批量保存调拨单
     *
     * @param transferOrders 调拨单列表
     */
    void batchSave(List<TransferOrder> transferOrders);

    /**
     * 删除调拨单
     *
     * @param transferOrderId 调拨单ID
     */
    void delete(TransferOrderId transferOrderId);
}
