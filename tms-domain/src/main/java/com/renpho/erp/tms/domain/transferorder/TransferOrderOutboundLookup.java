package com.renpho.erp.tms.domain.transferorder;

import org.jmolecules.ddd.integration.AssociationResolver;

import java.util.List;
import java.util.Optional;

/**
 * 调拨单出库查询接口
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
public interface TransferOrderOutboundLookup extends AssociationResolver<TransferOrderOutbound, TransferOrderOutboundId> {

    /**
     * 根据调拨单ID查询出库记录
     *
     * @param tsId 调拨单ID
     * @return 出库记录列表
     */
    List<TransferOrderOutbound> findByTsId(Integer tsId);

    /**
     * 根据调拨单号查询出库记录
     *
     * @param tsNo 调拨单号
     * @return 出库记录列表
     */
    List<TransferOrderOutbound> findByTsNo(String tsNo);

    /**
     * 根据出库单号查询
     *
     * @param outboundNo 出库单号
     * @return 出库记录
     */
    Optional<TransferOrderOutbound> findByOutboundNo(String outboundNo);

    /**
     * 根据PSKU查询出库记录
     *
     * @param psku PSKU
     * @return 出库记录列表
     */
    List<TransferOrderOutbound> findByPsku(String psku);

    /**
     * 根据物流方式ID查询出库记录
     *
     * @param logisticsModeId 物流方式ID
     * @return 出库记录列表
     */
    List<TransferOrderOutbound> findByLogisticsModeId(Integer logisticsModeId);
}
