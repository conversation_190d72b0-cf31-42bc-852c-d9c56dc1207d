package com.renpho.erp.tms.domain.transferorder;

import org.jmolecules.ddd.integration.AssociationResolver;

import java.util.List;
import java.util.Optional;

/**
 * 调拨单查询接口
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
public interface TransferOrderLookup extends AssociationResolver<TransferOrder, TransferOrderId> {

    /**
     * 根据调拨单号查询
     *
     * @param tsNo 调拨单号
     * @return 调拨单
     */
    Optional<TransferOrder> findByTsNo(String tsNo);

    /**
     * 根据OMS单号查询
     *
     * @param orderNo OMS单号
     * @return 调拨单列表
     */
    List<TransferOrder> findByOrderNo(String orderNo);

    /**
     * 根据状态查询
     *
     * @param status 状态
     * @return 调拨单列表
     */
    List<TransferOrder> findByStatus(Integer status);

    /**
     * 根据货主ID查询
     *
     * @param ownerId 货主ID
     * @return 调拨单列表
     */
    List<TransferOrder> findByOwnerId(Integer ownerId);

    /**
     * 根据店铺ID查询
     *
     * @param storeId 店铺ID
     * @return 调拨单列表
     */
    List<TransferOrder> findByStoreId(Integer storeId);
}
