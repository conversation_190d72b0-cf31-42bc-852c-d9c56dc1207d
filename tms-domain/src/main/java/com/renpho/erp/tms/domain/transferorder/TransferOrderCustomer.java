package com.renpho.erp.tms.domain.transferorder;

import com.renpho.erp.tms.domain.common.CreatedContainer;
import com.renpho.erp.tms.domain.common.UpdatedContainer;
import com.renpho.erp.tms.domain.operator.Operator;
import lombok.Data;
import org.jmolecules.ddd.types.AggregateRoot;

import java.io.Serial;
import java.io.Serializable;

/**
 * 调拨单客户信息表
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Data
public class TransferOrderCustomer implements AggregateRoot<TransferOrderCustomer, TransferOrderCustomerId>, CreatedContainer, UpdatedContainer, Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private final TransferOrderCustomerId id;

    /**
     * 调拨单ID
     */
    private Integer tsId;

    /**
     * 调拨单号
     */
    private String tsNo;

    /**
     * 客户公司名
     */
    private String customerCompanyName;

    /**
     * 客户国家/地区
     */
    private String customerCountryCode;

    /**
     * 客户联系人
     */
    private String customerContact;

    /**
     * 客户电话
     */
    private String customerPhone;

    /**
     * 客户邮箱
     */
    private String customerEmail;

    /**
     * 创建信息
     */
    private Operator created;

    /**
     * 更新信息
     */
    private Operator updated;
}
