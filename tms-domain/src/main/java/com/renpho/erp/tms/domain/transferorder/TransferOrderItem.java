package com.renpho.erp.tms.domain.transferorder;

import com.renpho.erp.tms.domain.common.CreatedContainer;
import com.renpho.erp.tms.domain.common.UpdatedContainer;
import com.renpho.erp.tms.domain.operator.Operator;
import com.renpho.erp.tms.domain.owner.Owner;
import com.renpho.erp.tms.domain.product.Product;
import com.renpho.erp.tms.domain.saleschannel.SalesChannel;
import com.renpho.erp.tms.domain.store.Store;
import lombok.Data;
import org.jmolecules.ddd.types.AggregateRoot;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 调拨单商品表
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Data
public class TransferOrderItem implements AggregateRoot<TransferOrderItem, TransferOrderItemId>, CreatedContainer, UpdatedContainer, Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private final TransferOrderItemId id;

    /**
     * 调拨单ID
     */
    private Integer tsId;

    /**
     * 调拨单号
     */
    private String tsNo;

    /**
     * PSKU
     */
    private String psku;

    /**
     * FNSKU
     */
    private String fnsku;

    private Product product;

    /**
     * 销售渠道ID
     */
    private Integer salesChannelId;

    private SalesChannel salesChannel;

    /**
     * 店铺ID
     */
    private Integer storeId;

    private Store store;

    /**
     * 货主ID
     */
    private Integer ownerId;

    private Owner owner;

    /**
     * 出库单号
     */
    private String outboundNo;

    private TransferOrderOutbound outbound;

    /**
     * 发货数量
     */
    private Integer qty;

    /**
     * 换标完成时间
     */
    private LocalDateTime relabelFinishTime;

    /**
     * 销售金额
     */
    private BigDecimal saleAmount;

    /**
     * 箱唛文件ID
     */
    private List<String> cartonLabelFileIds;

    /**
     * ASN标签文件ID
     */
    private List<String> asnLabelFileId;

    /**
     * 出借方PSKU
     */
    private String borrowedPsku;

    /**
     * 出借方FNSKU
     */
    private String borrowedFnsku;

    /**
     * 新产品标签文件ID
     */
    private String newProductLabelFileId;

    /**
     * 出借方店铺ID
     */
    private Integer borrowedStoreId;

    /**
     * 出借方货主ID
     */
    private Integer borrowedOwnerId;

    /**
     * 签收数量
     */
    private Integer receivedQty;

    /**
     * 签收差异
     */
    private Integer receiptDiscrepancy;

    /**
     * 上架数量
     */
    private Integer putawayQty;

    /**
     * 上架差异
     */
    private Integer putawayDiscrepancy;

    /**
     * 尺寸单位
     */
    private String dimensionUnit;

    /**
     * 外箱尺寸-长
     */
    private BigDecimal boxLength;

    /**
     * 外箱尺寸-宽
     */
    private BigDecimal boxWidth;

    /**
     * 外箱尺寸-高
     */
    private BigDecimal boxHeight;

    /**
     * 重量单位
     */
    private String weightUnit;

    /**
     * 单品净重
     */
    private BigDecimal weight;

    /**
     * 单品毛重
     */
    private BigDecimal grossWeight;

    /**
     * 整箱毛重
     */
    private BigDecimal boxGrossWeight;

    /**
     * 装箱数量
     */
    private Integer quantityPerBox;

    /**
     * 创建信息
     */
    private Operator created;

    /**
     * 更新信息
     */
    private Operator updated;
}
