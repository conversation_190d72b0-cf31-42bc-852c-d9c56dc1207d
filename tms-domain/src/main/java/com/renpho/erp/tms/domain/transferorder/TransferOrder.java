package com.renpho.erp.tms.domain.transferorder;

import com.renpho.erp.tms.domain.common.CreatedContainer;
import com.renpho.erp.tms.domain.common.UpdatedContainer;
import com.renpho.erp.tms.domain.inbound.BizContainer;
import com.renpho.erp.tms.domain.operator.Operator;
import com.renpho.erp.tms.domain.operator.OperatorId;
import com.renpho.erp.tms.domain.owner.Owner;
import com.renpho.erp.tms.domain.owner.OwnerId;
import com.renpho.erp.tms.domain.saleschannel.SalesChannel;
import com.renpho.erp.tms.domain.saleschannel.SalesChannelId;
import com.renpho.erp.tms.domain.site.CountryRegion;
import com.renpho.erp.tms.domain.store.Store;
import com.renpho.erp.tms.domain.store.StoreId;
import com.renpho.erp.tms.domain.warehouse.Warehouse;
import com.renpho.erp.tms.domain.warehouse.WarehouseId;
import lombok.Data;
import org.jmolecules.ddd.types.AggregateRoot;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 调拨单主表
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Data
public class TransferOrder implements AggregateRoot<TransferOrder, TransferOrderId>, BizContainer, CreatedContainer, UpdatedContainer, Serializable {

    @Serial
    private static final long serialVersionUID = 7840268419798885335L;

    private final TransferOrderId id;

    /**
     * 调拨单单号
     */
    private String tsNo;

    /**
     * OMS的单号
     */
    private String orderNo;

    /**
     * OMS的订单参考号
     */
    private String refNo;

    /**
     * 调拨单状态, 字典: transfer_order_status
     */
    private TransferOrderStatus status;

    /**
     * 调拨单类型, 字典: transfer_order_type
     */
    private String type;

    /**
     * 业务类型, 字典: transfer_order_biz_type
     */
    private String bizType;

    /**
     * 差异单号
     */
    private String doNo;
    /**
     * 差异单状态
     */
    private String doStatus;

    /**
     * 数据来源, 判断是TMS创建还是上游传入
     */
    private TransferOrderDataSource dataSource;

    /**
     * 销售渠道ID
     */
    private SalesChannelId salesChannelId;

    private SalesChannel salesChannel;

    /**
     * 店铺ID
     */
    private StoreId storeId;

    private Store store;

    /**
     * 货主ID
     */
    private OwnerId ownerId;

    private Owner owner;

    /**
     * 发货仓
     */
    private WarehouseId shippingWarehouseId;

    private Warehouse shippingWarehouse;

    /**
     * 目的国/地区
     */
    private String destCountryCode;

    private CountryRegion destCountry;

    /**
     * 目的仓库ID
     */
    private WarehouseId destWarehouseId;

    /**
     * 目的仓Code
     */
    private String destWarehouseCode;

    private Warehouse destWarehouse;

    /**
     * 目的地
     */
    private String destAddress;

    /**
     * 贸易条款, 字典: trade_terms
     */
    private String tradeTerms;

    /**
     * 付款条款, 字典: payment_terms
     */
    private String paymentTerms;

    /**
     * 是否打托
     */
    private Boolean isPalletized;

    /**
     * 预估发货时间
     */
    private LocalDateTime estimatedDepartureTime;

    /**
     * 预估交货时间
     */
    private LocalDateTime estimatedDeliveryTime;

    /**
     * 实际发货时间
     */
    private LocalDateTime actualDepartureTime;

    /**
     * 实际交货时间
     */
    private LocalDateTime actualDeliveryTime;

    /**
     * 期望上架时间
     */
    private LocalDateTime expectedPutawayTime;

    /**
     * 总数量
     */
    private Integer qty;

    /**
     * 总箱数
     */
    private BigDecimal boxQty;

    /**
     * 总毛重
     */
    private BigDecimal grossWeight;

    /**
     * 总净重
     */
    private BigDecimal netWeight;

    /**
     * 总体积
     */
    private BigDecimal volume;

    /**
     * 提单类型, 字典: bill_of_lading_type
     */
    private String billOfLadingType;

    /**
     * 发票号
     */
    private String invoiceNo;

    /**
     * 允许发货, 0-不允许, 1-允许
     */
    private Boolean isAllowedShipping;

    /**
     * 关闭时间
     */
    private LocalDateTime closedTime;

    /**
     * 运营人员ID
     */
    private OperatorId salesStaffId;

    private Operator salesStaff;

    /**
     * 计划人员ID
     */
    private OperatorId planerStaffId;

    private Operator planerStaff;

    /**
     * 船务人员ID
     */
    private OperatorId shippingStaffId;

    private Operator shippingStaff;

    /**
     * 创建信息
     */
    private Operator created;

    /**
     * 更新信息
     */
    private Operator updated;

    /**
     * 调拨单商品列表
     */
    private List<TransferOrderItem> items;

    /**
     * 调拨单客户信息
     */
    private TransferOrderCustomer customer;

    @Override
    public String getBizNo() {
        return tsNo;
    }

    @Override
    public Integer getBizId() {
        return id.id();
    }
}
