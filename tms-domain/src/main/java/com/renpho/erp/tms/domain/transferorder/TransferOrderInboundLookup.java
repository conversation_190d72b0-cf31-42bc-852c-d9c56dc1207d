package com.renpho.erp.tms.domain.transferorder;

import org.jmolecules.ddd.integration.AssociationResolver;

import java.util.List;
import java.util.Optional;

/**
 * 调拨单入库查询接口
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
public interface TransferOrderInboundLookup extends AssociationResolver<TransferOrderInbound, TransferOrderInboundId> {

    /**
     * 根据调拨单ID查询入库记录
     *
     * @param tsId 调拨单ID
     * @return 入库记录列表
     */
    List<TransferOrderInbound> findByTsId(Integer tsId);

    /**
     * 根据调拨单号查询入库记录
     *
     * @param tsNo 调拨单号
     * @return 入库记录列表
     */
    List<TransferOrderInbound> findByTsNo(String tsNo);

    /**
     * 根据入库单号查询
     *
     * @param inboundNo 入库单号
     * @return 入库记录
     */
    Optional<TransferOrderInbound> findByInboundNo(String inboundNo);

    /**
     * 根据PSKU查询入库记录
     *
     * @param psku PSKU
     * @return 入库记录列表
     */
    List<TransferOrderInbound> findByPsku(String psku);

    /**
     * 根据FNSKU查询入库记录
     *
     * @param fnsku FNSKU
     * @return 入库记录列表
     */
    List<TransferOrderInbound> findByFnsku(String fnsku);
}
