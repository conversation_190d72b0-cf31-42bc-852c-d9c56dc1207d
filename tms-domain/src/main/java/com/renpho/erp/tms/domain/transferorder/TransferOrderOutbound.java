package com.renpho.erp.tms.domain.transferorder;

import com.renpho.erp.tms.domain.common.CreatedContainer;
import com.renpho.erp.tms.domain.common.UpdatedContainer;
import com.renpho.erp.tms.domain.operator.Operator;
import lombok.Data;
import org.jmolecules.ddd.types.AggregateRoot;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 调拨单出库表
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Data
public class TransferOrderOutbound implements AggregateRoot<TransferOrderOutbound, TransferOrderOutboundId>, CreatedContainer, UpdatedContainer, Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private final TransferOrderOutboundId id;

    /**
     * 调拨单ID
     */
    private Integer tsId;

    /**
     * 调拨单号
     */
    private String tsNo;

    /**
     * PSKU
     */
    private String psku;

    /**
     * FNSKU
     */
    private String fnsku;

    /**
     * 出库单号
     */
    private String outboundNo;

    /**
     * 数量
     */
    private Integer qty;

    /**
     * 发货时间
     */
    private LocalDateTime departureTime;

    /**
     * 物流方式ID
     */
    private Integer logisticsModeId;

    /**
     * 是否打托, 0-否, 1-是
     */
    private Boolean isPalletized;

    /**
     * 发货要求
     */
    private String deliveryRequirement;

    /**
     * 是否借货, 0-否, 1-是
     */
    private Boolean isBorrowed;

    /**
     * 是否换标, 0-否, 1-是
     */
    private Boolean isRelabel;

    /**
     * 创建信息
     */
    private Operator created;

    /**
     * 更新信息
     */
    private Operator updated;
}
