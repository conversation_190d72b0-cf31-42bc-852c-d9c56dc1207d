package com.renpho.erp.tms.domain.transferorder;

import com.renpho.erp.tms.domain.common.CreatedContainer;
import com.renpho.erp.tms.domain.common.UpdatedContainer;
import com.renpho.erp.tms.domain.operator.Operator;
import lombok.Data;
import org.jmolecules.ddd.types.AggregateRoot;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 调拨单入库表
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Data
public class TransferOrderInbound implements AggregateRoot<TransferOrderInbound, TransferOrderInboundId>, CreatedContainer, UpdatedContainer, Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private final TransferOrderInboundId id;

    /**
     * 调拨单ID
     */
    private Integer tsId;

    /**
     * 调拨单号
     */
    private String tsNo;

    /**
     * PSKU
     */
    private String psku;

    /**
     * FNSKU
     */
    private String fnsku;

    /**
     * 入库单号
     */
    private String inboundNo;

    /**
     * 数量
     */
    private Integer qty;

    /**
     * 期望上架时间
     */
    private LocalDateTime expectedPutawayTime;

    /**
     * 签收开始时间
     */
    private LocalDateTime receivedStartTime;

    /**
     * 签收结束时间
     */
    private LocalDateTime receivedEndTime;

    /**
     * 上架开始时间
     */
    private LocalDateTime putawayStartTime;

    /**
     * 上架结束时间
     */
    private LocalDateTime putawayEndTime;

    /**
     * 创建信息
     */
    private Operator created;

    /**
     * 更新信息
     */
    private Operator updated;
}
