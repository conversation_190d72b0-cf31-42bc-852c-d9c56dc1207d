<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.renpho</groupId>
        <artifactId>renpho-build</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <groupId>com.renpho.erp</groupId>
    <artifactId>ftm-parent</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>${project.artifactId}</name>
    <description>ERP：文件传输管理系统（File transfer manage system），父POM</description>

    <modules>
        <module>ftm-domain</module>
        <module>ftm-infrastructure</module>
        <module>ftm-application</module>
        <module>ftm-client</module>
        <module>ftm-adapter</module>
        <module>ftm-start</module>
    </modules>

    <properties>
        <!-- Project revision -->
        <revision>1.0.0-SNAPSHOT</revision>
        <karma.version>1.0.0-SNAPSHOT</karma.version>
        <soraka.version>1.0.0-SNAPSHOT</soraka.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- ==================================== -->
            <!-- 框架依赖 -->
            <!-- ==================================== -->
            <dependency>
                <groupId>com.renpho.soraka</groupId>
                <artifactId>soraka-dependencies</artifactId>
                <version>${soraka.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.renpho.soraka</groupId>
                <artifactId>soraka-bom</artifactId>
                <version>${soraka.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.renpho.karma</groupId>
                <artifactId>karma-bom</artifactId>
                <version>${karma.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.renpho.karma</groupId>
                <artifactId>karma-xfile</artifactId>
                <version>${karma.version}</version>
            </dependency>
            <!-- ==================================== -->
            <!-- ERP-公共业务中心（Public Business Center） -->
            <!-- ==================================== -->
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>erp-openfeign-sdk</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>erp-operator-log</artifactId>
                <version>1.1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>erp-security</artifactId>
                <version>${revision}</version>
            </dependency>
            <!-- ==================================== -->
            <!-- 模块依赖 -->
            <!-- ==================================== -->
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>ftm-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>ftm-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>ftm-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>ftm-application</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>ftm-adapter</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- smart-doc插件 -->
            <plugin>
                <groupId>com.ly.smart-doc</groupId>
                <artifactId>smart-doc-maven-plugin</artifactId>
                <version>3.0.2</version>
                <configuration>
                    <!--指定生成文档的使用的配置文件-->
                    <configFile>${basedir}/smart-doc.json</configFile>
                    <!--指定项目名称-->
                    <projectName>FTM项目</projectName>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>io.spring.javaformat</groupId>
                <artifactId>spring-javaformat-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <configuration>
                    <enableRulesSummary>false</enableRulesSummary>
                    <configLocation>${basedir}/checkstyle.xml</configLocation>
                </configuration>
            </plugin>
            <!--            <plugin>-->
            <!--                <artifactId>maven-deploy-plugin</artifactId>-->
            <!--                <configuration>-->
            <!--                    <skip>false</skip>-->
            <!--                </configuration>-->
            <!--            </plugin>-->
        </plugins>
    </build>
    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <url>http://renpho.master.com:10081/repository/maven-releases/</url>
        </repository>

        <snapshotRepository>
            <id>maven-snapshots</id>
            <url>http://renpho.master.com:10081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
