<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renpho.erp.smc.infrastructure.persistence.mapper.UserLoginLogMapper">

    <sql id="Base_Column_List"> id, user_id, username, login_ip, login_time, login_type, login_addr,
        brower_info, sysos_info, login_status, remark, status, create_by, create_time, update_by,
        update_time </sql>
    <select id="selectListByIds"
        resultType="com.renpho.erp.smc.infrastructure.persistence.po.UserLoginLogPO"> 
            SELECT
                ull.id,
                ull.user_id AS userId,
                ull.login_ip,
                ull.login_time,
                ull.login_addr,
                ull.browser_info,
                ull.sysos_info,
                ull.login_status,
                ull.create_by,
                ull.create_time,
                ull.update_by,
                ull.update_time,
                ull.login_info,
                IFNULL(
                    JSON_UNQUOTE(
                        JSON_EXTRACT(
                            ull.browser_info,
                        CONCAT( '$.system' ))),
                    JSON_UNQUOTE(
                        JSON_EXTRACT(
                            ull.browser_info,
                        CONCAT( '$.System' )))) AS browserSystem,
                JSON_UNQUOTE(
                    JSON_EXTRACT(
                        ull.browser_info,
                    CONCAT( '$.Fingerprint' ))) AS browserFingerprint,
                JSON_UNQUOTE(
                    JSON_EXTRACT(
                        ull.sysos_info,
                    CONCAT( '$.System' ))) AS operatingSystem,
                JSON_UNQUOTE(
                    JSON_EXTRACT(
                        ull.sysos_info,
                    CONCAT( '$.Resolution' ))) AS screenResolution,
                su.NAME AS employeeName,
                su.CODE AS employeeID 
            FROM
                oum_user_login_log ull
                LEFT JOIN oum_user_info su ON ull.user_id = su.id  
         <where>
            <if test="userIds != null and userIds.size() > 0"> ull.user_id in <foreach
                    collection="userIds" item="sysId" open="(" separator="," close=")"> #{sysId} </foreach>
            </if>
        <if
                test="loginStatus != null"> and ull.login_status = #{loginStatus} </if>
        <if
                test="loginStartTime!=null and loginStartTime!=''"> and ull.login_time &gt;=
        CONCAT(#{loginStartTime},' 00:00:00') </if>
        <if
                test="loginEndTime!=null and loginEndTime!=''"> and ull.login_time &lt;=
        concat(#{loginEndTime},' 23:59:59') </if>
        </where> order by ull.login_time desc limit
        #{pageNum},#{pageSize} </select>
    <select id="selectCountByIds" resultType="java.lang.Long"> select count(ull.id) from
        oum_user_login_log ull join oum_user_info su on ull.user_id = su.id <where>
            <if test="userIds != null and userIds.size() > 0"> and ull.user_id in <foreach
                    collection="userIds" item="sysId" open="(" separator="," close=")"> #{sysId} </foreach>
            </if>
            <if
                test="loginStatus != null"> and ull.login_status = #{loginStatus} </if>
            <if
                test="loginStartTime!=null and loginStartTime!=''"> and ull.login_time &gt;=
        #{loginStartTime} </if>
            <if test="loginEndTime!=null and loginEndTime!=''"> and
        ull.login_time &lt;= #{loginEndTime} </if>
        </where>
    </select>
</mapper>