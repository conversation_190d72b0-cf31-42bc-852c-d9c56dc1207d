<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renpho.erp.smc.infrastructure.persistence.mapper.OumOfficeLocationAssignMapper">
    <resultMap id="BaseResultMap" type="com.renpho.erp.smc.infrastructure.persistence.po.OumOfficeLocationAssignPO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="office_location_id" jdbcType="INTEGER" property="officeLocationId"/>
        <result column="effective_date" jdbcType="TIMESTAMP" property="effectiveDate"/>
        <result column="create_by" jdbcType="INTEGER" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="INTEGER" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>

    </resultMap>

    <sql id="Base_Column_List">
        id
        , user_id, office_location_id, effective_date, create_by, create_time, update_by, update_time,is_deleted
    </sql>

    <select id="pageOfficeLocationAssign"
            resultType="com.renpho.erp.smc.infrastructure.persistence.po.OumOfficeLocationAssignPO">
        select
        ola.id as id,
        ola.effective_date as effectiveDate,
        oll.office_location_id as officeLocationId,
        oll.name as officeLocationName,
        oui.id AS userId,
        oui.code as employeeId,
        oui.name as userName,
        oui.status as userStatus,
        oui.department_id as departmentId,
        odl.name as departmentName,
        oui.position_id as positionId,
        opl.name as positionName,
        ola.update_time as updateTime
        from oum_office_location_assign ola
        left join oum_office_location_language oll on ola.office_location_id = oll.office_location_id and oll.language =
        #{language} and oll.is_deleted = 0
        left join oum_user_info oui on ola.user_id = oui.id and oui.is_deleted = 0
        left join oum_department_user odu on odu.user_id = oui.id and odu.is_deleted = 0 and odu.is_primary = 1
        left join oum_department_language odl on odu.department_id = odl.department_id and odl.language = #{language}
        and odu.is_deleted = 0
        left join oum_position_language opl on opl.position_id = oui.position_id AND opl.language = #{language} and
        opl.is_deleted = 0
        <where>
            ola.is_deleted = 0
            <if test="param.startEffectiveDate != null and param.startEffectiveDate !=''">
                and ola.`effective_date` &gt;= concat( #{param.startEffectiveDate},' 00:00:00')
            </if>
            <if test="param.endEffectiveDate != null and param.endEffectiveDate !=''">
                and ola.`effective_date` &lt;= concat( #{param.endEffectiveDate},' 23:59:59')
            </if>
            <if test="param.employeeId != null and param.employeeId != ''">
                and oui.`code` = #{param.employeeId}
            </if>
            <if test="param.userStatus != null ">
                and oui.`status` = #{param.userStatus}
            </if>
            <if test="param.userIds != null and param.userIds.size()!=0 ">
                and ola.`user_id` in
                <foreach collection="param.userIds" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="param.officeLocationIds != null and param.officeLocationIds.size()!=0 ">
                and ola.`office_location_id` in
                <foreach collection="param.officeLocationIds" item="item" index="index" open="(" close=")"
                         separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="param.departmentIds != null and param.departmentIds.size()!=0 ">
                and oui.`department_id` in
                <foreach collection="param.departmentIds" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        order by ola.update_time desc
    </select>
    <select id="selectExistUser"
            resultType="com.renpho.erp.smc.infrastructure.persistence.po.OumOfficeLocationAssignPO">
        select oui.name as userName
        from oum_office_location_assign ola
        left join oum_user_info oui on ola.user_id = oui.id and oui.is_deleted = 0
        <where>ola.is_deleted = 0
            <if test="userIds != null and userIds.size()>0">
                and ola.user_id in
                <foreach collection="userIds" item="currentId" open="(" separator="," close=")">
                    #{currentId}
                </foreach>
            </if>
            <if test="id!=null and id!='' ">
                and ola.id != #{id} and ola.user_id = #{userId}
            </if>
        </where>
    </select>


</mapper>