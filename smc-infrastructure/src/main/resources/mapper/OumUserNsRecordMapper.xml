<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renpho.erp.smc.infrastructure.persistence.mapper.OumUserNsRecordMapper">
    <resultMap id="selectOumUserNsRecordInfoResultMap"
               type="com.renpho.erp.smc.infrastructure.persistence.dto.SelectOumUserNsRecordInfoDto">
    </resultMap>
    <select id="selectOumUserNsRecordInfo" resultMap="selectOumUserNsRecordInfoResultMap">
        SELECT DISTINCT
        oui.corporation_id,
        ounr.id,
        ounr.`user_id`,
        ounr.`ns_id`,
        oui.`code`,
        oui.phone_no,
        oui.email,
        ounr.`ns_user_name`,
        ounr.`ns_corporation_name`,
        ounr.`is_defaulted`,
        ounr.`status`,
        ounr.`reason`,
        odu.department_id,
        ounr.update_by,
        (
        SELECT
        opl.`name`
        FROM
        oum_position_language opl
        WHERE
        opl.position_id = oui.position_id
        AND opl.is_deleted = 0
        AND opl.`language` = 'en-US'
        LIMIT 1
        ) AS positionName,
        ( SELECT odu.userid FROM oum_dingtalk_user odu WHERE odu.sys_user_id = ounr.update_by LIMIT 1 ) ding_ding_user_id
        FROM
        oum_user_ns_record ounr
        LEFT JOIN oum_department_user odu ON ounr.user_id = odu.user_id    AND ounr.is_deleted = 0
        AND odu.is_primary = 1
        AND odu.is_deleted = 0
        LEFT JOIN oum_user_info oui ON oui.id = odu.user_id
        AND oui.is_deleted = 0
        LEFT JOIN oum_account oa ON oa.user_id = oui.id
        AND oa.is_deleted = 0
        WHERE
        ounr.id = #{recordId}
    </select>
</mapper>