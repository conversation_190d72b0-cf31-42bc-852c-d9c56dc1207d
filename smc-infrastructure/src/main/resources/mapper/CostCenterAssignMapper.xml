<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renpho.erp.smc.infrastructure.persistence.mapper.CostCenterAssignMapper">
    <resultMap id="BaseResultMap" type="com.renpho.erp.smc.infrastructure.persistence.po.CostCenterAssignPO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="cost_center_id" jdbcType="INTEGER" property="costCenterId"/>
        <result column="efficetive_date" jdbcType="TIMESTAMP" property="efficetiveDate"/>
        <result column="create_by" jdbcType="INTEGER" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="INTEGER" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , user_id, office_location_id, efficetive_date, create_by, create_time, update_by,
    update_time, is_deleted
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from oum_cost_center_assign
        where id = #{id,jdbcType=INTEGER}
    </select>
    <select id="pageCostCenterAssign"
            resultType="com.renpho.erp.smc.infrastructure.persistence.po.CostCenterAssignPO">
        select distinct occa.id,
        occa.user_id as userId,
        occa.cost_center_id as costCenterId,
        oui.status as staffStatus,
        oui.name as userName,
        oui.code as userCode,
        odl.name as mainDepartmentName,
        opl.name as positionName,
        occa.efficetive_date,
        occa.update_time
        from oum_cost_center_assign occa
        left join oum_user_info oui on occa.user_id = oui.id and oui.is_deleted = 0
        left join oum_department_user oduu on oduu.id = oduu.user_id and oduu.is_deleted = 0
        left join oum_department_user odu on oui.id = odu.user_id and odu.is_deleted = 0 and odu.is_primary=1
        left join oum_department_language odl on odl.department_id = odu.department_id and odl.is_deleted = 0 and
        odl.language = #{language}
        left join oum_position_language opl on opl.position_id = oui.position_id and opl.is_deleted = 0 and opl.language
        = #{language}
        <where>
            occa.is_deleted = 0
            <if test="param.userIds != null and param.userIds.size()>0">
                and occa.user_id in
                <foreach collection="param.userIds" item="userId" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
            <if test="param.costCenterIds != null and param.costCenterIds.size()>0">
                and occa.cost_center_id in
                <foreach collection="param.costCenterIds" item="costCenterId" open="(" separator="," close=")">
                    #{costCenterId}
                </foreach>
            </if>
            <if test="param.departmentIds != null and param.departmentIds.size()>0">
                and oduu.department_id in
                <foreach collection="param.departmentIds" item="departmentId" open="(" separator="," close=")">
                    #{departmentId}
                </foreach>
            </if>
            <if test="param.staffStatus!=null">
                and oui.status=#{param.staffStatus}
            </if>
            <if test="param.userCode!=null and param.userCode!='' ">
                and oui.code =#{param.userCode}
            </if>
            <if test="param.startEfficetiveDate!=null and param.startEfficetiveDate !=''">
                and occa.efficetive_date >= concat(#{param.startEfficetiveDate},' 00:00:00')
            </if>
            <if test="param.endEfficetiveDate!=null and param.endEfficetiveDate !=''">
                and occa.efficetive_date &lt;= concat(#{param.endEfficetiveDate},' 23:59:59')
            </if>
        </where>
        order by occa.update_time desc
    </select>
    <select id="selectExistUser"
            resultType="com.renpho.erp.smc.infrastructure.persistence.po.CostCenterAssignPO">
        select oui.name as userName
        from oum_cost_center_assign occa
        left join oum_user_info oui on occa.user_id = oui.id and oui.is_deleted = 0
        <where>occa.is_deleted = 0
            <if test="userIds != null and userIds.size()>0">
                and occa.user_id in
                <foreach collection="userIds" item="currentId" open="(" separator="," close=")">
                    #{currentId}
                </foreach>
            </if>
            <if test="id!=null and id!=''">
                and occa.id != #{id} and occa.user_id = #{userId}
            </if>
        </where>
    </select>
    <select id="getCostCenterAssignDetail"
            resultType="com.renpho.erp.smc.infrastructure.persistence.po.CostCenterAssignPO">
        select distinct occa.id,
                        occa.cost_center_id as costCenterId,
                        oui.status          as staffStatus,
                        oui.name            as userName,
                        oui.code            as userCode,
                        odl.name            as mainDepartmentName,
                        opl.name            as positionName,
                        occa.efficetive_date,
                        occa.update_time
        from oum_cost_center_assign occa
                 left join oum_user_info oui on occa.user_id = oui.id and oui.is_deleted = 0
                 left join oum_department_user oduu on oduu.id = oduu.user_id and oduu.is_deleted = 0
                 left join oum_department_user odu on oui.id = odu.user_id and odu.is_deleted = 0 and odu.is_primary = 1
                 left join oum_department_language odl
                           on odl.department_id = odu.department_id and odl.is_deleted = 0 and
                              odl.language = #{language}
                 left join oum_position_language opl
                           on opl.position_id = oui.position_id and opl.is_deleted = 0 and opl.language = #{language}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from oum_cost_center_assign
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.renpho.erp.smc.infrastructure.persistence.po.CostCenterAssignPO" useGeneratedKeys="true">
        insert into oum_cost_center_assign
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                user_id,
            </if>
            <if test="officeLocationId != null">
                office_location_id,
            </if>
            <if test="efficetiveDate != null">
                efficetive_date,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId,jdbcType=INTEGER},
            </if>
            <if test="officeLocationId != null">
                #{officeLocationId,jdbcType=INTEGER},
            </if>
            <if test="efficetiveDate != null">
                #{efficetiveDate,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.renpho.erp.smc.infrastructure.persistence.po.CostCenterAssignPO">
        update oum_cost_center_assign
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=INTEGER},
            </if>
            <if test="officeLocationId != null">
                office_location_id = #{officeLocationId,jdbcType=INTEGER},
            </if>
            <if test="efficetiveDate != null">
                efficetive_date = #{efficetiveDate,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.renpho.erp.smc.infrastructure.persistence.po.CostCenterAssignPO">
        update oum_cost_center_assign
        set user_id            = #{userId,jdbcType=INTEGER},
            office_location_id = #{officeLocationId,jdbcType=INTEGER},
            efficetive_date    = #{efficetiveDate,jdbcType=TIMESTAMP},
            create_by          = #{createBy,jdbcType=INTEGER},
            create_time        = #{createTime,jdbcType=TIMESTAMP},
            update_by          = #{updateBy,jdbcType=INTEGER},
            update_time        = #{updateTime,jdbcType=TIMESTAMP},
            is_deleted         = #{isDeleted,jdbcType=INTEGER}
        where id = #{id,jdbcType=INTEGER}
    </update>
</mapper>