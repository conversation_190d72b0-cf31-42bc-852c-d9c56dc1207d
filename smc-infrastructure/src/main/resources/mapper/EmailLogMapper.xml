<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renpho.erp.smc.infrastructure.persistence.mapper.EmailLogMapper">
  <resultMap id="BaseResultMap" type="com.renpho.erp.smc.infrastructure.persistence.po.EmailLog">
    <!--@mbg.generated-->
    <!--@Table sys_email_log-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="from_email" jdbcType="VARCHAR" property="fromEmail" />
    <result column="from_nick_name" jdbcType="VARCHAR" property="fromNickName" />
    <result column="to_email" jdbcType="VARCHAR" property="toEmail" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_by" jdbcType="INTEGER" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="err_msg" jdbcType="VARCHAR" property="errMsg" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, from_email, from_nick_name, to_email, title, content, `type`, `status`, create_by, 
    create_time, err_msg
  </sql>
</mapper>