<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renpho.erp.smc.infrastructure.persistence.mapper.DictTypeLanguageMapper">


    <select id="selectListByTypeId"
            resultType="com.renpho.erp.smc.infrastructure.persistence.po.DictTypeLanguagePO">
        select id from sys_dict_type_language sdtl where sdtl.dict_type_id = #{id} and sdtl.language = #{language}
    </select>
</mapper>
