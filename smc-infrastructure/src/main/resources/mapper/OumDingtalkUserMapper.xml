<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.renpho.erp.smc.infrastructure.persistence.mapper.OumDingTalkUserMapper">

    <select id="selectNotUserId" resultType="com.renpho.erp.smc.infrastructure.persistence.po.OumDingTalkUser">
        select unionid , userid, `name`, sys_user_id as sysUserId,`status` from oum_dingtalk_user
        <where>
            <if test="thirdUserId != null">
                userid = #{thirdUserId}
            </if>
        </where>

    </select>
</mapper>