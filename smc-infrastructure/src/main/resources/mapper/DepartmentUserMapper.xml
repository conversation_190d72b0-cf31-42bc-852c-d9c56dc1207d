<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renpho.erp.smc.infrastructure.persistence.mapper.DepartmentUserMapper">
    <resultMap id="BaseResultMap" type="com.renpho.erp.smc.infrastructure.persistence.po.DepartmentUserPO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="department_id" jdbcType="INTEGER" property="departmentId"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="is_primary" jdbcType="BIT" property="isPrimary"/>
        <result column="create_by" jdbcType="INTEGER" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="INTEGER" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="INTEGER" property="deleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , department_id, user_id, is_primary, create_by, create_time, update_by, update_time,
    is_deleted
    </sql>
    <!--    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
            select
            <include refid="Base_Column_List"/>
            from oum_department_user
            where id = #{id,jdbcType=INTEGER}
        </select>-->
    <select id="findUserCountByDepartmentId" resultType="java.lang.Integer">
        select count(1)
        from oum_user_info oui left join oum_department_user odu on odu.user_id = oui.id and oui.is_deleted = 0
        <if test="userStatus != null and userStatus !='' ">
            and oui.status = #{userStatus}
        </if>
        where odu.is_deleted = 0 and odu.department_id in
        <foreach collection="departmentIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>
    <select id="pageDepartmentUser"
            resultType="com.renpho.erp.smc.infrastructure.persistence.po.DepartmentUserPO">
        SELECT
        odu.id as id,
        odu.user_id AS userId,
        oui.code AS userCode,
        oui.email AS email,
        oui.status AS status,
        oui.name AS userName,
        oa.status AS accountStatus,
        opl.name AS positionName

        FROM oum_department_user odu
        LEFT JOIN oum_user_info oui ON oui.id = odu.user_id AND oui.is_deleted = 0
        LEFT JOIN oum_position_language opl ON opl.position_id = oui.position_id AND opl.language = #{language}
        LEFT JOIN oum_position op ON op.id = oui.position_id AND op.is_deleted = 0
        LEFT JOIN oum_account oa ON oa.user_id = oui.id AND oa.is_deleted = 0
        <where>odu.is_deleted = 0
            <if test="param.userCode != null">
                AND oui.code LIKE CONCAT('%', #{param.userCode}, '%')
            </if>
            <if test="param.userIds != null and param.userIds.size() > 0">
                AND odu.user_id IN
                <foreach collection="param.userIds" item="userId" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
            <if test="param.status != null ">
                AND oui.status = #{param.status}
            </if>
            <if test="param.departmentId != null and param.departmentId != ''">
                AND odu.department_id = #{param.departmentId}
            </if>

            <if test="param.positionIds != null and param.positionIds.size() > 0">
                AND op.id IN
                <foreach collection="param.positionIds" item="positionId" open="(" separator="," close=")">
                    #{positionId}
                </foreach>
            </if>

            <if test="param.accountStatus != null ">
                AND oa.status = #{param.accountStatus}
            </if>
        </where>
        order by oui.position_added_time desc
    </select>
    <select id="listDepartmentUser"
            resultType="com.renpho.erp.smc.infrastructure.persistence.po.DepartmentUserPO">
        select ou.user_id       as userId,
               ou.department_id as departmentId,
               oui.name         as userName
        from oum_department_user ou
                 left join oum_user_info oui on oui.id = ou.user_id and oui.is_deleted = 0
        where ou.is_deleted = 0
    </select>

    <select id="exportDepartmentUserList"
            resultType="com.renpho.erp.smc.infrastructure.persistence.po.DepartmentUserPO">
        select od.code        as departmentCode,
               odl.name       as departmentName,
               oui.name       as managerName,
               od.status      as departmentStatus,
               od.level_label as levelLable,
               odll.name      as parentName,
               odd.code       as parentCode,
               opl.name       as positionName,
               ouii.name      as userName,
               ouii.code      as userCode,
               ouii.email     as email,
               oa.status      as accountStatus,
               ouii.status    as status

        from oum_department_user odu
                 left join oum_department od on od.id = odu.department_id and od.is_deleted = 0
                 left join oum_department_language odl
                           on od.id = odl.department_id and odl.language = #{language} and odl.is_deleted = 0
                 left join oum_department_user oduu on od.manager_id = oduu.id and oduu.is_deleted = 0
                 left join oum_department_language odll
                           on od.parent_id = odll.department_id and odll.language = #{language} and odll.is_deleted = 0
                 left join oum_department odd on od.parent_id = odd.id and odd.is_deleted = 0
                 left join oum_user_info oui on oui.id = od.manager_id and oui.is_deleted = 0
                 left join oum_user_info ouii on ouii.id = odu.user_id and ouii.is_deleted = 0
                 left join oum_account oa on oa.user_id = ouii.id and oa.is_deleted = 0
                 left join oum_position_language opl
                           on opl.position_id = ouii.position_id and opl.language = #{language} and opl.is_deleted = 0
        where odu.is_deleted = 0

    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from oum_department_user
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <!--<insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.renpho.erp.smc.infrastructure.persistence.po.DepartmentUserPO" useGeneratedKeys="true">
        insert into oum_department_user (department_id, user_id, is_primary)
        values (#{departmentId,jdbcType=INTEGER}, #{userId,jdbcType=INTEGER}, #{isPrimary,jdbcType=BIT})
    </insert>-->
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.renpho.erp.smc.infrastructure.persistence.po.DepartmentUserPO" useGeneratedKeys="true">
        insert into oum_department_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="departmentId != null">
                department_id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="isPrimary != null">
                is_primary,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="departmentId != null">
                #{departmentId,jdbcType=INTEGER},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=INTEGER},
            </if>
            <if test="isPrimary != null">
                #{isPrimary,jdbcType=BIT},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <insert id="insertDepartmentUser" parameterType="com.renpho.erp.smc.infrastructure.persistence.po.DepartmentUserPO">
        insert into oum_department_user (department_id, user_id, is_primary, create_by, update_by, create_time,
                                         update_time)
        values (#{param.departmentId,jdbcType=INTEGER}, #{param.userId,jdbcType=INTEGER},
                #{param.isPrimary,jdbcType=BIT},
                #{param.createBy,jdbcType=INTEGER}, #{param.updateBy,jdbcType=INTEGER}, now(), now())


    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.renpho.erp.smc.infrastructure.persistence.po.DepartmentUserPO">
        update oum_department_user
        <set>
            <if test="departmentId != null">
                department_id = #{departmentId,jdbcType=INTEGER},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=INTEGER},
            </if>
            <if test="isPrimary != null">
                is_primary = #{isPrimary,jdbcType=BIT},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.renpho.erp.smc.infrastructure.persistence.po.DepartmentUserPO">
        update oum_department_user
        set department_id = #{departmentId,jdbcType=INTEGER},
            user_id       = #{userId,jdbcType=INTEGER},
            is_primary    = #{isPrimary,jdbcType=BIT},
            create_by     = #{createBy,jdbcType=INTEGER},
            create_time   = #{createTime,jdbcType=TIMESTAMP},
            update_by     = #{updateBy,jdbcType=INTEGER},
            update_time   = #{updateTime,jdbcType=TIMESTAMP},
            is_deleted    = #{isDeleted,jdbcType=INTEGER}
        where id = #{id,jdbcType=INTEGER}
    </update>
</mapper>