<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renpho.erp.smc.infrastructure.persistence.mapper.RoleUserMapper">

    <select id="page" resultType="com.renpho.erp.smc.infrastructure.persistence.po.RoleUserPO">
        SELECT
        ru.id,ru.role_id,ru.user_id,
        ui.code as user_code,ui.name as user_name,ui.email,ui.status as user_status,
        ac.status as account_status,
        dl.name as department_name,
        pl.name as position_name
        FROM oum_role_user ru
        LEFT JOIN oum_user_info ui ON ru.user_id = ui.id AND ui.is_deleted = 0
        LEFT JOIN oum_account ac ON ru.user_id = ac.user_id AND ac.is_deleted = 0
        LEFT JOIN oum_department_user du ON du.user_id = ru.user_id AND du.is_primary = 1 AND du.is_deleted = 0
        LEFT JOIN oum_department_language dl ON du.department_id = dl.department_id AND dl.is_deleted = 0
        <if test="param.language != null and param.language != ''">
            AND dl.language = #{param.language}
        </if>
        LEFT JOIN oum_position_language pl ON ui.position_id = pl.position_id
        <if test="param.language != null and param.language != ''">
            AND pl.language = #{param.language}
        </if>
        WHERE ru.is_deleted = 0
        <if test="param.roleId != null">
            AND ru.role_id = #{param.roleId}
        </if>
        <if test="param.userCode != null and param.userCode != ''">
            AND ui.code LIKE concat('%', #{param.userCode}, '%')
        </if>
        <if test="param.userName != null and param.userName != ''">
            AND ui.name LIKE concat('%', #{param.userName}, '%')
        </if>
        <if test="param.userStatus != null">
            AND ui.status = #{param.userStatus}
        </if>
        <if test="param.accountStatus != null">
            AND ac.status = #{param.accountStatus}
        </if>
    </select>

</mapper>