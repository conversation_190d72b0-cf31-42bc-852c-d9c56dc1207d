<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renpho.erp.smc.infrastructure.persistence.mapper.ParamConfigMapper">

    <sql id="Base_Column_List">
        id,
        param_name as paramName,
        param_key,
        param_value,
        remark,
        status,
        is_deleted as deleted,
        create_by,
        create_time,
        update_by,
        update_time
    </sql>

    <select id="selectParamConfigList"
            resultType="com.renpho.erp.smc.infrastructure.persistence.po.ParamConfigPO">
        select
        <include refid="Base_Column_List"/>
        from sys_param_config
        <where>
            <if test="name != null and name != ''">
                and param_name like concat('%', #{name}, '%')
            </if>
            <if test="key != null and key != ''">
                and param_key like concat('%', #{key}, '%')
            </if>
            <if test="status != null ">
                and status = #{status}
            </if>
        </where>
        order by update_time desc
    </select>
    <select id="getDetailById" resultType="com.renpho.erp.smc.infrastructure.persistence.po.ParamConfigPO">
        select id,param_name ,param_key ,param_value  ,status,remark  FROM sys_param_config where
            id=#{id}
    </select>

    <select id="getDetailByKey" resultType="com.renpho.erp.smc.infrastructure.persistence.po.ParamConfigPO">
        select id,param_name ,param_key ,param_value  ,status,remark  FROM sys_param_config where
        param_key=#{key}
    </select>
</mapper>
