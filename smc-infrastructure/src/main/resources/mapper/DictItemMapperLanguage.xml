<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renpho.erp.smc.infrastructure.persistence.mapper.DictItemMultilanguageMapper">
    <update id="updateLanguage">
        update sys_dict_item_language
        set `name` = #{name}, update_time = now(), update_by = #{updateBy}
        where dict_item_id = #{dictItemId} and `language` = #{language}
    </update>


    <delete id="deleteByDictItemId">
        update sys_dict_item_language set is_deleted = 1 where dict_item_id = #{dictItemId}
    </delete>
</mapper>
