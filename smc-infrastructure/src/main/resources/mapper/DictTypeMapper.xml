<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renpho.erp.smc.infrastructure.persistence.mapper.DictTypeMapper">



    <select id="selectAllList" resultType="com.renpho.erp.smc.infrastructure.persistence.po.DictTypePO">
        SELECT
            id,
            status,
            COALESCE(
                    (SELECT sdtl.name FROM sys_dict_type_language sdtl WHERE sdtl.dict_type_id = sdt.id AND sdtl.language = #{language}),
                    (SELECT sdtl.name FROM sys_dict_type_language sdtl WHERE sdtl.dict_type_id = sdt.id AND sdtl.language = 'en-US')
                ) AS dict_name,
            dict_type
        FROM sys_dict_type sdt  where sdt.is_deleted=0
        ORDER BY status DESC, id ASC
    </select>

    <select id="selectListByName" resultType="com.renpho.erp.smc.infrastructure.persistence.po.DictTypePO">
        Select * from
        (SELECT
            id,
            status,
            COALESCE(
                    (SELECT sdtl.name FROM sys_dict_type_language sdtl WHERE sdtl.dict_type_id = sdt.id AND sdtl.language = #{language}),
                    (SELECT sdtl.name FROM sys_dict_type_language sdtl WHERE sdtl.dict_type_id = sdt.id AND sdtl.language = 'en-US')
                ) AS dict_name,
            dict_type
        FROM sys_dict_type sdt  where sdt.is_deleted=0
        ORDER BY status DESC, id ASC) de where de.dict_name LIKE CONCAT('%', #{name}, '%') or de.dict_type LIKE CONCAT('%', #{name}, '%')
    </select>

    <select id="selectListByType" resultType="com.renpho.erp.smc.infrastructure.persistence.po.DictTypePO">
        SELECT id,
             (SELECT sdtl.name FROM sys_dict_type_language sdtl WHERE sdtl.dict_type_id = sdt.id
            ) AS dict_name,
            status,dict_type FROM sys_dict_type WHERE dict_type LIKE CONCAT('%',#{type}, '%') and is_deleted=0 order by status desc,id asc
    </select>

    <select id="selectAllNameList" resultType="com.renpho.erp.smc.infrastructure.persistence.po.DictTypePO">
        SELECT id, dict_name,status,dict_type FROM sys_dict_type  where is_deleted=0 order by status desc,id asc

    </select>
    <select id="getDetailById" resultType="com.renpho.erp.smc.infrastructure.persistence.po.DictTypePO">
        SELECT sdt.id,
               CONCAT('[', GROUP_CONCAT('{"name":"',sdtl.name,'","language":"' ,sdtl.language,'"}'),']') as dictName,sdt.remark,
            status,dict_type FROM sys_dict_type sdt
            LEFT JOIN sys_dict_type_language sdtl
        on sdtl.dict_type_id = sdt.id
        WHERE sdt.id = #{id} and sdt.is_deleted=0
    </select>
    <select id="selectDictByName" resultType="com.renpho.erp.smc.infrastructure.persistence.po.DictTypePO">
        SELECT sdt.id
        FROM sys_dict_type sdt left join sys_dict_type_language sdtl on sdt.id = sdtl.dict_type_id
        where sdtl.language=#{language} and sdtl.name=#{name} and sdt.is_deleted=0
    </select>
    <select id="selectDictByType" resultType="com.renpho.erp.smc.infrastructure.persistence.po.DictTypePO">
        SELECT id FROM sys_dict_type WHERE dict_type=#{type}
    </select>
    <select id="selectDictByTypeExcludeId"
            resultType="com.renpho.erp.smc.infrastructure.persistence.po.DictTypePO">
        SELECT id FROM sys_dict_type WHERE dict_type=#{type} and id!=#{id} and is_deleted=0
    </select>
    <select id="selectDictByNameExcludeId"
            resultType="com.renpho.erp.smc.infrastructure.persistence.po.DictTypePO">
        SELECT sdt.id
        FROM sys_dict_type sdt left join sys_dict_type_language sdtl on sdt.id = sdtl.dict_type_id
        where sdtl.language=#{language} and sdtl.name=#{name} and sdt.is_deleted=0 and sdt.id!=#{id}
    </select>

</mapper>
