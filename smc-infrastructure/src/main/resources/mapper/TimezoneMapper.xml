<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renpho.erp.smc.infrastructure.persistence.mapper.TimezoneMapper">
    <resultMap id="BaseResultMap" type="com.renpho.erp.smc.infrastructure.persistence.po.TimezonePO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="continents_en" jdbcType="VARCHAR" property="continentsEn"/>
        <result column="continents_cn" jdbcType="VARCHAR" property="continentsCn"/>
        <result column="contry_en" jdbcType="VARCHAR" property="contryEn"/>
        <result column="contry_cn" jdbcType="VARCHAR" property="contryCn"/>
        <result column="city_en" jdbcType="VARCHAR" property="cityEn"/>
        <result column="city_cn" jdbcType="VARCHAR" property="cityCn"/>
        <result column="time_zone_no" jdbcType="VARCHAR" property="timeZoneNo"/>
        <result column="time_zone_nm" jdbcType="VARCHAR" property="timeZoneNm"/>
        <result column="smr_mk" jdbcType="VARCHAR" property="smrMk"/>
        <result column="smr_str_datetime" jdbcType="TIMESTAMP" property="smrStrDatetime"/>
        <result column="smr_end_datetime" jdbcType="TIMESTAMP" property="smrEndDatetime"/>
        <result column="create_by" jdbcType="INTEGER" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="INTEGER" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , continents_en, continents_cn, contry_en, contry_cn, city_en, city_cn, time_zone_no,
    time_zone_nm, smr_mk, smr_str_datetime, smr_end_datetime, create_by, create_time,
    update_by, update_time, `status`
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_timezone
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from sys_timezone
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.renpho.erp.smc.infrastructure.persistence.po.TimezonePO" useGeneratedKeys="true">
        insert into sys_timezone
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="continentsEn != null">
                continents_en,
            </if>
            <if test="continentsCn != null">
                continents_cn,
            </if>
            <if test="contryEn != null">
                contry_en,
            </if>
            <if test="contryCn != null">
                contry_cn,
            </if>
            <if test="cityEn != null">
                city_en,
            </if>
            <if test="cityCn != null">
                city_cn,
            </if>
            <if test="timeZoneNo != null">
                time_zone_no,
            </if>
            <if test="timeZoneNm != null">
                time_zone_nm,
            </if>
            <if test="smrMk != null">
                smr_mk,
            </if>
            <if test="smrStrDatetime != null">
                smr_str_datetime,
            </if>
            <if test="smrEndDatetime != null">
                smr_end_datetime,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="continentsEn != null">
                #{continentsEn,jdbcType=VARCHAR},
            </if>
            <if test="continentsCn != null">
                #{continentsCn,jdbcType=VARCHAR},
            </if>
            <if test="contryEn != null">
                #{contryEn,jdbcType=VARCHAR},
            </if>
            <if test="contryCn != null">
                #{contryCn,jdbcType=VARCHAR},
            </if>
            <if test="cityEn != null">
                #{cityEn,jdbcType=VARCHAR},
            </if>
            <if test="cityCn != null">
                #{cityCn,jdbcType=VARCHAR},
            </if>
            <if test="timeZoneNo != null">
                #{timeZoneNo,jdbcType=VARCHAR},
            </if>
            <if test="timeZoneNm != null">
                #{timeZoneNm,jdbcType=VARCHAR},
            </if>
            <if test="smrMk != null">
                #{smrMk,jdbcType=VARCHAR},
            </if>
            <if test="smrStrDatetime != null">
                #{smrStrDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="smrEndDatetime != null">
                #{smrEndDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.renpho.erp.smc.infrastructure.persistence.po.TimezonePO">
        update sys_timezone
        <set>
            <if test="continentsEn != null">
                continents_en = #{continentsEn,jdbcType=VARCHAR},
            </if>
            <if test="continentsCn != null">
                continents_cn = #{continentsCn,jdbcType=VARCHAR},
            </if>
            <if test="contryEn != null">
                contry_en = #{contryEn,jdbcType=VARCHAR},
            </if>
            <if test="contryCn != null">
                contry_cn = #{contryCn,jdbcType=VARCHAR},
            </if>
            <if test="cityEn != null">
                city_en = #{cityEn,jdbcType=VARCHAR},
            </if>
            <if test="cityCn != null">
                city_cn = #{cityCn,jdbcType=VARCHAR},
            </if>
            <if test="timeZoneNo != null">
                time_zone_no = #{timeZoneNo,jdbcType=VARCHAR},
            </if>
            <if test="timeZoneNm != null">
                time_zone_nm = #{timeZoneNm,jdbcType=VARCHAR},
            </if>
            <if test="smrMk != null">
                smr_mk = #{smrMk,jdbcType=VARCHAR},
            </if>
            <if test="smrStrDatetime != null">
                smr_str_datetime = #{smrStrDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="smrEndDatetime != null">
                smr_end_datetime = #{smrEndDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=INTEGER},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.renpho.erp.smc.infrastructure.persistence.po.TimezonePO">
        update sys_timezone
        set continents_en    = #{continentsEn,jdbcType=VARCHAR},
            continents_cn    = #{continentsCn,jdbcType=VARCHAR},
            contry_en        = #{contryEn,jdbcType=VARCHAR},
            contry_cn        = #{contryCn,jdbcType=VARCHAR},
            city_en          = #{cityEn,jdbcType=VARCHAR},
            city_cn          = #{cityCn,jdbcType=VARCHAR},
            time_zone_no     = #{timeZoneNo,jdbcType=VARCHAR},
            time_zone_nm     = #{timeZoneNm,jdbcType=VARCHAR},
            smr_mk           = #{smrMk,jdbcType=VARCHAR},
            smr_str_datetime = #{smrStrDatetime,jdbcType=TIMESTAMP},
            smr_end_datetime = #{smrEndDatetime,jdbcType=TIMESTAMP},
            create_by        = #{createBy,jdbcType=INTEGER},
            create_time      = #{createTime,jdbcType=TIMESTAMP},
            update_by        = #{updateBy,jdbcType=INTEGER},
            update_time      = #{updateTime,jdbcType=TIMESTAMP},
            `status`         = #{status,jdbcType=INTEGER},
            is_deleted       = #{isDeleted,jdbcType=INTEGER}
        where id = #{id,jdbcType=INTEGER}
    </update>
</mapper>