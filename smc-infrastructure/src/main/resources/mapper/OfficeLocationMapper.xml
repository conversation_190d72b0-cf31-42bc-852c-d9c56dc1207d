<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renpho.erp.smc.infrastructure.persistence.mapper.OfficeLocationMapper">
    <resultMap id="BaseResultMap" type="com.renpho.erp.smc.infrastructure.persistence.po.OfficeLocationPO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="create_by" jdbcType="INTEGER" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="INTEGER" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>

    </resultMap>

    <sql id="Base_Column_List">
        id
        , status, create_by, create_time, update_by, update_time,is_deleted
    </sql>

    <select id="pageOfficeLocation" resultType="com.renpho.erp.smc.infrastructure.persistence.po.OfficeLocationPO">
        select
        o.id as id,
        o.status as status,
        l.name as name,
        oui.name as updateName,
        o.update_time as updateTime,
        ou.code as userCode,
        CONCAT('[', GROUP_CONCAT('{"name":"',ll.name,'","language":"' ,ll.language,'"}'),']') as names
        from oum_office_location o
        left join oum_office_location_language l on o.id = l.office_location_id and l.language = #{language}
        left join oum_user_info oui on o.update_by = oui.id
        left join oum_office_location_language ll on o.id = ll.office_location_id
        left join oum_user_info ou on o.create_by = ou.id
        <where>
            o.is_deleted = 0 and l.is_deleted = 0 and oui.is_deleted = 0 and ll.is_deleted = 0
            <if test="param.status != null ">
                and o.`status` = #{param.status}
            </if>
            <if test="param.nameList != null and param.nameList.size()!=0 ">
                and l.`office_location_id` in
                <foreach collection="param.nameList" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        group by o.id, o.status, l.name, oui.name, o.update_time,ou.code
        order by o.update_time desc
    </select>

    <select id="listOfficeLocation" resultType="com.renpho.erp.smc.infrastructure.persistence.po.OfficeLocationPO">
        select
        o.id as id,
        o.status as status,
        (CASE WHEN o.status = 0 THEN '禁用' ELSE '启用' END) as statusName,
        o.update_by as updateBy,
        o.update_time as updateTime,
        l.name as name
        from oum_office_location o
        left join oum_office_location_language l on o.id = l.office_location_id and l.language = #{language}
        left join oum_user_info oui on o.update_by = oui.id
        <where>
            o.is_deleted = 0 and l.is_deleted = 0
            <if test="param.status != null ">
                and o.`status` = #{param.status}
            </if>
            <if test="param.nameList != null and param.nameList.size()!=0 ">
                and l.`office_location_id` in
                <foreach collection="param.nameList" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        order by o.status desc
    </select>

</mapper>