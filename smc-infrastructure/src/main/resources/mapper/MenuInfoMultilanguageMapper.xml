<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renpho.erp.smc.infrastructure.persistence.mapper.MenuInfoMultilanguageMapper">


    <resultMap id="BaseResultMap" type="com.renpho.erp.smc.infrastructure.persistence.po.MenuInfoPO">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="component_path" jdbcType="VARCHAR" property="componentPath" />
        <result column="create_by" jdbcType="INTEGER" property="createBy" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="is_cached" jdbcType="VARCHAR" property="isCached" />
        <result column="is_deleted" jdbcType="INTEGER" property="deleted" />
        <result column="menu_icon" jdbcType="VARCHAR" property="menuIcon" />
        <result column="multi_language_names" jdbcType="VARCHAR" property="multiLanguageNames" />
        <result column="menu_type" jdbcType="VARCHAR" property="menuType" />
        <result column="parent_id" jdbcType="INTEGER" property="parentId" />
        <result column="perms" jdbcType="VARCHAR" property="perms" />
        <result column="route_address" jdbcType="VARCHAR" property="routeAddress" />
        <result column="route_params" jdbcType="VARCHAR" property="routeParams" />
        <result column="sort" jdbcType="INTEGER" property="sort" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="system_module" jdbcType="VARCHAR" property="systemModule" />
        <result column="update_by" jdbcType="INTEGER" property="updateBy" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>

    <update id="updateLanguage">
        update sys_menu_language
        <set>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
            <if test="name != null and name != ''">
                `name` = #{name},
            </if>
        </set>
        where menu_id = #{menuId} and `language` = #{language}
    </update>

    <delete id="deleteByMenuId">
        update sys_menu_language set is_deleted =1 where menu_id = #{menuId}
    </delete>


</mapper>
