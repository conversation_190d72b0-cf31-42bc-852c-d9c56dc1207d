<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renpho.erp.smc.infrastructure.persistence.mapper.DictItemMapper">

    <resultMap id="BaseResultMap" type="com.renpho.erp.smc.infrastructure.persistence.po.DictItemPO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="dict_id" jdbcType="INTEGER" property="dictId"/>
        <result column="dict_key" jdbcType="VARCHAR" property="dictKey"/>
        <result column="dict_type" jdbcType="VARCHAR" property="dictType"/>
        <result column="multi_language_names" jdbcType="VARCHAR" property="multiLanguageNames"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_by" jdbcType="INTEGER" property="createBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_by" jdbcType="INTEGER" property="updateBy"/>
        <result column="is_deleted" jdbcType="INTEGER" property="deleted"/>
    </resultMap>

    <sql id="Base_Column_List">
        di
        .
        id
        ,
        dl.language,
        dl.name,
        di.dict_id,
        di.dict_key,
        di.dict_type,
        di.remark,
        di.sort,
        di.status,
        di.create_time,
        di.create_by,
        di.update_by,
        di.update_time,
        di.is_deleted
    </sql>

    <update id="updateDictItem">
        update sys_dict_item
        <set>
            <if test="dictId != null">
                dict_id = #{dictId,jdbcType=INTEGER},
            </if>
            <if test="dictKey != null">
                dict_key = #{dictKey,jdbcType=VARCHAR},
            </if>
            <if test="dictType != null">
                dict_type = #{dictType,jdbcType=VARCHAR},
            </if>
            <if test="multiLanguageNames != null">
                multi_language_names = #{multiLanguageNames,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=INTEGER},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=INTEGER},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            update_time = now()
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectDictItemList" resultType="com.renpho.erp.smc.infrastructure.persistence.po.DictItemPO">
        SELECT
        dii.id,
        dll.LANGUAGE,
        dll.NAME,
        dii.dict_id,
        dii.dict_key,
        dii.dict_type,
        dii.remark,
        dii.sort,
        dii.STATUS,
        dii.`create_time`,
        dii.create_by,
        dii.update_by,
        dii.`update_time`,
        dii.is_deleted
        FROM
        (
        SELECT
        sd.id,
        sd.dict_id,
        sd.dict_key,
        sd.dict_type,
        sd.remark,
        sd.sort,
        sd.`status`,
        sd.create_time,
        sd.create_by,
        sd.update_by,
        sd.update_time,
        sd.is_deleted
        FROM sys_dict_item sd
        JOIN (
        SELECT di.id
        FROM sys_dict_item di
        JOIN sys_dict_item_language dl ON di.id = dl.dict_item_id
        <where>
            di.is_deleted = 0 and dl.is_deleted = 0
            <if test="item.language != null and item.language != ''">
                and dl.language=#{item.language} AND dl.name like concat('%', #{item.dictName}, '%')
            </if>
            <if test="item.dictType != null and item.dictType != ''">
                and dict_type = #{item.dictType}
            </if>
            <if test="item.status != null">
                and status = #{item.status}
            </if>
        </where>
        GROUP BY di.id
        order by sort, di.id
        <if test="item.pageNum != null and item.pageSize != null">
            limit #{item.pageNum},#{item.pageSize}
        </if>) a ON sd.id = a.id
        ) dii
        JOIN sys_dict_item_language dll ON dii.id = dll.dict_item_id
        order by sort, id

    </select>

    <select id="selectDictItemNameCount" resultType="java.lang.Boolean">
        select count(1)
        from sys_dict_item di
        join sys_dict_item_language dl on di.id = dl.dict_item_id
        <where>
            di.is_deleted = 0
            <if test="names != null and names.size() > 0">
                and
                <foreach collection="names" item="item" open="(" separator="or" close=")">
                    dl.language=#{item.language} AND dl.name = #{item.name}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectDictItemDetailById"
            resultType="com.renpho.erp.smc.infrastructure.persistence.po.DictItemPO">
        select
        <include refid="Base_Column_List"/>
        from sys_dict_item di
        join sys_dict_item_language dl on di.id = dl.dict_item_id
        where di.id = #{id}
    </select>

    <select id="selectDictItemCount" resultType="java.lang.Long">
        SELECT
        <include refid="Base_Column_List"/>
        from sys_dict_item di
        join sys_dict_item_language dl on di.id = dl.dict_item_id
        <where>
            di.is_deleted = 0 and dl.is_deleted = 0
            <if test="item.language != null and item.language != ''">
                and dl.language=#{item.language} AND dl.name like concat('%', #{item.dictName}, '%')
            </if>
            <if test="item.dictType != null and item.dictType != ''">
                and dict_type = #{item.dictType}
            </if>
            <if test="item.status != null and item.status != ''">
                and status = #{item.status}
            </if>
            <if test="item.dictType != null and item.dictType != ''">
                and sdi.dict_type = #{item.dictType}
            </if>
        </where>

    </select>

    <select id="selectActiveDictList" resultType="com.renpho.erp.smc.infrastructure.persistence.po.DictItemPO">
        select
        <include refid="Base_Column_List"/>
        from sys_dict_item di
        join sys_dict_item_language dl on di.id = dl.dict_item_id
        where di.status = 1 and di.is_deleted = 0
        order by sort asc
    </select>
    <select id="selectDictItemListByName"
            resultType="com.renpho.erp.smc.infrastructure.persistence.po.DictItemPO">
        SELECT
        mde.id,
        mde.dict_id,
        mde.dict_key,
        mde.status,
        mde.dict_type,
        mde.sort,
        mde.remark,
        CONCAT('[', GROUP_CONCAT('{"name":"', sdd.name, '","language":"', sdd.language, '"}'), ']') AS
        multiLanguageNames
        FROM
        (
        SELECT * FROM
        (
        SELECT
        sdi.*,
        COALESCE(
        (SELECT sdil.name FROM sys_dict_item_language sdil WHERE sdil.dict_item_id = sdi.id AND sdil.language =
        #{item.language}),
        (SELECT sdil.name FROM sys_dict_item_language sdil WHERE sdil.dict_item_id = sdi.id AND sdil.language = 'en-US')
        ) AS multiLanguageName
        FROM
        sys_dict_item sdi
        <where>
            sdi.is_deleted = 0
            <if test="item.status != null and item.status != ''">
                and sdi.status = #{item.status}
            </if>
            <if test="item.dictType != null and item.dictType != ''">
                and sdi.dict_type = #{item.dictType}
            </if>
        </where>
        ) AS de
        <if test="item.dictName != null and item.dictName != ''">
            WHERE multiLanguageName LIKE CONCAT('%', #{item.dictName}, '%')
        </if>
        ) AS mde
        LEFT JOIN
        sys_dict_item_language sdd ON sdd.dict_item_id = mde.id
        GROUP BY
        mde.id, mde.status, mde.dict_type
        ORDER BY
        mde.status DESC, mde.id ASC
        <if test="item.pageNum != null and item.pageSize != null">
            limit #{item.pageNum},#{item.pageSize}
        </if>
    </select>

    <select id="selectDictItemLanguageCount" resultType="java.lang.Boolean">
        select count(1)
        from sys_dict_item di
        join sys_dict_item_language dl on di.id = dl.dict_item_id
        <where>
            di.is_deleted = 0
            <if test="dictType != null and dictType != ''">
                and dict_type = #{dictType}
            </if>
            and dl.language = #{item.language}
            and dl.name = #{item.name}
        </where>
    </select>

    <resultMap id="selectDetailByIdResultMap" type="com.renpho.erp.smc.infrastructure.persistence.po.DictItemPO">
        <id column="id" property="id"/>
        <result column="dict_type" property="dictType"/>
        <result column="dict_key" property="dictKey"/>
        <result column="status" property="status"/>
        <collection property="names" ofType="com.renpho.erp.smc.infrastructure.persistence.po.DictItemLanguagePO">
            <result column="language" property="language"/>
            <result column="name" property="name"/>
        </collection>
    </resultMap>
    <select id="selectDetailById" resultMap="selectDetailByIdResultMap">
        SELECT DI.id, DI.dict_type, DI.dict_key, DI.status, DIL.language, DIL.name
        FROM sys_dict_item AS DI
                 INNER JOIN sys_dict_item_language AS DIL
                            ON DI.id = DIL.dict_item_id AND DIL.is_deleted = 0
        WHERE DI.id =
              #{id}
    </select>

    <resultMap id="selectAllActivedResultMap" type="com.renpho.erp.smc.infrastructure.persistence.po.DictItemPO">
        <id column="id" property="id"/>
        <result column="dict_type" property="dictType"/>
        <result column="dict_key" property="dictKey"/>
        <result column="status" property="status"/>
        <collection property="names" ofType="com.renpho.erp.smc.infrastructure.persistence.po.DictItemLanguagePO">
            <result column="language" property="language"/>
            <result column="name" property="name"/>
        </collection>
    </resultMap>
    <select id="selectAllActived" resultMap="selectAllActivedResultMap">
        SELECT DI.id, DI.dict_type, DI.dict_key, DI.status, DIL.language, DIL.name
        FROM sys_dict_item AS DI
                 INNER JOIN sys_dict_item_language AS DIL
                            ON DI.id = DIL.dict_item_id AND DIL.is_deleted = 0
        WHERE DI.status = 1
          AND DI.is_deleted = 0
    </select>

    <select id="selectActivedByType" resultMap="selectAllActivedResultMap">
        SELECT DI.id, DI.dict_type, DI.dict_key, DI.status, DIL.language, DIL.name
        FROM sys_dict_item AS DI
        INNER JOIN sys_dict_item_language AS DIL
        ON DI.id = DIL.dict_item_id AND DIL.is_deleted = 0
        WHERE DI.status = 1
        AND DI.is_deleted = 0
        <if test="dictId != null and dictId != ''">
            AND DI.dict_id = #{dictId}
        </if>
    </select>

</mapper>
