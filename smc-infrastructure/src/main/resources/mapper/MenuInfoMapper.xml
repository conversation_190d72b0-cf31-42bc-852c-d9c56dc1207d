<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renpho.erp.smc.infrastructure.persistence.mapper.MenuInfoMapper">


    <resultMap id="BaseResultMap" type="com.renpho.erp.smc.infrastructure.persistence.po.MenuInfoPO">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="component_path" jdbcType="VARCHAR" property="componentPath" />
        <result column="create_by" jdbcType="INTEGER" property="createBy" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="is_cached" jdbcType="VARCHAR" property="isCached" />
        <result column="is_deleted" jdbcType="INTEGER" property="deleted" />
        <result column="menu_icon" jdbcType="VARCHAR" property="menuIcon" />
        <result column="multi_language_names" jdbcType="VARCHAR" property="multiLanguageNames" />
        <result column="menu_type" jdbcType="VARCHAR" property="menuType" />
        <result column="parent_id" jdbcType="INTEGER" property="parentId" />
        <result column="perms" jdbcType="VARCHAR" property="perms" />
        <result column="route_address" jdbcType="VARCHAR" property="routeAddress" />
        <result column="route_params" jdbcType="VARCHAR" property="routeParams" />
        <result column="sort" jdbcType="INTEGER" property="sort" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="system_module" jdbcType="VARCHAR" property="systemModule" />
        <result column="update_by" jdbcType="INTEGER" property="updateBy" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        mi.id,
            ml.`language`,
            ml.`name`,
            mi.component_path,
            mi.menu_icon,
            mi.menu_type,
            mi.parent_id,
            mi.perms,
            mi.route_address,
            mi.route_params,
            mi.sort,
            mi.`status`,
            mi.system_module,
            mi.is_cached,
            mi.is_deleted,
            mi.create_by,
            mi.create_time,
            mi.update_by,
            mi.update_time
    </sql>

    <update id="updateMenu">
        update sys_menu_info
        <set>
            <if test="parentId != null">
                parent_id = #{parentId},
            </if>
            <if test="systemModule != null and systemModule != ''">
                system_module = #{systemModule},
            </if>
            <if test="componentPath != null and componentPath != ''">
                component_path = #{componentPath},
            </if>
            <if test="isCached != null">
                is_cached = #{isCached},
            </if>
            <if test="menuIcon != null and menuIcon != ''">
                menu_icon = #{menuIcon},
            </if>
            <if test="menuType != null and menuType != ''">
                menu_type = #{menuType},
            </if>
            <if test="multiLanguageNames != null and multiLanguageNames != ''">
                multi_language_names = #{multiLanguageNames},
            </if>
            <if test="perms != null and perms != ''">
                perms = #{perms},
            </if>
            <if test="routeAddress != null and routeAddress != ''">
                route_address = #{routeAddress},
            </if>
            <if test="routeParams != null and routeParams != ''">
                route_params = #{routeParams},
            </if>
            <if test="sort != null">
                sort = #{sort},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="createBy != null">
                create_by = #{createBy},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>

        </set>
        where id = #{id}
    </update>


    <select id="selectMenuList" resultType="com.renpho.erp.smc.infrastructure.persistence.po.MenuInfoPO">
        select <include refid="Base_Column_List"/>
        from sys_menu_info mi
        join sys_menu_language ml on mi.id = ml.menu_id
        <where>
            mi.is_deleted = 0
            and ml.is_deleted = 0
            <if test="language != null and language != ''">
                and ml.language=#{language} AND ml.name like concat('%', #{name}, '%')
            </if>
            <if test="systemModules != null and systemModules.size() > 0 ">
                and system_module in
                <foreach collection="systemModules" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
        </where>
        order by mi.sort, mi.id
    </select>

    <select id="selectTypeMenuList" resultType="com.renpho.erp.smc.infrastructure.persistence.po.MenuInfoPO">
        select
        <include refid="Base_Column_List"/>
        from sys_menu_info mi
        join sys_menu_language ml on mi.id = ml.menu_id
        where menu_type != 'Button'
        and mi.is_deleted = 0
        and ml.is_deleted = 0
    </select>

    <select id="selectMenuLanguageCount" resultType="java.lang.Boolean">
        select count(1)
        from sys_menu_info mi
        left join sys_menu_language ml on mi.id = ml.menu_id
        <where>
            mi.is_deleted = 0
            and ml.language= #{item.language}
            AND ml.name = #{item.name}
        </where>
    </select>

    <select id="selectMenuById" resultType="com.renpho.erp.smc.infrastructure.persistence.po.MenuInfoPO">
        select <include refid="Base_Column_List"/>
        from sys_menu_info mi
        join sys_menu_language ml on mi.id = ml.menu_id
        where mi.id = #{id}
    </select>

    <select id="selectMenuTreeByUserId"
            resultType="com.renpho.erp.smc.infrastructure.persistence.po.MenuInfoPO">
        select *
        from sys_menu_info
        where deleted = 0
        and status = 1
        and id in (select menu_id from smc_role_menu_info where role_id in (select role_id from smc_user_role_info where
        user_id = #{userId}))
    </select>

    <select id="selectMenuByParentId" resultType="com.renpho.erp.smc.infrastructure.persistence.po.MenuInfoPO">
        select *
        from sys_menu_info
        where id = #{parentId} and is_deleted = 0
    </select>

</mapper>
