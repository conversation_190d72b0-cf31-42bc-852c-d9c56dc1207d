<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.renpho.erp.smc.infrastructure.persistence.mapper.PositionMapper">
    <select id="selectByCondition" resultType="com.renpho.erp.smc.infrastructure.persistence.po.PositionPO">
        SELECT
        op.id,
        op.department_id,
        COALESCE(
        (SELECT opl.name FROM oum_position_language opl WHERE opl.position_id = op.id AND opl.language =
        #{language}),
        (SELECT opl.name FROM oum_position_language opl WHERE opl.position_id = op.id AND opl.language = 'en-US')
        ) AS name,
        op.code,
        (SELECT COUNT(*)
        FROM oum_user_info uinfo
        WHERE uinfo.position_id = op.id and uinfo.is_deleted=0 and uinfo.status=1 ) AS member,
        op.remark,
        op.status,
        (
        SELECT GROUP_CONCAT(d2.name SEPARATOR ',')
        FROM oum_department_language d2
        WHERE d2.is_deleted = 0 AND d2.language = #{language}
        AND JSON_CONTAINS(op.department_id, CAST(d2.department_id AS CHAR), '$')
        ) AS departmentName
        FROM
        oum_position op
        LEFT JOIN
        oum_department_language AS d
        ON JSON_CONTAINS(op.department_id, CAST(d.department_id AS CHAR), '$')
        <where>
            op.is_deleted = 0
            <if test="param.code != null and param.code != ''">
                AND op.code = #{param.code}
            </if>
            <if test="param.status != null">
                AND op.status = #{param.status}
            </if>
            <if test="param.departmentIds != null and param.departmentIds.size() > 0">
                AND d.is_deleted = 0 and d.language=#{language} AND d.department_id IN
                <foreach collection="param.departmentIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.ids != null and param.ids.size() > 0">
                AND op.id IN
                <foreach collection="param.ids" item="currentid" open="(" separator="," close=")">
                    #{currentid}
                </foreach>
            </if>
        </where>
        GROUP BY
        op.id, op.department_id, op.code, member, op.remark, op.status
        order by op.update_time desc
    </select>
    <select id="selectByConditionCount" resultType="java.lang.Integer">
        SELECT
        COUNT(distinct op.id)
        FROM
        oum_position op
        LEFT JOIN
        oum_department_language AS d
        ON JSON_CONTAINS(op.department_id, CAST(d.department_id AS CHAR), '$')
        <where>
            op.is_deleted = 0
            <if test="param.code != null and param.code != ''">
                AND op.code = #{param.code}
            </if>
            <if test="param.status != null">
                AND op.status = #{param.status}
            </if>
            <if test="param.departmentIds != null and param.departmentIds.size() > 0">
                AND d.is_deleted = 0 AND d.department_id IN
                <foreach collection="param.departmentIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.ids != null and param.ids.size() > 0">
                AND op.id IN
                <foreach collection="param.ids" item="currentid" open="(" separator="," close=")">
                    #{currentid}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectNameList" resultType="com.renpho.erp.smc.infrastructure.persistence.po.PositionPO">
        select distinct op.id,opl.name as name,op.status as status
        from oum_position op
        left join oum_position_language opl
        on op.id = opl.position_id
        left join
        oum_department_language d
        ON JSON_CONTAINS(op.department_id, CAST(d.department_id AS CHAR), '$')
        <where>
            op.is_deleted = 0 and opl.language = #{language} and opl.is_deleted = 0
            <if test="name !=null">
                AND opl.name like concat('%',#{name},'%') and opl.language = #{language}
            </if>
            <if test="status !=null">
                AND op.status = #{status}
            </if>
            <if test="departmentIds != null and departmentIds.size() > 0">
                AND d.is_deleted = 0 and d.language=#{language} AND d.department_id IN
                <foreach collection="departmentIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by op.status desc
    </select>
    <select id="page" resultType="com.renpho.erp.smc.infrastructure.persistence.po.PositionPO">
        SELECT
        op.id,
        op.department_id,
        COALESCE(
        (SELECT opl.name FROM oum_position_language opl WHERE opl.position_id = op.id AND opl.language = #{language}),
        (SELECT opl.name FROM oum_position_language opl WHERE opl.position_id = op.id AND opl.language = 'en-US')
        ) AS name,
        op.code,
        (SELECT COUNT(*)
        FROM oum_user_info uinfo
        WHERE uinfo.position_id = op.id and uinfo.is_deleted=0 and uinfo.status=1 ) AS member,
        op.remark,
        op.status,
        (
        SELECT GROUP_CONCAT(d2.name SEPARATOR ',')
        FROM oum_department_language d2
        WHERE d2.is_deleted = 0 AND d2.language = #{language}
        AND JSON_CONTAINS(op.department_id, CAST(d2.department_id AS CHAR), '$')
        ) AS departmentName
        FROM
        oum_position op
        LEFT JOIN
        oum_department_language AS d
        ON JSON_CONTAINS(op.department_id, CAST(d.department_id AS CHAR), '$')
        <where>
            op.is_deleted = 0
            <if test="code != null and code != ''">
                AND op.code = #{code}
            </if>
            <if test="status != null">
                AND op.status = #{status}
            </if>
            <if test="departmentIds != null and departmentIds.size() > 0">
                AND d.is_deleted = 0 and d.language=#{language} AND d.department_id IN
                <foreach collection="departmentIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="ids != null and ids.size() > 0">
                AND op.id IN
                <foreach collection="ids" item="currentid" open="(" separator="," close=")">
                    #{currentid}
                </foreach>
            </if>
        </where>
        GROUP BY
        op.id, op.department_id, op.code, member, op.remark, op.status
    </select>
    <select id="pagePositionUser" resultType="com.renpho.erp.smc.infrastructure.persistence.po.PositionPO">
        select op.id as id,
        opl.name AS name,
        op.code as code,
        oui.id as userId,
        oui.code as userCode,
        oui.name as userName,
        oui.position_added_time as addedTime,
        odl.name as departmentName
        from oum_position op
        inner join oum_user_info oui on oui.position_id = op.id
        left join oum_position_language opl on opl.position_id = op.id AND opl.language = #{language}
        left join oum_department_user odu on odu.user_id = oui.id and odu.is_deleted = 0 and
        odu.is_primary=1
        left join oum_department_language odl on odu.department_id = odl.department_id and odl.language = #{language}
        and odl.is_deleted = 0
        <where>
            op.is_deleted = 0 and oui.is_deleted = 0 and oui.status= 1
            <if test="positionId != null">
                AND op.id = #{positionId}
            </if>
        </where>
    </select>
    <select id="selectDetailById" resultType="com.renpho.erp.smc.infrastructure.persistence.po.PositionPO">
        SELECT op.code,
               op.status,
               op.remark,
               op.department_id,
               op.id,
               (SELECT GROUP_CONCAT(odl.name SEPARATOR ',')
                FROM oum_department_language odl
                WHERE odl.is_deleted = 0
                  AND odl.language = #{language}
                  AND JSON_CONTAINS(op.department_id, CAST(odl.department_id AS CHAR), '$')) AS departmentName
        from oum_position op
        where op.id = #{id}
    </select>
    <select id="selectByConditionPage"
            resultType="com.renpho.erp.smc.infrastructure.persistence.po.PositionPO">
        SELECT
        op.id,
        op.department_id,
        opl.name as name,
        op.code,
        (SELECT COUNT(*)
        FROM oum_user_info uinfo
        WHERE uinfo.position_id = op.id and uinfo.is_deleted=0 and uinfo.status=1 ) AS member,
        op.remark,
        op.status,
        (
        SELECT GROUP_CONCAT(d2.name SEPARATOR ',')
        FROM oum_department_language d2
        WHERE d2.is_deleted = 0 AND d2.language = #{language}
        AND JSON_CONTAINS(op.department_id, CAST(d2.department_id AS CHAR), '$')
        ) AS departmentName
        FROM
        oum_position op
        LEFT JOIN
        oum_department_language AS d
        ON JSON_CONTAINS(op.department_id, CAST(d.department_id AS CHAR), '$')
        left join oum_position_language opl on opl.position_id = op.id AND opl.language = #{language} and opl.is_deleted
        = 0
        <where>
            op.is_deleted = 0
            <if test="param.code != null and param.code != ''">
                AND op.code = #{param.code}
            </if>
            <if test="param.status != null">
                AND op.status = #{param.status}
            </if>
            <if test="param.departmentIds != null and param.departmentIds.size() > 0">
                AND d.is_deleted = 0 and d.language=#{language} AND d.department_id IN
                <foreach collection="param.departmentIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.ids != null and param.ids.size() > 0">
                AND op.id IN
                <foreach collection="param.ids" item="currentid" open="(" separator="," close=")">
                    #{currentid}
                </foreach>
            </if>
        </where>
        GROUP BY
        op.id, op.department_id, op.code, member, op.remark, op.status,opl.name
        order by op.update_time desc
    </select>
</mapper>