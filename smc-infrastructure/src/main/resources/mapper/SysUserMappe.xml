<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renpho.erp.smc.infrastructure.persistence.mapper.SysUserMapper">
  <resultMap id="BaseResultMap" type="com.renpho.erp.smc.infrastructure.persistence.po.SysUserPO">
    
     <result column="id" jdbcType="INTEGER" property="id" />
     <result column="dept_id" jdbcType="INTEGER" property="deptId" />
     <result column="username" jdbcType="VARCHAR" property="username" />
     <result column="nick_name" jdbcType="VARCHAR" property="nickName" />
     <result column="user_type" jdbcType="VARCHAR" property="userType" />
     <result column="title" jdbcType="VARCHAR" property="title" />
     <result column="email" jdbcType="VARCHAR" property="email" />
     <result column="phonenumber" jdbcType="VARCHAR" property="phonenumber" />
     <result column="sex" jdbcType="INTEGER" property="sex" />
     <result column="avatar" jdbcType="VARCHAR" property="avatar" />
     <result column="password" jdbcType="VARCHAR" property="password" />
     <result column="dingtalk_union_id" jdbcType="VARCHAR" property="dingtalkUnionId" />
     <result column="dingtalk_user_id" jdbcType="VARCHAR" property="dingtalkUserId" />
     <result column="work_place" jdbcType="VARCHAR" property="workPlace" />
     <result column="work_number" jdbcType="VARCHAR" property="workNumber" />
     <result column="hired_date" jdbcType="TIMESTAMP" property="hiredDate" />
     <result column="status" jdbcType="INTEGER" property="status" />
     <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
     <result column="login_ip" jdbcType="VARCHAR" property="loginIp" />
     <result column="login_date" jdbcType="TIMESTAMP" property="loginDate" />
     <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
     <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
     <result column="remark" jdbcType="VARCHAR" property="remark" />
     <result column="create_by" jdbcType="INTEGER" property="createBy" />
     <result column="update_by" jdbcType="INTEGER" property="updateBy" />
    
  </resultMap>

  <sql id="Base_Column_List">
      id, dept_id, username, nick_name, user_type, title, email, phonenumber, sex, avatar, password, dingtalk_union_id, dingtalk_user_id, work_place, work_number, hired_date, status, is_deleted, login_ip, login_date, create_time, update_time, remark, create_by, update_by
  </sql>


</mapper>