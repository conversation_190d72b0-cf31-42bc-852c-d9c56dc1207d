<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renpho.erp.smc.infrastructure.persistence.mapper.DepartmentMapper">
    <resultMap id="BaseResultMap" type="com.renpho.erp.smc.infrastructure.persistence.po.DepartmentPO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="parent_id" jdbcType="INTEGER" property="parentId"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="manager_id" jdbcType="INTEGER" property="managerId"/>
        <result column="level_label" jdbcType="VARCHAR" property="levelLabel"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_by" jdbcType="INTEGER" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="INTEGER" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">parent_id
    , code, manager_id, level_label, sort, `status`, create_by, create_time,
    update_by, update_time, is_deleted
    </sql>

    <select id="selectDetailById" resultType="com.renpho.erp.smc.infrastructure.persistence.po.DepartmentPO">
        select od.id          as id,
               od.code        as code,
               od.manager_id  as managerId,
               od.parent_id   as parentId,
               od.sort        as sort,
               od.level_label as levelLabel,
               od.status      as status,
               oui.name       as managerName,
               odl.name       as parentName,
               odll.name      as name

        from oum_department od
                 left join oum_user_info oui on oui.id = od.manager_id
                 left join oum_department_language odl
                           on od.parent_id = odl.department_id and odl.language = #{language}
                 left join oum_department_language odll on od.id = odll.department_id and odll.language = #{language}
        where od.id = #{id}
          and od.is_deleted = 0
    </select>
    <select id="getParentDepartmentStatus" resultType="java.lang.Integer">
        select status
        from oum_department as
        where id = #{id}
    </select>
    <select id="findUserByDepartmentId" resultType="java.lang.Integer">
        select ou.id
        from oum_user ou
        where ou.department_id = #{id}
          and ou.is_deleted = 0
    </select>
    <select id="selectNameList" resultType="com.renpho.erp.smc.infrastructure.persistence.po.DepartmentPO">
        select od.id,
        od.code,
        od.manager_Id as managerId,
        odl.name as name,
        od.parent_id as parentId,
        od.sort as sort,
        od.status as status,
        od.is_deleted as deleted
        from oum_department od left join oum_department_language odl on od.id = odl.department_id and odl.language =
        #{language} and odl.is_deleted = 0
        where 1=1
        <if test="status != 2">
            and od.is_deleted = 0
        </if>
        <if test="status != null and status != 2">
            and od.status = #{status}
        </if>
    </select>
    <select id="selectDepartmentNameUserList"
            resultType="com.renpho.erp.smc.infrastructure.persistence.po.DepartmentPO">
        SELECT od.id,
               odl.name     AS name,
               od.parent_id AS parentId,
               ouii.name    AS managerName,
               od.sort      AS sort,
               od.status    AS status
        FROM oum_department od
                 LEFT JOIN oum_department_language odl
                           ON od.id = odl.department_id AND odl.language = #{language} and odl.is_deleted = 0
                 LEFT JOIN oum_department_user odu ON odu.department_id = od.id and odu.is_deleted = 0
                 LEFT JOIN oum_user_info oui ON oui.id = odu.user_id and oui.is_deleted = 0 and od.status = 1
                 left join oum_user_info ouii on ouii.id = od.manager_id and ouii.is_deleted = 0 and ouii.status = 1
        where od.is_deleted = 0
        GROUP BY od.id, odl.name
    </select>
    <select id="exportDepartmentList"
            resultType="com.renpho.erp.smc.infrastructure.persistence.po.DepartmentPO">
        select od.code as code,
        odl.name as name,
        oui.name as managerName,
        odd.code as parentCode,
        odll.name as parentName,
        od.level_label as levelLabel,
        od.status as status
        from oum_department od
        left join oum_user_info oui on oui.id = od.manager_id and oui.is_deleted = 0
        left join oum_department_language odl on od.id = odl.department_id and odl.language = #{language} and
        odl.is_deleted = 0
        left join oum_department_language odll on od.parent_id = odll.department_id and odll.language = #{language} and
        odll.is_deleted = 0
        left join oum_department odd on od.parent_id = odd.id and odd.is_deleted = 0
        <where>
            od.is_deleted = 0 and od.status = 1
        </where>
        order by od.sort,od.id
    </select>
    <select id="selectNoHierarchicalList"
            resultType="com.renpho.erp.smc.infrastructure.persistence.po.DepartmentPO">
        select odl.name as name,
        od.id as id,
        od.sort as sort,
        od.status as status,
        od.parent_id as parentId
        from oum_department od
        left join oum_department_language odl on od.id = odl.department_id and odl.language = #{language} and
        odl.is_deleted = 0
        <where>
            <if test="status != null and status !=''">
                and od.status = #{status}
            </if>
        </where>
        order by od.status desc, od.sort, od.id
    </select>
    <select id="selectByIdList" resultType="com.renpho.erp.smc.infrastructure.persistence.po.DepartmentPO">
        select od.id,
        od.code as code,
        odl.name as name,
        od.parent_id as parentId,
        od.manager_id as managerId,
        od.sort as sort,
        od.status as status
        from oum_department od left join oum_department_language odl on od.id = odl.department_id and odl.language =
        #{language} and odl.is_deleted = 0
        where 1=1
        <if test="status != 2">
            and od.is_deleted = 0
        </if>
        <if test="status != null and status != 2">
            and od.status = #{status}
        </if>
        <if test="deptIdList != null and deptIdList.size() > 0">
            AND od.id IN
            <foreach collection="deptIdList" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        order by od.status desc, od.sort, od.id
    </select>

    <!--    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">-->
    <!--        select-->
    <!--        <include refid="Base_Column_List"/>-->
    <!--        from oum_department-->
    <!--        where id = #{id,jdbcType=INTEGER}-->
    <!--    </select>-->
    <!--    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">-->
    <!--        delete-->
    <!--        from oum_department-->
    <!--        where id = #{id,jdbcType=INTEGER}-->
    <!--    </delete>-->
    <!--    &lt;!&ndash;    <insert id="insert" keyColumn="id" keyProperty="id"&ndash;&gt;-->
    <!--    &lt;!&ndash;            parameterType="com.renpho.erp.smc.infrastructure.persistence.po.DepartmentPO" useGeneratedKeys="true">&ndash;&gt;-->
    <!--    &lt;!&ndash;        insert into oum_department (parent_id, code, manager_id,&ndash;&gt;-->
    <!--    &lt;!&ndash;                                    level_label, sort, `status`,&ndash;&gt;-->
    <!--    &lt;!&ndash;                                    create_by, create_time, update_by,&ndash;&gt;-->
    <!--    &lt;!&ndash;                                    update_time, is_deleted)&ndash;&gt;-->
    <!--    &lt;!&ndash;        values (#{parentId,jdbcType=INTEGER}, #{code,jdbcType=VARCHAR}, #{managerId,jdbcType=INTEGER},&ndash;&gt;-->
    <!--    &lt;!&ndash;                #{levelLabel,jdbcType=VARCHAR}, #{sort,jdbcType=INTEGER}, #{status,jdbcType=VARCHAR},&ndash;&gt;-->
    <!--    &lt;!&ndash;                #{createBy,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=INTEGER},&ndash;&gt;-->
    <!--    &lt;!&ndash;                #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=INTEGER})&ndash;&gt;-->
    <!--    &lt;!&ndash;    </insert>&ndash;&gt;-->
    <!--    <insert id="insertSelective" keyColumn="id" keyProperty="id"-->
    <!--            parameterType="com.renpho.erp.smc.infrastructure.persistence.po.DepartmentPO" useGeneratedKeys="true">-->
    <!--        insert into oum_department-->
    <!--        <trim prefix="(" suffix=")" suffixOverrides=",">-->
    <!--            <if test="parentId != null">-->
    <!--                parent_id,-->
    <!--            </if>-->
    <!--            <if test="code != null">-->
    <!--                code,-->
    <!--            </if>-->
    <!--            <if test="managerId != null">-->
    <!--                manager_id,-->
    <!--            </if>-->
    <!--            <if test="levelLabel != null">-->
    <!--                level_label,-->
    <!--            </if>-->
    <!--            <if test="sort != null">-->
    <!--                sort,-->
    <!--            </if>-->
    <!--            <if test="status != null">-->
    <!--                `status`,-->
    <!--            </if>-->
    <!--            <if test="createBy != null">-->
    <!--                create_by,-->
    <!--            </if>-->
    <!--            <if test="createTime != null">-->
    <!--                create_time,-->
    <!--            </if>-->
    <!--            <if test="updateBy != null">-->
    <!--                update_by,-->
    <!--            </if>-->
    <!--            <if test="updateTime != null">-->
    <!--                update_time,-->
    <!--            </if>-->
    <!--            <if test="isDeleted != null">-->
    <!--                is_deleted,-->
    <!--            </if>-->
    <!--        </trim>-->
    <!--        <trim prefix="values (" suffix=")" suffixOverrides=",">-->
    <!--            <if test="parentId != null">-->
    <!--                #{parentId,jdbcType=INTEGER},-->
    <!--            </if>-->
    <!--            <if test="code != null">-->
    <!--                #{code,jdbcType=VARCHAR},-->
    <!--            </if>-->
    <!--            <if test="managerId != null">-->
    <!--                #{managerId,jdbcType=INTEGER},-->
    <!--            </if>-->
    <!--            <if test="levelLabel != null">-->
    <!--                #{levelLabel,jdbcType=VARCHAR},-->
    <!--            </if>-->
    <!--            <if test="sort != null">-->
    <!--                #{sort,jdbcType=INTEGER},-->
    <!--            </if>-->
    <!--            <if test="status != null">-->
    <!--                #{status,jdbcType=VARCHAR},-->
    <!--            </if>-->
    <!--            <if test="createBy != null">-->
    <!--                #{createBy,jdbcType=INTEGER},-->
    <!--            </if>-->
    <!--            <if test="createTime != null">-->
    <!--                #{createTime,jdbcType=TIMESTAMP},-->
    <!--            </if>-->
    <!--            <if test="updateBy != null">-->
    <!--                #{updateBy,jdbcType=INTEGER},-->
    <!--            </if>-->
    <!--            <if test="updateTime != null">-->
    <!--                #{updateTime,jdbcType=TIMESTAMP},-->
    <!--            </if>-->
    <!--            <if test="isDeleted != null">-->
    <!--                #{isDeleted,jdbcType=INTEGER},-->
    <!--            </if>-->
    <!--        </trim>-->
    <!--    </insert>-->
    <!--    <update id="updateByPrimaryKeySelective"-->
    <!--            parameterType="com.renpho.erp.smc.infrastructure.persistence.po.DepartmentPO">-->
    <!--        update oum_department-->
    <!--        <set>-->
    <!--            <if test="parentId != null">-->
    <!--                parent_id = #{parentId,jdbcType=INTEGER},-->
    <!--            </if>-->
    <!--            <if test="code != null">-->
    <!--                code = #{code,jdbcType=VARCHAR},-->
    <!--            </if>-->
    <!--            <if test="managerId != null">-->
    <!--                manager_id = #{managerId,jdbcType=INTEGER},-->
    <!--            </if>-->
    <!--            <if test="levelLabel != null">-->
    <!--                level_label = #{levelLabel,jdbcType=VARCHAR},-->
    <!--            </if>-->
    <!--            <if test="sort != null">-->
    <!--                sort = #{sort,jdbcType=INTEGER},-->
    <!--            </if>-->
    <!--            <if test="status != null">-->
    <!--                `status` = #{status,jdbcType=VARCHAR},-->
    <!--            </if>-->
    <!--            <if test="createBy != null">-->
    <!--                create_by = #{createBy,jdbcType=INTEGER},-->
    <!--            </if>-->
    <!--            <if test="createTime != null">-->
    <!--                create_time = #{createTime,jdbcType=TIMESTAMP},-->
    <!--            </if>-->
    <!--            <if test="updateBy != null">-->
    <!--                update_by = #{updateBy,jdbcType=INTEGER},-->
    <!--            </if>-->
    <!--            <if test="updateTime != null">-->
    <!--                update_time = #{updateTime,jdbcType=TIMESTAMP},-->
    <!--            </if>-->
    <!--            <if test="isDeleted != null">-->
    <!--                is_deleted = #{isDeleted,jdbcType=INTEGER},-->
    <!--            </if>-->
    <!--        </set>-->
    <!--        where id = #{id,jdbcType=INTEGER}-->
    <!--    </update>-->
    <!--    <update id="updateByPrimaryKey" parameterType="com.renpho.erp.smc.infrastructure.persistence.po.DepartmentPO">-->
    <!--        update oum_department-->
    <!--        set parent_id   = #{parentId,jdbcType=INTEGER},-->
    <!--            code        = #{code,jdbcType=VARCHAR},-->
    <!--            manager_id  = #{managerId,jdbcType=INTEGER},-->
    <!--            level_label = #{levelLabel,jdbcType=VARCHAR},-->
    <!--            sort        = #{sort,jdbcType=INTEGER},-->
    <!--            `status`    = #{status,jdbcType=VARCHAR},-->
    <!--            create_by   = #{createBy,jdbcType=INTEGER},-->
    <!--            create_time = #{createTime,jdbcType=TIMESTAMP},-->
    <!--            update_by   = #{updateBy,jdbcType=INTEGER},-->
    <!--            update_time = #{updateTime,jdbcType=TIMESTAMP},-->
    <!--            is_deleted  = #{isDeleted,jdbcType=INTEGER}-->
    <!--        where id = #{id,jdbcType=INTEGER}-->
    <!--    </update>-->
    <update id="updateAllById" parameterType="com.renpho.erp.smc.infrastructure.persistence.po.DepartmentPO">
        update oum_department
        set parent_id   = #{parentId},
            code        = #{code},
            manager_id  = #{managerId},
            level_label = #{levelLabel},
            sort        = #{sort},
            `status`    = #{status}
        where id = #{id}
    </update>
</mapper>