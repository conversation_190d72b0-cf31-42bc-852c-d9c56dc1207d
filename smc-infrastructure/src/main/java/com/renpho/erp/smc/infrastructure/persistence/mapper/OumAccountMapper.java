package com.renpho.erp.smc.infrastructure.persistence.mapper;

import java.util.Optional;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.OumAccountPO;

/**
 * 参数配置 Mapper.
 *
 * <AUTHOR>
 */
public interface OumAccountMapper extends BaseMapper<OumAccountPO> {

	@Update("""
			update oum_account set pwd = #{passwd}, is_initialized_pwd = 0 where user_id = #{userid}
			""")
	void updatePasswd(@Param("userid") int userid, @Param("passwd") String passwd);

	@Update("""
			 update oum_account set status = #{status} where user_id = #{userid}
			""")
	void updateStatus(@Param("userid") int userid, @Param("status") int status);

	/**
	 * 查询主键id
	 * @param userId 用户id
	 * @return 主键id
	 */
	default Integer getIdByUserId(Integer userId) {
		OumAccountPO account = this
			.selectOne(new LambdaQueryWrapper<OumAccountPO>().select(OumAccountPO::getId).eq(OumAccountPO::getUserId, userId));
		return Optional.ofNullable(account).map(OumAccountPO::getId).orElse(null);
	}
}
