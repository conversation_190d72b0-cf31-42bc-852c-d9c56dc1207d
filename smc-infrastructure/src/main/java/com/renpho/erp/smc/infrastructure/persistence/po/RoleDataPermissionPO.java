package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.AlterationPO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 角色数据权限 PO.
 *
 * <AUTHOR>
 */
@Data
@TableName("oum_role_data_permission")
public class RoleDataPermissionPO extends AlterationPO<Integer, Integer, LocalDateTime, Integer> {

	/**
	 * 角色ID
	 */
	private Integer roleId;

	/**
	 * 菜单ID
	 */
	private Integer menuId;

	/**
	 * 数据范围类型，1 本人及下级、2 自定义
	 */
	private Integer scopeType;

	/**
	 * 设置类型，0 无区分、1 按部门、2 按员工，若scope_type=1，set_type=0
	 */
	private Integer setType;

	/**
	 * 业务编码，来源 department_id、user_id，若scope_type=1，biz_value=0
	 */
	private Integer bizValue;

}
