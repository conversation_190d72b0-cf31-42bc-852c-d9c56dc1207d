package com.renpho.erp.smc.infrastructure.persistence.dto;

import java.time.LocalDateTime;

import lombok.Data;

/**
 * @description: 用户信息表
 * @author: doctor
 * @date: 2024/09/13
 * @version: 1.0.0
 */
@Data
public class OumUserSimpleListDto {

	private Integer id;

	/** 用户工号 **/
	private String code;

	/** 合同地点ID **/
	private String name;

	private String phoneNo;

	/** **/
	private Integer status;

	/** 邮箱 **/
	private String email;

	/** 用户性别，1 男、2 女、3 未知 **/
	private Integer gender;

	/** 入职时间 **/
	private LocalDateTime hireTime;

	/** 全日制还是 **/
	private String type;

	/** 角色标签 */
	private String label;

	private LocalDateTime createTime;

	private LocalDateTime updateTime;

}
