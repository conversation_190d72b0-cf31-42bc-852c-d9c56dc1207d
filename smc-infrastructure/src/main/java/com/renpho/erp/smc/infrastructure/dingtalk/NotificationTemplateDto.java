package com.renpho.erp.smc.infrastructure.dingtalk;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class NotificationTemplateDto implements Serializable {

	/**
	 * 用户姓名
	 */
	private String name;

	/**
	 * 用户工号
	 */
	private String code;

	/**
	 * 业务id
	 */
	private Integer bizId;

	/**
	 * 三方id
	 */
	private String thirdBizId;

	/**
	 * 操作时间（北京）
	 */
	private String optTime;

	/**
	 * 预警时间（北京）
	 */
	private String warningTime;

	/**
	 * 失败原因
	 */
	private String failedReason;

}