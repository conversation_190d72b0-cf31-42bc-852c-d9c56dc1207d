package com.renpho.erp.smc.infrastructure.persistence.dto;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * @description: 用户信息表
 * @author: doctor
 * @date: 2024/09/13
 * @version: 1.0.0
 */
@Data
public class OumUserInfoListDto {

	private Integer id;

	/** 用户工号 **/
	private String code;

	/** 合同地点ID **/
	private String username;

	/** 领导人名 */
	private String reportName;

	/** 领导人名 */
	private String reportUserCode;

	private Integer contractId;

	/** 合同地点多语言 */
	private String contractLocation;

	private Integer officeLocationId;

	/** 办公地点，多语言 */
	private String officeLocation;

	/** 岗位多语言 */
	private String positionName;

	/** 部门多语言 */
	private String departmentName;

	/** 手机号 */
	private String phoneNo;

	/** 员工状态 **/
	private Integer status;

	private String statusName;

	private Integer accountStatus;

	/** 账号状态多语言 */
	private String accountStatusName;

	/** 邮箱 **/
	private String email;

	/** 用户性别，1 男、2 女、3 未知 **/
	private Integer gender;

	/** 入职时间 **/
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime hireTime;

	/** 全日制还是 **/
	private String type;

	/** 员工类型多语言，全日制 */
	private String typeName;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime createTime;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime updateTime;

}
