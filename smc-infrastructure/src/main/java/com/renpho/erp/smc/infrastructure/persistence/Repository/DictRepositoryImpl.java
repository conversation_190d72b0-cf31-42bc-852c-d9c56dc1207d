package com.renpho.erp.smc.infrastructure.persistence.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.mapstruct.Mapping;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.renpho.erp.security.util.SecurityUtils;
import com.renpho.erp.smc.domain.systemsetting.MultiLanguage;
import com.renpho.erp.smc.domain.systemsetting.dict.Dict;
import com.renpho.erp.smc.domain.systemsetting.dict.DictItem;
import com.renpho.erp.smc.domain.systemsetting.dict.DictRepository;
import com.renpho.erp.smc.exception.BizErrorCode;
import com.renpho.erp.smc.infrastructure.persistence.mapper.DictItemMapper;
import com.renpho.erp.smc.infrastructure.persistence.mapper.DictItemMultilanguageMapper;
import com.renpho.erp.smc.infrastructure.persistence.mapper.DictTypeLanguageMapper;
import com.renpho.erp.smc.infrastructure.persistence.mapper.DictTypeMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.DictItemLanguagePO;
import com.renpho.erp.smc.infrastructure.persistence.po.DictItemPO;
import com.renpho.erp.smc.infrastructure.persistence.po.DictTypeLanguagePO;
import com.renpho.erp.smc.infrastructure.persistence.po.DictTypePO;
import com.renpho.erp.smc.infrastructure.persistence.po.transformer.DictTransformer;
import com.renpho.erp.smc.infrastructure.utils.lang.LangUtil;
import com.renpho.karma.exception.ErrorCodeException;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;

/**
 * 字典仓储实现.
 *
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class DictRepositoryImpl implements DictRepository {

	private final DictTypeMapper dictTypeMapper;

	private final DictItemMapper dictItemMapper;

	private final DictTypeLanguageMapper dictTypeLanguageMapper;

	private final DictItemMultilanguageMapper dictItemMultilanguageMapper;

	@Override
	public Dict findByID(Dict.DictID id) {

		DictTypePO dictTypePO = dictTypeMapper.selectOne(
				Wrappers.<DictTypePO> lambdaQuery().select(DictTypePO::getId, DictTypePO::getDictType).eq(DictTypePO::getId, id.getId()));
		if (Objects.isNull(dictTypePO)) {
			throw new ErrorCodeException(BizErrorCode.DICT_NOT_FOUND);
		}

		return null;
	}

	@Override
	public DictItem findDictItemByID(DictItem.DictItemID id) {
		// List<DictItemPO> poList =
		// dictItemMapper.selectDictItemDetailById(id.getId());
		// List<DictItem> dictItems = DictTransformer.INSTANCE.toDictItemList(poList);
		// if (CollUtil.isEmpty(dictItems)) {
		// throw new ErrorCodeException(BizErrorCode.DICT_NOT_FOUND);
		// }
		// return dictItems.get(0);
		DictItemPO dictItemPO = dictItemMapper.selectDetailById(id.getId());
		if (Objects.isNull(dictItemPO)) {
			throw new ErrorCodeException(BizErrorCode.DICT_NOT_FOUND);
		}

		DictItem dictItem = DictTransformer.INSTANCE.toDictItem(dictItemPO);
		return dictItem;
	}

	@Override
	@Mapping(source = "dict.type", target = "DictTypePO.type")
	public void checkDuplication(Dict dict) {
		DictTypePO po = dictTypeMapper.selectDictByType(dict.getType());
		if (Objects.nonNull(po)) {
			throw new ErrorCodeException(BizErrorCode.DICT_TYPE_FOUND, dict.getType());
		}
		for (MultiLanguage multiLanguage : dict.getNames()) {
			String lang = multiLanguage.getLanguage();
			String name = multiLanguage.getName();
			DictTypePO namePo = dictTypeMapper.selectDictByName(multiLanguage.getLanguage(), multiLanguage.getName());
			if (Objects.nonNull(namePo)) {
				throw new ErrorCodeException(BizErrorCode.DICT_TYPE_NAME_FOUND, multiLanguage.getName());
			}
		}
	}

	@Override
	public void update(Dict dict) {
		findByID(dict.getId());
		DictTypePO dictTypePO = DictTransformer.INSTANCE.toDictTypePO(dict);
		dictTypeMapper.updateById(dictTypePO);
		Integer id = dict.getId().getId();
		List<MultiLanguage> languageList = dict.getNames();
		for (MultiLanguage multiLanguage : languageList) {
			DictTypeLanguagePO languagepo = dictTypeLanguageMapper.selectOne(Wrappers.<DictTypeLanguagePO> lambdaQuery()
				.eq(DictTypeLanguagePO::getDictTypeId, id)
				.eq(DictTypeLanguagePO::getLanguage, multiLanguage.getLanguage()));
			if (Objects.isNull(languagepo)) {
				DictTypeLanguagePO addPo = DictTransformer.INSTANCE.toDictTypeLanguagePO(multiLanguage, id);
				dictTypeLanguageMapper.insert(addPo);
			}
			else {
				DictTypeLanguagePO updatePo = DictTransformer.INSTANCE.toDictTypeLanguagePO(multiLanguage, id);
				updatePo.setId(languagepo.getId());
				dictTypeLanguageMapper.updateById(updatePo);
			}
		}
		List<String> languageExistList = languageList.stream().map(MultiLanguage::getLanguage).collect(Collectors.toList());

		LambdaQueryWrapper<DictTypeLanguagePO> queryWrapper = Wrappers.lambdaQuery(DictTypeLanguagePO.class)
			.eq(DictTypeLanguagePO::getDictTypeId, id)
			.notIn(DictTypeLanguagePO::getLanguage, languageExistList);
		dictTypeLanguageMapper.delete(queryWrapper);

	}

	@Override
	public void updateDictItem(DictItem dictItem) {
		DictItemPO dictItemPO = DictTransformer.INSTANCE.toDictItemPO(dictItem);
		dictItemPO.setUpdateBy(SecurityUtils.getUserId());

		dictItemMapper.updateDictItem(dictItemPO);

		if (CollUtil.isNotEmpty(dictItem.getNames())) {
			dictItem.getNames().forEach(e -> updateDictItemLanguage(e, dictItemPO));
		}
	}

	private void updateDictItemLanguage(MultiLanguage e, DictItemPO dictItemPO) {
		DictItemLanguagePO updatePo = new DictItemLanguagePO();
		updatePo.setDictItemId(dictItemPO.getId());
		updatePo.setName(e.getName());
		updatePo.setDeleted(dictItemPO.getIsDeleted());
		updatePo.setLanguage(e.getLanguage());
		updatePo.setUpdateBy(SecurityUtils.getUserId());

		dictItemMultilanguageMapper.updateLanguage(updatePo);
	}

	@Override
	public List<DictItem> findActiveDictDataList() {
		List<DictItemPO> dictItemPOS = dictItemMapper.selectAllActived();
		if (CollUtil.isEmpty(dictItemPOS)) {
			return Collections.emptyList();
		}
		return DictTransformer.INSTANCE.toDictItemList(dictItemPOS);
	}

	@Override
	public List<DictItem> findActiveDictDataListByType(Integer dictId) {
		List<DictItemPO> dictItemPOS = dictItemMapper.selectActivedByType(dictId);
		if (CollUtil.isEmpty(dictItemPOS)) {
			return Collections.emptyList();
		}
		return DictTransformer.INSTANCE.toDictItemList(dictItemPOS);
	}

	@Override
	public Integer saveDict(Dict dict) {
		DictTypePO dictTypePO = DictTransformer.INSTANCE.toDictTypePO(dict);
		dictTypePO.setDeleted(0);
		dictTypeMapper.insert(dictTypePO);
		Integer id = dictTypePO.getId();
		List<MultiLanguage> languageList = dict.getNames();
		for (MultiLanguage multiLanguage : languageList) {
			DictTypeLanguagePO po = DictTransformer.INSTANCE.toDictTypeLanguagePO(multiLanguage, id);
			po.setDeleted(0);
			dictTypeLanguageMapper.insert(po);
		}
		return dictTypePO.getId();
	}

	@Override
	@Mapping(source = "dict.type", target = "DictTypePO.type")
	public void checkDuplicationExcludeById(Dict dict) {
		DictTypePO po = dictTypeMapper.selectDictByTypeExcludeId(dict.getType(), dict.getId().getId());
		if (Objects.nonNull(po)) {
			throw new ErrorCodeException(BizErrorCode.DICT_TYPE_FOUND, dict.getType());
		}
		for (MultiLanguage multiLanguage : dict.getNames()) {
			DictTypePO namePo = dictTypeMapper.selectDictByNameExcludeId(multiLanguage.getLanguage(), multiLanguage.getName(),
					dict.getId().getId());
			if (Objects.nonNull(namePo)) {
				throw new ErrorCodeException(BizErrorCode.DICT_TYPE_NAME_FOUND, multiLanguage.getName());
			}
		}
	}

	@Override
	public void deleteDictItemLanguage(Integer id) {
		dictItemMultilanguageMapper.deleteByDictItemId(id);
	}

	@Override
	public void checkDictItemDuplication(DictItem dictItem) {
		if (CollUtil.isNotEmpty(dictItem.getNames())) {
			for (MultiLanguage multiLanguage : dictItem.getNames()) {
				if (dictItemMapper.selectDictItemLanguageCount(multiLanguage, dictItem.getDictType())) {
					throw new ErrorCodeException(BizErrorCode.DICT_FOUND, multiLanguage.getLanguage());
				}
			}
		}
	}

	@Override
	public Integer saveDictItem(DictItem dictItem) {
		DictItemPO dictItemPO = DictTransformer.INSTANCE.toDictItemPO(dictItem);

		dictItemMapper.insert(dictItemPO);

		this.saveDictItemLanguage(dictItem, dictItemPO);

		return dictItemPO.getId();
	}

	private void saveDictItemLanguage(DictItem dictItem, DictItemPO dictItemPO) {
		if (CollUtil.isEmpty(dictItem.getNames())) {
			return;
		}
		for (MultiLanguage multiLanguage : dictItem.getNames()) {
			DictItemLanguagePO multilanguagePO = new DictItemLanguagePO();
			multilanguagePO.setDeleted(0);
			multilanguagePO.setDictItemId(dictItemPO.getId());
			multilanguagePO.setLanguage(multiLanguage.getLanguage());
			multilanguagePO.setName(multiLanguage.getName());

			dictItemMultilanguageMapper.insert(multilanguagePO);
		}
	}

	private String getMultiLanguageString(List<MultiLanguage> names) {
		return LangUtil.getMultiLanguageString(DictTransformer.INSTANCE.getMultiLanguageString(names));
	}

}
