package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.DefaultPO;

import lombok.Data;

/**
 * @description: 用户合同地址分配表
 * @author: doctor
 * @date: 2024/09/29
 * @version: 1.0.0
 */
@Data
@TableName("oum_contract_location_assign")
public class OumContractLocationAssignPO extends DefaultPO {

	/** 用户ID **/
	private Integer userId;

	/** 办公地点ID **/
	private Integer contractLocationId;

}
