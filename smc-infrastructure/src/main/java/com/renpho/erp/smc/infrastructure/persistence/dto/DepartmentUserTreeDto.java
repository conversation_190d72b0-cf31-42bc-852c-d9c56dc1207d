package com.renpho.erp.smc.infrastructure.persistence.dto;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class DepartmentUserTreeDto {

	/**
	 * 部门id
	 */
	private Integer id;

	/**
	 * 部门状态
	 */
	private Integer status;

	/**
	 * 上级部门
	 */
	private Integer parentId;

	/**
	 * 排序，默认 0
	 */
	private Integer sort;

	/**
	 * 部门名称
	 */
	private String name;

	/**
	 * 负责人名称
	 */
	private String managerName;

	/**
	 * 部门子级
	 */
	private List<Object> children;

	/**
	 * 部门用户
	 */
	private List<DepartmentUserSimpleDto> userList;

	public DepartmentUserTreeDto() {
	}

	public DepartmentUserTreeDto(List<DepartmentUserSimpleDto> userList) {
		this.userList = userList;
	}

}
