package com.renpho.erp.smc.infrastructure.persistence.dto;

import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * @description: 用户信息表
 * @author: doctor
 * @date: 2024/09/13
 * @version: 1.0.0
 */
@Data
public class OumUserQueryListDto {

	private Integer id;

	/** 用户工号 **/
	private String code;

	/** 名称 **/
	private String name;

	private String phoneNo;

	/** **/
	private Integer status;

	/** 邮箱 **/
	private String email;

	/** 用户性别，1 男、2 女、3 未知 **/
	private Integer gender;

	private Integer reportUserId;

	/** 全日制还是 **/
	private String type;

	@TableField(exist = false)
	private String deptIdStr;

	@TableField(exist = false)
	private String deptNameStr;

	@TableField(exist = false)
	private String deptLeaderIdStr;

	@TableField(exist = false)
	private String roleIdStr;

}
