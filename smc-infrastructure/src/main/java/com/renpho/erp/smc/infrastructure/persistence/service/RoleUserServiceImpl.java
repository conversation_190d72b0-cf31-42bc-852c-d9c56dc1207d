package com.renpho.erp.smc.infrastructure.persistence.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.smc.infrastructure.persistence.mapper.RoleUserMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.RoleUserPO;
import org.springframework.stereotype.Service;

/**
 * 角色用户持久层Service 场景：使用Mybatis-Plus的IService批量方法，例：IService#saveBatch()方法.
 *
 * <AUTHOR>
 */
@Service
public class RoleUserServiceImpl extends ServiceImpl<RoleUserMapper, RoleUserPO> {

}
