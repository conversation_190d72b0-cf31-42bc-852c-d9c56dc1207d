package com.renpho.erp.smc.infrastructure.persistence.Repository;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.renpho.erp.smc.domain.organizationmanagement.costCenterAssign.CostCenterAssign;
import com.renpho.erp.smc.domain.organizationmanagement.costCenterAssign.CostCenterAssignRepository;
import com.renpho.erp.smc.exception.BizErrorCode;
import com.renpho.erp.smc.infrastructure.persistence.mapper.CostCenterAssignMapper;
import com.renpho.erp.smc.infrastructure.persistence.mapper.OumUserInfoMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.CostCenterAssignPO;
import com.renpho.erp.smc.infrastructure.persistence.po.OumUserInfoPO;
import com.renpho.erp.smc.infrastructure.persistence.po.transformer.CostCenterAssignTransformer;
import com.renpho.karma.exception.ErrorCodeException;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/09/19 17:05
 */
@Repository
@RequiredArgsConstructor
public class CostCenterAssignRepositoryImpl implements CostCenterAssignRepository {

	private final CostCenterAssignMapper costCenterAssignMapper;

	private final OumUserInfoMapper oumUserInfoMapper;

	/**
	 * 检验名称是否重复
	 */
	@Override
	public void checkDuplication(Integer costCenterId, String effectiveDate, List<Integer> userIds) {
		List<CostCenterAssignPO> costCenterAssignPOList = this.costCenterAssignMapper.selectList(Wrappers.<CostCenterAssignPO> lambdaQuery()
			.eq(CostCenterAssignPO::getEfficetiveDate, Timestamp.valueOf(effectiveDate + " 00:00:00"))
			.in(CostCenterAssignPO::getUserId, userIds));
		if (costCenterAssignPOList.size() > 0) {
			List<Integer> userList = costCenterAssignPOList.stream().map(CostCenterAssignPO::getUserId).collect(Collectors.toList());
			List<String> userNameList = this.oumUserInfoMapper
				.selectList(Wrappers.<OumUserInfoPO> lambdaQuery().in(OumUserInfoPO::getId, userList))
				.stream()
				.map(OumUserInfoPO::getName)
				.collect(Collectors.toList());
			String userNameStr = String.join(",", userNameList);
			throw new ErrorCodeException(BizErrorCode.COST_CENTER_ASSIGN_FOUND, userNameStr);
		}
	}

	/**
	 * 保存成本中心分配list
	 */
	@Override
	public List<Integer> saveCostCenterAssignList(List<CostCenterAssign> costCenterAssignList) {
		List<Integer> ids = new ArrayList<>();
		for (CostCenterAssign costCenterAssign : costCenterAssignList) {
			CostCenterAssignPO costCenterAssignPO = CostCenterAssignTransformer.INSTANCE.toCostCenterAssignPO(costCenterAssign);
			this.costCenterAssignMapper.insert(costCenterAssignPO);
			ids.add(costCenterAssignPO.getId());
		}
		return ids;
	}

	/**
	 * 保存成本中心分配
	 */
	@Override
	public Integer saveCostCenterAssign(CostCenterAssign costCenterAssign) {

		CostCenterAssignPO costCenterAssignPO = CostCenterAssignTransformer.INSTANCE.toCostCenterAssignPO(costCenterAssign);
		this.costCenterAssignMapper.insert(costCenterAssignPO);
		return costCenterAssignPO.getId();
	}

	/**
	 * 检验名称是否重复（编辑）
	 */
	@Override
	public void checkUpdateDuplication(CostCenterAssign costCenterAssign) {
		CostCenterAssignPO currentPO = this.costCenterAssignMapper.selectById(costCenterAssign.getId().getId());
		costCenterAssign.setUserId(currentPO.getUserId());
		List<CostCenterAssignPO> costCenterAssignPOList = this.costCenterAssignMapper.selectList(Wrappers.<CostCenterAssignPO> lambdaQuery()
			.eq(CostCenterAssignPO::getEfficetiveDate, Timestamp.valueOf(costCenterAssign.getEfficetiveDate()))
			.eq(CostCenterAssignPO::getUserId, currentPO.getUserId())
			.ne(CostCenterAssignPO::getId, costCenterAssign.getId().getId()));
		if (costCenterAssignPOList.size() > 0) {
			List<Integer> userList = costCenterAssignPOList.stream().map(CostCenterAssignPO::getUserId).collect(Collectors.toList());
			List<String> userNameList = this.oumUserInfoMapper
				.selectList(Wrappers.<OumUserInfoPO> lambdaQuery().in(OumUserInfoPO::getId, userList))
				.stream()
				.map(OumUserInfoPO::getName)
				.collect(Collectors.toList());
			String userNameStr = String.join(",", userNameList);
			throw new ErrorCodeException(BizErrorCode.COST_CENTER_ASSIGN_FOUND, userNameStr);
		}
	}

	/**
	 * 编辑成本中心分配
	 */
	@Override
	public void updateCostCenterAssign(CostCenterAssign costCenterAssign) {
		CostCenterAssignPO po = new CostCenterAssignPO();
		po.setCostCenterId(costCenterAssign.getCostCenterId());
		po.setEfficetiveDate(costCenterAssign.getEfficetiveDate());
		po.setId(costCenterAssign.getId().getId());
		this.costCenterAssignMapper.updateById(po);
	}

	/**
	 * 查询是否不存在
	 */
	@Override
	public void findById(Integer id) {
		CostCenterAssignPO po = this.costCenterAssignMapper
			.selectOne(Wrappers.<CostCenterAssignPO> lambdaQuery().eq(CostCenterAssignPO::getId, id));
		if (Objects.isNull(po)) {
			throw new ErrorCodeException(BizErrorCode.COST_CENTER_ASSIGN_NOT_FOUND);
		}
	}

}
