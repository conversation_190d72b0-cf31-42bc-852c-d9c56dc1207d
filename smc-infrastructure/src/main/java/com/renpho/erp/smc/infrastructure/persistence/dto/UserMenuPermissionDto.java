package com.renpho.erp.smc.infrastructure.persistence.dto;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.renpho.erp.smc.infrastructure.persistence.po.DepartmentUserPO;
import com.renpho.erp.smc.infrastructure.persistence.po.MenuInfoPO;
import com.renpho.erp.smc.infrastructure.persistence.po.OumUserInfoPO;
import com.renpho.erp.smc.infrastructure.persistence.po.RoleDataPermissionPO;

import lombok.Data;

/**
 * 用户菜单数据权限dto.
 *
 * <AUTHOR>
 */
@Data
public class UserMenuPermissionDto {

	/**
	 * Map, key为用户id, value为角色id集合
	 */
	private Map<Integer, Set<Integer>> userRoleMap;

	/**
	 * Map, key为角色id, value为菜单id集合
	 */
	private Map<Integer, Set<Integer>> roleMenuMap;

	/**
	 * Map, key为角色id, value为数据权限列表
	 */
	private Map<Integer, MenuInfoPO> menuMap;

	/**
	 * Map, key为角色id, value为数据权限列表
	 */
	private Map<Integer, List<RoleDataPermissionPO>> roleDataPermissionMap;

	/**
	 * Map, key为部门id，value为子部门id（包含子子部门）
	 */
	private Map<Integer, Set<Integer>> departmentIdMap;

	/**
	 * 部门用户列表
	 */
	private List<DepartmentUserPO> departmentUserList;

	/**
	 * 直属领导用户列表
	 */
	private List<OumUserInfoPO> reportUserList;

}
