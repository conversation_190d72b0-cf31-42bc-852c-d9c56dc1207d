package com.renpho.erp.smc.infrastructure.persistence.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.PositionMultilanguagePO;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/09/12 14:15
 */
public interface PositionMultilanguageMapper extends BaseMapper<PositionMultilanguagePO> {

	default Map<Integer, String> queryMapByIdListLang(List<Integer> idList, String lang) {
		LambdaQueryWrapper<PositionMultilanguagePO> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(PositionMultilanguagePO::getLanguage, lang);
		queryWrapper.in(PositionMultilanguagePO::getPositionId, idList);
		List<PositionMultilanguagePO> officeLocationLanguagePOS = selectList(queryWrapper);
		return officeLocationLanguagePOS.stream()
			.collect(Collectors.toMap(PositionMultilanguagePO::getPositionId, PositionMultilanguagePO::getName, (v1, v2) -> v2));
	}

}
