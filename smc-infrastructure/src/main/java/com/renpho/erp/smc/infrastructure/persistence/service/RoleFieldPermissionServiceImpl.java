package com.renpho.erp.smc.infrastructure.persistence.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.smc.infrastructure.persistence.mapper.RoleFieldPermissionMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.RoleFieldPermissionPO;
import org.springframework.stereotype.Service;

/**
 * 角色字段脱敏权限持久层Service 场景：使用Mybatis-Plus的IService批量方法，例：IService#saveBatch()方法.
 *
 * <AUTHOR>
 */
@Service
public class RoleFieldPermissionServiceImpl extends ServiceImpl<RoleFieldPermissionMapper, RoleFieldPermissionPO> {

}
