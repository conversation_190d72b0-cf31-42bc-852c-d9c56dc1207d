package com.renpho.erp.smc.infrastructure.persistence.dto;

import lombok.Data;

/**
 * @description: 用户信息表
 * @author: doctor
 * @date: 2024/09/13
 * @version: 1.0.0
 */
@Data
public class OumUserSimpleInfoPO {

	private Integer id;

	/** 员工名称 */
	private String name;

	/** 员工号 */
	private String code;

	/** 是否离职 */
	private Integer status;

	private String statusName;

	/** 部门id */
	private int departId;

	/** 部门多语言名称 */
	private String departName;

}
