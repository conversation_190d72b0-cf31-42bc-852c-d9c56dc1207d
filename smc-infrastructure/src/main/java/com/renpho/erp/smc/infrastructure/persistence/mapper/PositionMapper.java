package com.renpho.erp.smc.infrastructure.persistence.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.renpho.erp.smc.infrastructure.persistence.po.PositionPO;

/**
 * 岗位Mapper.
 */
public interface PositionMapper extends BaseMapper<PositionPO> {

	List<PositionPO> selectByCondition(@Param("param") PositionPO positionPO, @Param("language") String language);

	Page<PositionPO> selectByConditionPage(IPage page, @Param("param") PositionPO positionPO, @Param("language") String language);

	Integer selectByConditionCount(@Param("param") PositionPO po);

	Page<PositionPO> page(IPage page, @Param("param") PositionPO positionPO, @Param("language") String language);

	List<PositionPO> selectNameList(String name, List<Integer> departmentIds, Integer status, String language);

	IPage<PositionPO> pagePositionUser(IPage<PositionPO> page, @Param("positionId") Integer positionId, @Param("language") String language);

	PositionPO selectDetailById(Integer id, String language);

}
