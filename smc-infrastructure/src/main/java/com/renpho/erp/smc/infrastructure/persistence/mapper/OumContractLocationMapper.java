package com.renpho.erp.smc.infrastructure.persistence.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.OumContractLocationPO;

import org.apache.ibatis.annotations.Select;

import feign.Param;

/**
 * 字典类型 Mapper.
 *
 * <AUTHOR>
 */
public interface OumContractLocationMapper extends BaseMapper<OumContractLocationPO> {

	@Select("""
			<script>
				select
					di.id,
					JSON_UNQUOTE(JSON_EXTRACT( di.LANGUAGE, '$."${language}".name' )) AS location,
					di.`language`,
					di.status,
					di.create_time,
					di.create_by,
					di.update_by,
					di.update_time,
					di.is_deleted
					from oum_contract_location di
					where di.is_deleted = 0 and di.status=1
				</script>
				""")
	List<OumContractLocationPO> selectContractList(@Param("language") String language);

	@Select("""
			<script>
				select
					di.id,
					JSON_UNQUOTE(JSON_EXTRACT( di.LANGUAGE, '$.*.name' )) AS location,
					di.`language`,
					di.status,
					di.create_time,
					di.create_by,
					di.update_by,
					di.update_time,
					di.is_deleted
					from oum_contract_location di
					where di.is_deleted = 0
				</script>
				""")
	List<OumContractLocationPO> selectContractNameList();

	@Select("""
			<script>
				select
					di.id,
					di.`language`,
					di.status,
					di.create_time,
					di.create_by,
					di.update_by,
					di.update_time,
					di.is_deleted
					from oum_contract_location di
					where di.is_deleted = 0
				</script>
				""")
	List<OumContractLocationPO> selectContractInfoList();

	@Select("""
			<script>
				select
					di.id,
					JSON_UNQUOTE(JSON_EXTRACT( di.LANGUAGE, '$."${language}".name' )) AS location,
					di.`language`,
					di.status,
					di.create_time,
					di.create_by,
					di.update_by,
					di.update_time,
					di.is_deleted
				from oum_contract_location di
				where di.is_deleted = 0
			         and  id in
				 <foreach collection="idList" item="item" index="index" open="(" close=")" separator=",">
					   #{item}
			   </foreach>
			</script>
			""")
	List<OumContractLocationPO> queryByIdLanguage(@Param("language") String language, @Param("idList") List<Integer> idList);

}
