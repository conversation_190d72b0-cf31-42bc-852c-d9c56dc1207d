package com.renpho.erp.smc.infrastructure.persistence.dto.notice;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 系统公告表
 *
 * <AUTHOR>
 * @date 2025/03/25
 */
@Data
public class NoticeIndexUserPageQuery implements Serializable {

	/**
	 * 通知类型 NoticeTypeEnum NOTICE(1, "通知公告"), GROUP_NEWS(2, "集团要闻");
	 */
	private Integer type;

	private Integer limit;

	@Serial
	private static final long serialVersionUID = 1L;

}