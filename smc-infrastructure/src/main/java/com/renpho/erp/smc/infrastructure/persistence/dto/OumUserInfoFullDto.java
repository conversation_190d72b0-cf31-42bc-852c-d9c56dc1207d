package com.renpho.erp.smc.infrastructure.persistence.dto;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.github.houbb.sensitive.annotation.Sensitive;
import com.renpho.erp.data.sensitive.SensitiveDynamicCondition;
import com.renpho.erp.data.sensitive.SensitiveDynamicStrategy;

import org.springframework.format.annotation.DateTimeFormat;

import lombok.Data;

@Data
public class OumUserInfoFullDto implements Serializable {

	/** 用户id */
	private Integer id;

	/** 用户工号 **/
	private String code;

	/** 用户工号 **/
	private String oldCode;

	/** 成本中心 */
	private Integer costcenterId;

	/** 成本中心名称 */
	private String costcenterName;

	/** 成本中心名称导出使用 */
	private String costcenterCode;

	/** 公司ID **/
	private Integer corporationId;

	/** 公司名称 */
	private String corporationName;

	/** 主部门ID **/
	private Integer departmentId;

	/** 主部门名称 */
	private String departmentName;

	/** 主部门名称 */
	private String departmentCode;

	/** 邮箱 **/
	private String email;

	/** 用户性别，1 男、2 女、3 未知 **/
	private String gender;

	private String genderName;

	/** 公积金号 **/
	private String housingFundNo;

	/** 用户姓名 **/
	private String name;

	/** 手机号码 **/
	@Sensitive(permission = "USER:PHONE", condition = SensitiveDynamicCondition.class, strategy = SensitiveDynamicStrategy.class)
	private String phoneNo;

	/** 岗位ID **/
	private Integer positionId;

	private String positionName;

	private String positionCode;

	/** 直属领导ID **/
	private Integer reportUserId;

	/** 直属领导ID */
	private String reportUserCode;

	/** 直属领导名称 */
	private String reportUserName;

	/** 办公室id */
	private Integer officeLocationId;

	/** 办公地点名称 */
	private String officeLocation;

	/** 离职时间 **/
	@DateTimeFormat(pattern = "yyyy/MM/dd")
	private LocalDateTime resignationTime;

	@DateTimeFormat(pattern = "yyyy/MM/dd")
	private LocalDateTime hireTime;

	/** 社保电脑号 **/
	private String socialInsuranceNo;

	/** 用户状态，1 在职，2 离职 **/
	private String status;

	/** 多语言名称 */
	private String statusName;

	/** 账号状态 */
	private String accountStatus;

	/** 账号状态名称多语言 */
	private String accountStatusName;

	/** 用户类型，1 全日制、2 非全日制、3 退休返聘、4 兼职、5 劳务派遣、6 劳务外包、7 实习生、8 服务外包、9自由职业者 **/
	private String type;

	/** 员工类型名称 */
	private String typeName;

	/** 钉钉id */
	private String unionid;

	/** 钉钉userid */
	private String dingUserid;

	/** 钉钉名称 */
	private String dingName;

	private LocalDateTime createTime;

}
