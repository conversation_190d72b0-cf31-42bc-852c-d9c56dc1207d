package com.renpho.erp.smc.infrastructure.persistence.dto.notice;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 系统公告可见范围表
 *
 * <AUTHOR>
 * @date 2025/03/26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NoticeVisibilityVo implements Serializable {

	/**
	 * 可见类型：1 部门、2 用户 VisibilityTypeEnum
	 */
	private Integer type;

	/**
	 * 可见对象ID. 用户id或者 部门id
	 */
	private Integer visibilityId;

	/**
	 * name
	 */
	private String name;

	private String userCode;

	@Serial
	private static final long serialVersionUID = 1L;

}