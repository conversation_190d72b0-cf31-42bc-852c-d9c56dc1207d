package com.renpho.erp.smc.infrastructure.persistence.dto.index;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 用户菜单配置表
 *
 * <AUTHOR>
 * @date 2025/03/26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserMenuSettingVo implements Serializable {

	/**
	 * 用户ID
	 */
	private Integer userId;

	/**
	 * 菜单Id
	 */
	private Integer menuId;

	private String menuName;

	/**
	 * 路径
	 */
	private String path;

	/**
	 * 菜单状态，0启用 1禁用
	 */
	private Integer menuStatus;

	@Serial
	private static final long serialVersionUID = 1L;

}