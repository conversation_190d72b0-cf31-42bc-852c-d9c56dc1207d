package com.renpho.erp.smc.infrastructure.constants;

import com.renpho.karma.i18n.I18nMessageKit;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 更新
 *
 * <AUTHOR>
 */
public class UserImportTreadLocal {

	/** msg */
	private static final ThreadLocal<String> ERROR_MSG_LOCAL = new ThreadLocal<>();

	public static void setErrorMsgCode(String code) {
		setErrorMsg(I18nMessageKit.getMessage(code));
	}

	public static void setErrorMsg(String msg) {
		if (Objects.isNull(ERROR_MSG_LOCAL.get()) || ERROR_MSG_LOCAL.get().isEmpty()) {
			ERROR_MSG_LOCAL.set(msg);
			return;
		}
		// 已经存在的错误信息跳过
		String message = ERROR_MSG_LOCAL.get();
		if (StringUtils.contains(message, msg)) {
			return;
		}
		ERROR_MSG_LOCAL.set(message + "\n" + msg);
	}

	public static void remove() {
		ERROR_MSG_LOCAL.set("");
	}

	public static String get() {
		return ERROR_MSG_LOCAL.get();
	}

}
