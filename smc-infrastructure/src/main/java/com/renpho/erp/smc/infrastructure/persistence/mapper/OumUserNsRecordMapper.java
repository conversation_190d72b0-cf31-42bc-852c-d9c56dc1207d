package com.renpho.erp.smc.infrastructure.persistence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.renpho.erp.smc.infrastructure.persistence.dto.SelectOumUserNsRecordInfoDto;
import com.renpho.erp.smc.infrastructure.persistence.po.OumUserNsRecordPO;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 参数配置 Mapper.
 *
 * <AUTHOR>
 */
public interface OumUserNsRecordMapper extends BaseMapper<OumUserNsRecordPO> {

	@Delete("""
			DELETE from oum_user_ns_record where user_id = #{uid}
			""")
	void deleteByUid(int uid);

	List<SelectOumUserNsRecordInfoDto> selectOumUserNsRecordInfo(@Param("recordId") Long recordId);

}
