package com.renpho.erp.smc.infrastructure.persistence.po;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 钉钉日程
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DingDingScheduleQuery implements Serializable {

	/**
	 * 开始时间
	 */
	@NotNull
	private LocalDateTime startTime;

	/**
	 * 结束时间
	 */
	@NotNull
	private LocalDateTime endTime;

	@Serial
	private static final long serialVersionUID = 1L;

}