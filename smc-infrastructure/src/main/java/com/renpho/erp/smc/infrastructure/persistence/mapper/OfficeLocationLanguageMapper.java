package com.renpho.erp.smc.infrastructure.persistence.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.OfficeLocationLanguagePO;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 部门多语言 Mapper.
 */
public interface OfficeLocationLanguageMapper extends BaseMapper<OfficeLocationLanguagePO> {

	default Map<Integer, String> queryMapByIdListLang(List<Integer> idList, String lang) {
		LambdaQueryWrapper<OfficeLocationLanguagePO> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(OfficeLocationLanguagePO::getLanguage, lang);
		queryWrapper.in(OfficeLocationLanguagePO::getOfficeLocationId, idList);
		List<OfficeLocationLanguagePO> officeLocationLanguagePOS = selectList(queryWrapper);
		return officeLocationLanguagePOS.stream()
			.collect(Collectors.toMap(OfficeLocationLanguagePO::getOfficeLocationId, OfficeLocationLanguagePO::getName, (v1, v2) -> v2));
	}

}
