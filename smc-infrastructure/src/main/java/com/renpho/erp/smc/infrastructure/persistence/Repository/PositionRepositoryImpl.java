package com.renpho.erp.smc.infrastructure.persistence.Repository;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.renpho.erp.smc.domain.organizationmanagement.position.Position;
import com.renpho.erp.smc.domain.organizationmanagement.position.PositionRepository;
import com.renpho.erp.smc.domain.systemsetting.MultiLanguage;
import com.renpho.erp.smc.exception.BizErrorCode;
import com.renpho.erp.smc.infrastructure.persistence.mapper.OumUserInfoMapper;
import com.renpho.erp.smc.infrastructure.persistence.mapper.PositionMapper;
import com.renpho.erp.smc.infrastructure.persistence.mapper.PositionMultilanguageMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.OumUserInfoPO;
import com.renpho.erp.smc.infrastructure.persistence.po.PositionMultilanguagePO;
import com.renpho.erp.smc.infrastructure.persistence.po.PositionPO;
import com.renpho.erp.smc.infrastructure.persistence.po.transformer.PositionTransformer;
import com.renpho.karma.exception.ErrorCodeException;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/09/12 14:12
 */
@Repository
@RequiredArgsConstructor
public class PositionRepositoryImpl implements PositionRepository {

	private final PositionMapper positionMapper;

	private final PositionMultilanguageMapper positionMultilanguageMapper;

	private final OumUserInfoMapper oumUserInfoMapper;

	@Override
	public void checkDuplication(Position position) {
		for (MultiLanguage multiLanguage : position.getNames()) {
			String lang = multiLanguage.getLanguage();
			String name = multiLanguage.getName();
			PositionMultilanguagePO langPO = this.positionMultilanguageMapper.selectOne(Wrappers.<PositionMultilanguagePO> lambdaQuery()
				.eq(PositionMultilanguagePO::getLanguage, lang)
				.eq(PositionMultilanguagePO::getName, name));
			if (Objects.nonNull(langPO)) {
				throw new ErrorCodeException(BizErrorCode.POSTION_FOUND, multiLanguage.getName());
			}
		}
	}

	@Override
	public void checkDuplicationExcludeById(Position position) {
		for (MultiLanguage multiLanguage : position.getNames()) {
			String lang = multiLanguage.getLanguage();
			String name = multiLanguage.getName();
			PositionMultilanguagePO langPO = this.positionMultilanguageMapper.selectOne(Wrappers.<PositionMultilanguagePO> lambdaQuery()
				.eq(PositionMultilanguagePO::getLanguage, lang)
				.eq(PositionMultilanguagePO::getName, name)
				.ne(PositionMultilanguagePO::getPositionId, position.getId().getId()));
			if (Objects.nonNull(langPO)) {
				throw new ErrorCodeException(BizErrorCode.POSTION_FOUND, multiLanguage.getName());
			}
		}
	}

	private String generateCode(Integer id) {
		return "PID" + String.format("%06d", id);
	}

	@Override
	public Integer savePosition(Position position) {
		PositionPO positionPO = PositionTransformer.INSTANCE.toPositionPO(position);
		this.positionMapper.insert(positionPO);
		Integer positionId = positionPO.getId();
		List<PositionMultilanguagePO> positionMultilanguagePOList = PositionTransformer.INSTANCE
			.toPositionMultilanguagePOList(position.getNames(), positionId);
		positionPO.setCode(this.generateCode(positionId));
		this.positionMapper.updateById(positionPO);
		for (PositionMultilanguagePO positionMultilanguagePO : positionMultilanguagePOList) {
			this.positionMultilanguageMapper.insert(positionMultilanguagePO);
		}
		return positionId;
	}

	@Override
	public Position findById(Integer id) {
		PositionPO positionPO = this.positionMapper.selectById(id);
		if (Objects.isNull(positionPO)) {
			throw new ErrorCodeException(BizErrorCode.POSTION_NOT_FOUND);
		}
		return PositionTransformer.INSTANCE.toPosition(positionPO);
	}

	@Override
	public void updatePosition(Position position) {
		PositionPO positionPO = PositionTransformer.INSTANCE.toPositionPO(position);
		this.positionMapper.updateById(positionPO);
		List<MultiLanguage> names = position.getNames();
		for (MultiLanguage multiLanguage : names) {
			PositionMultilanguagePO positionMultilanguagePO = PositionTransformer.INSTANCE.toPositionMultilanguagePO(multiLanguage,
					positionPO.getId());
			PositionMultilanguagePO currentPO = this.positionMultilanguageMapper.selectOne(Wrappers.<PositionMultilanguagePO> lambdaQuery()
				.eq(PositionMultilanguagePO::getPositionId, positionPO.getId())
				.eq(PositionMultilanguagePO::getLanguage, multiLanguage.getLanguage()));
			if (Objects.isNull(currentPO)) {
				this.positionMultilanguageMapper.insert(positionMultilanguagePO);
			}
			else {
				positionMultilanguagePO.setId(currentPO.getId());
				this.positionMultilanguageMapper.updateById(positionMultilanguagePO);
			}
		}
		List<String> languageExistList = names.stream().map(MultiLanguage::getLanguage).collect(Collectors.toList());
		LambdaQueryWrapper<PositionMultilanguagePO> deleteQueryWrapper = Wrappers.lambdaQuery(PositionMultilanguagePO.class)
			.eq(PositionMultilanguagePO::getPositionId, positionPO.getId())
			.notIn(PositionMultilanguagePO::getLanguage, languageExistList);
		this.positionMultilanguageMapper.delete(deleteQueryWrapper);
	}

	@Override
	public void changeStatusPosition(Integer id, Integer status) {
		PositionPO po = new PositionPO();
		po.setId(id);
		po.setStatus(status);
		this.positionMapper.updateById(po);
	}

	@Override
	public Integer selectMemberCountByPositionId(Integer id) {
		return this.oumUserInfoMapper
			.selectCount(Wrappers.<OumUserInfoPO> lambdaQuery().eq(OumUserInfoPO::getPositionId, id).eq(OumUserInfoPO::getStatus, 1))
			.intValue();
	}

}
