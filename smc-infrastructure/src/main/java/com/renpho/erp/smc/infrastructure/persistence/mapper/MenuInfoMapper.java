package com.renpho.erp.smc.infrastructure.persistence.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.renpho.erp.smc.domain.systemsetting.MultiLanguage;
import com.renpho.erp.smc.infrastructure.persistence.po.MenuInfoPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 参数配置 Mapper.
 *
 * <AUTHOR>
 */
public interface MenuInfoMapper extends BaseMapper<MenuInfoPO> {

	List<MenuInfoPO> selectMenuList(List<String> systemModules, String name, String language, Integer status);

	List<MenuInfoPO> selectMenuById(Integer id);

	void updateMenu(MenuInfoPO toMenuInfoPO);

	List<MenuInfoPO> selectMenuTreeByUserId(Integer userId);

	List<MenuInfoPO> selectTypeMenuList();

	MenuInfoPO selectMenuByParentId(Integer parentId);

	boolean selectMenuLanguageCount(@Param("item") MultiLanguage multiLanguage);

	/**
	 * 菜单列表
	 * @param idSet 菜单id集合
	 * @param menuTypeSet 菜单类型集合
	 * @return List
	 */
	default List<MenuInfoPO> selectBatchIdsAndMenuType(Set<Integer> idSet, Set<String> menuTypeSet) {
		return this.selectList(new LambdaQueryWrapper<MenuInfoPO>().in(MenuInfoPO::getId, idSet).in(MenuInfoPO::getMenuType, menuTypeSet));
	}

	/**
	 * 菜单列表
	 * @param menuTypeSet 菜单类型集合
	 * @return List
	 */
	default List<MenuInfoPO> selectByMenuTypeSet(Set<String> menuTypeSet) {
		return this.selectList(new LambdaQueryWrapper<MenuInfoPO>().in(MenuInfoPO::getMenuType, menuTypeSet));
	}

	/**
	 * 菜单类型
	 * @param menuType 菜单类型
	 * @param perms 菜单标识符
	 * @return MenuInfoPO
	 */
	default MenuInfoPO getOneByPermsAndMenuType(String menuType, String perms) {
		return this.selectOne(new LambdaQueryWrapper<MenuInfoPO>().eq(MenuInfoPO::getMenuType, menuType).eq(MenuInfoPO::getPerms, perms));
	}

}
