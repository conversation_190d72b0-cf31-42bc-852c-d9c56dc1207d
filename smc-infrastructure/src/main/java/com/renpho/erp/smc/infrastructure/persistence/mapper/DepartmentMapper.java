package com.renpho.erp.smc.infrastructure.persistence.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.renpho.erp.smc.infrastructure.persistence.dto.baseinfo.DeptInfoExcelDto;
import com.renpho.erp.smc.infrastructure.persistence.po.DepartmentPO;

import org.apache.ibatis.annotations.Select;

/**
 * 部门 Mapper.
 */
public interface DepartmentMapper extends BaseMapper<DepartmentPO> {

	DepartmentPO selectDetailById(Integer id, String language);

	// int deleteByPrimaryKey(Integer id);

	// int insert(DepartmentPO record);

	// int insertSelective(DepartmentPO record);

	// DepartmentPO selectByPrimaryKey(Integer id);

	// int updateByPrimaryKeySelective(DepartmentPO record);

	// int updateByPrimaryKey(DepartmentPO record);

	/**
	 * 部门列表
	 * @return 只返回id,parentId和managerId
	 */
	default List<DepartmentPO> listIdAndParentIdAndManagerId() {
		return this.selectList(
				new LambdaQueryWrapper<DepartmentPO>().select(DepartmentPO::getId, DepartmentPO::getParentId, DepartmentPO::getManagerId));
	}

	Integer getParentDepartmentStatus(Integer id);

	List<DepartmentPO> selectNameList(Integer status, String language);

	List<DepartmentPO> selectDepartmentNameUserList(Integer status, String language);

	List<DepartmentPO> exportDepartmentList(Integer status, String language);

	List<DepartmentPO> selectNoHierarchicalList(Integer status, String language);

	void updateAllById(DepartmentPO departmentPO);

	List<DepartmentPO> selectByIdList(List<Integer> deptIdList, Integer status, String language);

	@Select("""
			SELECT
				c.id,
				l.NAME,
				c.CODE,
				c.status,
				u.NAME AS managerName,
				u.CODE as userCode,
				p.NAME AS parentName,
				p.CODE AS parentCode
			FROM
				oum_department c
				LEFT JOIN ( SELECT d.id, d.CODE, l.`name`, `language` FROM oum_department d LEFT JOIN oum_department_language l ON d.id = l.department_id ) p ON c.parent_id = p.id
				LEFT JOIN oum_user_info u ON u.id = c.manager_id
				LEFT JOIN oum_department_language l ON c.id = l.department_id
				where  c.is_deleted = 0 and c.status = 1 and ( p.language IS NULL OR p.`language` = #{language} ) AND l.`language` = #{language}
			""")
	List<DeptInfoExcelDto> selectDepatDto(String language);

}
