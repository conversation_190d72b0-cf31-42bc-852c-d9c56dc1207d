package com.renpho.erp.smc.infrastructure.persistence.dto.index;

import com.fhs.core.trans.vo.VO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 系统公告表
 *
 * <AUTHOR>
 * @date 2025/03/25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DepartmentUserItemVo implements Serializable, VO {

	/**
	 * 部门id或者用户id
	 */
	private Integer id;

	/**
	 * 名称、支持中英文
	 */
	private String name;

	/**
	 * 可见类型：1 部门、2 用户 VisibilityTypeEnum
	 */
	private Integer type;

	@Serial
	private static final long serialVersionUID = 1L;

}