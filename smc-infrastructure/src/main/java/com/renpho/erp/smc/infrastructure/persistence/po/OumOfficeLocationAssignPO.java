package com.renpho.erp.smc.infrastructure.persistence.po;

import java.time.LocalDateTime;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.AlterationPO;

import lombok.Data;

/**
 * @description: 用户办公地点分配表
 * @author: doctor
 * @date: 2024/09/18
 * @version: 1.0.0
 */
@Data
@TableName("oum_office_location_assign")
public class OumOfficeLocationAssignPO extends AlterationPO<Integer, Integer, LocalDateTime, Integer> {

	// private static final long serialVersionUID = 1L;

	// @TableId(type = IdType.AUTO)
	// private Integer id;

	/** 用户ID **/
	private Integer userId;

	/** 办公地点ID **/
	private Integer officeLocationId;

	/** 生效日期 **/
	private LocalDateTime effectiveDate;

	/** 创建人ID **/
	// private Integer createBy;

	/** 创建时间 **/
	// private LocalDateTime createTime;

	/**
	 * 更新人
	 */
	// private Integer updateBy;

	/**
	 * 更新时间
	 */
	// private LocalDateTime updateTime;

	/** 是否删除：0 否、1 是；默认 0 **/
	// private Integer isDeleted;

	@TableField(exist = false)
	private List<Integer> userIds;

	@TableField(exist = false)
	private String language;

	@TableField(exist = false)
	private String officeLocationName;

	@TableField(exist = false)
	private String userName;

	@TableField(exist = false)
	private Integer departmentId;

	@TableField(exist = false)
	private String departmentName;

	@TableField(exist = false)
	private Integer positionId;

	@TableField(exist = false)
	private String positionName;

	@TableField(exist = false)
	private String employeeId;

	@TableField(exist = false)
	private Integer userStatus;

	@TableField(exist = false)
	private List<Integer> departmentIds;

	@TableField(exist = false)
	private List<Integer> officeLocationIds;

	@TableField(exist = false)
	private String startEffectiveDate;

	@TableField(exist = false)
	private String endEffectiveDate;

	/*
	 * @TableField(exist = false) private Integer pageNum;
	 */

	/*
	 * @TableField(exist = false) private Integer pageSize;
	 */

}
