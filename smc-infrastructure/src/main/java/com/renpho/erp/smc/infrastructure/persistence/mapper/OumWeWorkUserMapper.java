package com.renpho.erp.smc.infrastructure.persistence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.OumWeWorkUser;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 参数配置 Mapper.
 *
 * <AUTHOR>
 */
public interface OumWeWorkUserMapper extends BaseMapper<OumWeWorkUser> {

	@Update("""
			UPDATE oum_wework_user SET sys_user_id = #{userId} WHERE userid = #{dingUserid}
			""")
	void updateDingUserId(@Param("dingUserid") String dingUserid, @Param("userId") long userId);

	@Update("""

			        UPDATE oum_wework_user SET sys_user_id = NULL WHERE sys_user_id = #{sysUserId}
			""")
	void unbindByThirdUserId(@Param("sysUserId") long sysUserId);

	List<OumWeWorkUser> selectNotUserId(@Param("thirdUserId") String thirdUserId);

}
