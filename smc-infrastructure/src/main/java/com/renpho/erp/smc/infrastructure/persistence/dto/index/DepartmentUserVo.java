package com.renpho.erp.smc.infrastructure.persistence.dto.index;

import com.fhs.core.trans.vo.VO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 系统公告表
 *
 * <AUTHOR>
 * @date 2025/03/25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class DepartmentUserVo implements Serializable, VO {

	/**
	 * 部门id
	 */
	private Integer id;

	/**
	 * 名称、支持中英文
	 */
	private String name;

	/**
	 * 用户工号
	 */
	private String userCode;

	/**
	 * 可见类型：1 部门、2 用户 VisibilityTypeEnum
	 */
	private Integer type;

	/**
	 * 部门或人员信息
	 */
	private List<DepartmentUserVo> childDepartmentUser;

	/**
	 * 部门和状态
	 */
	private Integer status;

	@Serial
	private static final long serialVersionUID = 1L;

}