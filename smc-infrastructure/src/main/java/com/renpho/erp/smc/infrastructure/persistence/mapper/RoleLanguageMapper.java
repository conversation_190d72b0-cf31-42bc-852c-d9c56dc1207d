package com.renpho.erp.smc.infrastructure.persistence.mapper;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.context.i18n.LocaleContextHolder;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.renpho.erp.smc.domain.systemsetting.MultiLanguage;
import com.renpho.erp.smc.infrastructure.persistence.po.RoleLanguagePO;
import com.renpho.erp.smc.infrastructure.persistence.po.RolePO;

/**
 * 角色多语言 Mapper.
 */
public interface RoleLanguageMapper extends BaseMapper<RoleLanguagePO> {

	default List<Integer> listByNameLike(String name) {
		List<RoleLanguagePO> roleLanguagePOS = selectList(Wrappers.<RoleLanguagePO> lambdaQuery()
			.like(RoleLanguagePO::getName, name)
			.eq(RoleLanguagePO::getLanguage, LocaleContextHolder.getLocale().toLanguageTag()));
		if (CollectionUtils.isNotEmpty(roleLanguagePOS)) {
			return roleLanguagePOS.stream().map(RoleLanguagePO::getRoleId).collect(Collectors.toList());
		}
		else {
			return new ArrayList<>();
		}
	}

	default List<RolePO> setRoleLanguageList(List<RolePO> roleList) {
		if (CollectionUtils.isEmpty(roleList)) {
			return roleList;
		}

		List<Integer> roleIdList = roleList.stream().map(t -> t.getId()).collect(Collectors.toList());
		List<RoleLanguagePO> roleLanguagePOS = this
			.selectList(Wrappers.<RoleLanguagePO> lambdaQuery().in(RoleLanguagePO::getRoleId, roleIdList));
		Map<Integer, List<RoleLanguagePO>> roleLanguageMap = roleLanguagePOS.stream()
			.collect(Collectors.groupingBy(RoleLanguagePO::getRoleId));
		for (RolePO rolePo : roleList) {
			Integer roleId = rolePo.getId();
			if (roleLanguageMap.containsKey(roleId)) {
				List<RoleLanguagePO> languagePOS = roleLanguageMap.get(roleId);
				// 调用transmulti方法转换并设置names字段
				rolePo.setNames(transMutiLanguageList(languagePOS));
			}
		}
		return roleList;
	}

	default MultiLanguage transMutiLanguage(RoleLanguagePO roleLanguagePO) {
		MultiLanguage multiLanguage = new MultiLanguage();
		multiLanguage.setLanguage(roleLanguagePO.getLanguage());
		multiLanguage.setName(roleLanguagePO.getName());
		return multiLanguage;
	}

	default List<MultiLanguage> transMutiLanguageList(List<RoleLanguagePO> roleLanguagePOs) {
		ArrayList<MultiLanguage> multiLanguages = new ArrayList<>();
		for (RoleLanguagePO roleLanguagePO : roleLanguagePOs) {
			multiLanguages.add(transMutiLanguage(roleLanguagePO));
		}
		return multiLanguages;
	}

}
