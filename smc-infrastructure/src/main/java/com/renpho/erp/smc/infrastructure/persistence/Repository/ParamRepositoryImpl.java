package com.renpho.erp.smc.infrastructure.persistence.Repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.renpho.erp.smc.domain.systemsetting.param.ParamConfig;
import com.renpho.erp.smc.domain.systemsetting.param.ParamRepository;
import com.renpho.erp.smc.domain.systemsetting.param.ParamStatus;
import com.renpho.erp.smc.exception.BizErrorCode;
import com.renpho.erp.smc.infrastructure.persistence.mapper.ParamConfigMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.ParamConfigPO;
import com.renpho.erp.smc.infrastructure.persistence.po.transformer.ParamConfigTransformer;
import com.renpho.karma.exception.ErrorCodeException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;

/**
 * 参数仓促实现.
 *
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class ParamRepositoryImpl implements ParamRepository {

	private final ParamConfigMapper paramConfigMapper;

	@Override
	public Integer save(ParamConfig paramConfig) {
		ParamConfigPO po = ParamConfigTransformer.INSTANCE.toParamConfigPO(paramConfig);
		paramConfigMapper.insert(po);
		Integer id = po.getId();
		return id;
	}

	@Override
	public void checkDuplication(ParamConfig paramConfig) {
		// 需要告知是键名还是名称重复，所以分两次
		ParamConfigPO poByKey = paramConfigMapper.selectOne(
				Wrappers.<ParamConfigPO> lambdaQuery().select(ParamConfigPO::getId).eq(ParamConfigPO::getParamKey, paramConfig.getKey()));
		ParamConfigPO poByName = paramConfigMapper.selectOne(
				Wrappers.<ParamConfigPO> lambdaQuery().select(ParamConfigPO::getId).eq(ParamConfigPO::getParamName, paramConfig.getName()));
		if (Objects.nonNull(poByKey)) {
			throw new ErrorCodeException(BizErrorCode.PARAM_KEY_FOUND, paramConfig.getKey());
		}
		else if (Objects.nonNull(poByName)) {
			throw new ErrorCodeException(BizErrorCode.PARAM_FOUND, paramConfig.getName());
		}
	}

	@Override
	public void checkDuplicationExcludeById(ParamConfig paramConfig) {
		// 需要告知是键名还是名称重复，所以分两次
		ParamConfigPO poByKey = paramConfigMapper.selectOne(Wrappers.<ParamConfigPO> lambdaQuery()
			.select(ParamConfigPO::getId)
			.eq(ParamConfigPO::getParamKey, paramConfig.getKey())
			.ne(ParamConfigPO::getId, paramConfig.getId().getId()));
		ParamConfigPO poByName = paramConfigMapper.selectOne(Wrappers.<ParamConfigPO> lambdaQuery()
			.select(ParamConfigPO::getId)
			.eq(ParamConfigPO::getParamName, paramConfig.getName())
			.ne(ParamConfigPO::getId, paramConfig.getId().getId()));
		if (Objects.nonNull(poByKey)) {
			throw new ErrorCodeException(BizErrorCode.PARAM_KEY_FOUND, paramConfig.getKey());
		}
		else if (Objects.nonNull(poByName)) {
			throw new ErrorCodeException(BizErrorCode.PARAM_FOUND, paramConfig.getName());
		}
	}

	@Override
	public ParamConfig findByID(Integer id) {
		ParamConfigPO po = paramConfigMapper.selectOne(Wrappers.<ParamConfigPO> lambdaQuery().eq(ParamConfigPO::getId, id));
		if (Objects.isNull(po)) {
			throw new ErrorCodeException(BizErrorCode.PARAM_NOT_FOUND);
		}
		ParamConfig config = ParamConfigTransformer.INSTANCE.toParamConfig(po);
		return config;
	}

	@Override
	public List<ParamConfig> findActiveParamConfigList() {
		List<ParamConfigPO> paramConfifPOS = paramConfigMapper
			.selectList(Wrappers.<ParamConfigPO> lambdaQuery().eq(ParamConfigPO::getStatus, ParamStatus.ACTIVE.getValue()));
		List<ParamConfig> paramConfigList = ParamConfigTransformer.INSTANCE.toParamConfigList(paramConfifPOS);
		return paramConfigList;
	}

	@Override
	public void changeStatusParamConfig(Integer id, Integer status) {
		ParamConfigPO po = new ParamConfigPO();
		po.setId(id);
		po.setStatus(status);
		paramConfigMapper.updateById(po);
	}

	@Override
	public void update(ParamConfig paramConfig) {
		paramConfigMapper.updateById(ParamConfigTransformer.INSTANCE.toParamConfigPO(paramConfig));
	}

}
