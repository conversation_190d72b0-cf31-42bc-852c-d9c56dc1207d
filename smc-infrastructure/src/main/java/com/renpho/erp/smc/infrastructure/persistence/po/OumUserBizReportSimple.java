package com.renpho.erp.smc.infrastructure.persistence.po;

import lombok.Data;

/**
 * @description: 用户业务线汇报表
 * @author: doctor
 * @date: 2024/09/18
 * @version: 1.0.0
 */
@Data
public class OumUserBizReportSimple {

	private static final long serialVersionUID = 1L;

	private Integer id;

	/** 用户ID **/
	private Integer userId;

	/** 汇报类型 **/
	private String reportType;

	/** 业务编号 **/
	private Integer bizValue;

	private String bizName;

	/** 汇报用户ID **/
	private Integer reportUserId;

	private String reportUserName;

	private String reportUserCode;

}
