package com.renpho.erp.smc.infrastructure.persistence.mapper;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.renpho.erp.data.permission.aop.annotation.DataPermission;
import com.renpho.erp.smc.domain.oumuser.OumUserInfoQuery;
import com.renpho.erp.smc.domain.oumuser.OumUserListQuery;
import com.renpho.erp.smc.infrastructure.persistence.dto.*;
import com.renpho.erp.smc.infrastructure.persistence.po.CostCenterAssignPO;
import com.renpho.erp.smc.infrastructure.persistence.po.OumUserBizReportSimple;
import com.renpho.erp.smc.infrastructure.persistence.po.OumUserInfoPO;

/**
 * 参数配置 Mapper.
 *
 * <AUTHOR>
 */
public interface OumUserInfoMapper extends BaseMapper<OumUserInfoPO> {

	@Select("""
			select cast( SUBSTRING(code, 3, 8) as UNSIGNED) + 1000000 as idcode
			    from oum_user_info ORDER BY idcode desc limit 1
			""")
	int getLastCodeNum();

	@Select("""
			select u.id, u.name, u.code, u.status
			    from oum_user_info u
				  where u.is_deleted = 0
			""")
	@MapKey("code")
	Map<String, OumUserSimpleInfoPO> selectAllSimpleList();

	@Select("""
			<script>
				select u.id as id, u.name, u.code, u.status
					from oum_user_info u
					where u.is_deleted = 0
					and u.id in
					<foreach collection="userIds" item="userId" open="(" separator="," close=")">
						#{userId}
					</foreach>
			 </script>
			""")
	@MapKey("id")
	Map<Integer, OumUserSimpleInfoPO> selectUserIdMap(List<Integer> userIds);

	@Select("""
			select u.id, u.name, u.code, u.report_user_id, u.status
			    from oum_user_info u
				  where u.is_deleted = 0
			""")
	List<OumUserInfoPO> getAllSpimpleList();

	@Select("""
			<script>
				select u.id, u.name, u.code, u.status,
					dl.name as departName,
					IF(a.status = 1, 3 , a.status)  as acc_status,
					od.department_id as departId
					from oum_user_info u
					LEFT JOIN oum_account a ON u.id = a.user_id
					LEFT JOIN oum_department_user od ON u.id = od.user_id
			        LEFT JOIN oum_department_language dl on dl.department_id = od.department_id
				    where u.is_deleted = 0
					and od.is_primary = 1
					and od.is_deleted = 0
					and dl.is_deleted = 0
					 <if test="param.name != null and param.name !='' ">
				        and ( u.name like concat('%', #{param.name}, '%') or u.code like concat('%', #{param.name}, '%')  )
				     </if>
					 <if test="param.status != null "  >
			             and u.`status` = #{param.status}
			        </if>
					<if test="param.language != null and param.language !='' ">
			              and dl.language = #{param.language}
			        </if>
					<if test="param.departmentId != null and param.departmentId !=''">
							and od.department_id = #{param.departmentId}
					</if>
					<if test="param.userIdList !=null and param.userIdList.size>0">
							and u.id in
								<foreach collection="param.userIdList" item="userId" open="(" separator="," close=")">
								 #{userId}
								</foreach>
					</if>
					order by u.status desc, acc_status desc
			</script>
				""")
	List<OumUserSimpleInfoPO> selectUserListByName(@Param("param") QueryOumUserDto param);

	@Select("""
			<script>
				select distinct u.id, u.name, u.code, u.status
					from oum_user_info u
			        LEFT JOIN oum_role_user ruu on u.id = ruu.user_id
					LEFT JOIN oum_role r on r.id = ruu.role_id
				    where u.is_deleted = 0 and ruu.is_deleted = 0
					<if test="param.status != null"  >
			             and u.`status` = #{param.status}
			        </if>
			        <if test="param.roleLabel != null and param.roleLabel !=''">
			              and FIND_IN_SET(#{param.roleLabel},r.`label` )
			        </if>
			        <if test="param.userIdList != null and param.userIdList.size > 0 ">
			        	and u.id in
			        	<foreach item="id" collection="param.userIdList" open="(" separator="," close=")">
			                 #{id}
			             </foreach>
			        </if>
			        order by u.status desc
			</script>
				""")
	List<OumUserSimpleInfoPO> selectUserListByLable(@Param("param") QueryOumUserDto param);

	@Select("""
			<script>
					SELECT
						u.id,
						u.name,
						u.code,
						u.old_code,
						u.phone_no,
						u.contract_id,
						JSON_UNQUOTE(JSON_EXTRACT( c.LANGUAGE, '$."${param.language}".name' )) AS contractName,
						ca.cost_center_id as costcenterId,
						u.corporation_id,
						od.department_id AS departmentId,
						dl.NAME AS departmentName,
						odt.code AS departmentCode,
						u.STATUS,
						a.`status` AS accountStatus,
						u.type,
						u.gender,
						u.social_insurance_no,
						u.housing_fund_no,
						u.email,
						u.report_user_id,
						rpu.`name` AS reportUserName,
						rpu.code AS reportUserCode,
						u.hire_time AS hireTime,
						u.resignation_time,
						l.`name` AS positionName,
						p.code as positionCode,
						l.position_id AS positionId,
						ls.office_location_id,
						ol.name as officeLocation,
						du.unionid,
						du.`name` as dingName,
						du.userid as dingUserid,
						u.create_time AS createTime,
						u.update_time AS updateTime
					FROM
						oum_user_info u
						LEFT JOIN oum_account a ON u.id = a.user_id
						LEFT JOIN oum_position p ON u.position_id = p.id
						LEFT JOIN oum_position_language l ON p.id = l.position_id
						LEFT JOIN oum_contract_location c ON c.id = u.contract_id
						LEFT JOIN oum_department_user od ON u.id = od.user_id
						LEFT JOIN oum_department odt on odt.id = od.department_id
						LEFT JOIN oum_department_language dl ON dl.department_id = od.department_id
						LEFT JOIN oum_user_info rpu ON u.report_user_id = rpu.id
						LEFT JOIN oum_dingtalk_user du on du.sys_user_id = u.id

						LEFT JOIN (select a.* from (select user_id,is_deleted,office_location_id,effective_date,ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY effective_date desc) i from oum_office_location_assign where <![CDATA[ effective_date < now() ]]> and is_deleted = 0 ) a where a.i=1) ls on ls.user_id = u.id
						LEFT JOIN oum_office_location_language ol on ol.office_location_id = ls.office_location_id

						LEFT JOIN (select a.* from (select id,user_id,cost_center_id,efficetive_date,ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY efficetive_date desc) i from oum_cost_center_assign where <![CDATA[ efficetive_date < now() ]]> ) a where a.i=1) ca on ca.user_id = u.id
					WHERE
						u.is_deleted = 0
						and u.id = #{param.userId}
						and od.is_primary = 1
						and od.is_deleted = 0
						and dl.is_deleted = 0
						<if test="param.language != null and param.language !='' ">
							and l.language = #{param.language}
							and dl.language = #{param.language}
							and (ol.language = #{param.language} OR ol.language is null )
						</if>
						LIMIT 1
				</script>
			 """)
	// @DataPermission(columnNames = "u.id")
	OumUserInfoFullDto selectUserInfo(@Param("param") QueryOumUserDto param);

	@Select("""
			SELECT *
				FROM oum_cost_center_assign
				WHERE efficetive_date < now()
				 and is_deleted = 0
				 and user_id = #{userId}
				 ORDER BY efficetive_date DESC
				 limit 1
			""")
	CostCenterAssignPO selectUserCostcenter(@Param("userId") int userId);

	@Select("""
				SET sql_mode = REPLACE(@@sql_mode, 'ONLY_FULL_GROUP_BY', '');
			""")
	void openGroupBy();

	@Select("""
			<script>
			    SELECT
			        u.id,
			        u.code,
			        u.`name` as username,
			        u.`status`,
					a.`status` as accountStatus,
			        u.type,
			        u.gender,
			        u.email,
			        u.hire_time as hireTime,
			        u.report_user_id,
			        rpu.`name` as reportName,
					rpu.code as reportUserCode,
			        u.contract_id,
			        c.location as contractLocation,
			        l.`name` as positionName,
			        l.position_id as positionId,
					ls.office_location_id,
					ol.name as officeLocation,
			        dl.name as departmentName,
			        u.create_time as createTime,
			        u.update_time as updateTime

			    FROM
			        oum_user_info u
			        LEFT JOIN oum_account a on u.id = a.user_id
			        LEFT JOIN oum_position p on u.position_id = p.id
			        LEFT JOIN oum_position_language l on p.id = l.position_id
			        LEFT JOIN oum_contract_location c on c.id = u.contract_id

					LEFT JOIN (select a.* from (select user_id,is_deleted,office_location_id,effective_date,ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY effective_date desc) i from oum_office_location_assign where <![CDATA[ effective_date < now() ]]> and is_deleted = 0 ) a where a.i=1) ls on ls.user_id = u.id
					LEFT JOIN oum_office_location_language ol on ol.office_location_id = ls.office_location_id
			        LEFT JOIN oum_user_info rpu on u.report_user_id = rpu.id
			        LEFT JOIN oum_department_user od on u.id = od.user_id
			        LEFT JOIN oum_department_language dl on dl.department_id = od.department_id
			    WHERE
			        u.is_deleted = 0
					and od.is_primary = 1
					and od.is_deleted = 0
					and dl.is_deleted = 0
					<if test="param.code != null and param.code !=''"  >
			             and u.`code` like concat('%', #{param.code}, '%')
			        </if>
			        <if test="param.email != null and param.email !=''">
			              and u.email like concat('%', #{param.email}, '%')
			        </if>

					<if test="param.accountStatus != null and param.accountStatus.size > 0 ">
			              and a.`status` in
						<foreach item="id" collection="param.accountStatus" open="(" separator="," close=")">
							#{id}
						</foreach>
			        </if>
			        <if test="param.userStatus != null and param.userStatus !='' ">
			              and u.`status` = #{param.userStatus}
			        </if>
			        <if test="param.language != null and param.language !='' ">
			              and l.language = #{param.language}
			              and dl.language = #{param.language}
						  and (ol.language = #{param.language} OR ol.language is null )
			        </if>
			         <if test="param.departmentIds != null and param.departmentIds.size > 0 ">
			              and od.department_id in
						<foreach item="id" collection="param.departmentIds" open="(" separator="," close=")">
							#{id}
						</foreach>
			        </if>
					<if test="param.userIds != null and param.userIds.size > 0 ">
			              and u.id in
						<foreach item="id" collection="param.userIds" open="(" separator="," close=")">
							#{id}
						</foreach>
			        </if>
			        <if test="param.positionIds != null and param.positionIds.size > 0 ">
			              and u.position_id in
						<foreach item="id" collection="param.positionIds" open="(" separator="," close=")">
							#{id}
						</foreach>
			        </if>
			         <if test="param.reportUserIds != null and param.reportUserIds.size > 0 ">
			              and u.report_user_id in
						<foreach item="id" collection="param.reportUserIds" open="(" separator="," close=")">
							#{id}
						</foreach>
			        </if>
			        <if test="param.officeLocationIds != null and param.officeLocationIds.size > 0 ">
			              and ls.office_location_id in
						<foreach item="id" collection="param.officeLocationIds" open="(" separator="," close=")">
							#{id}
						</foreach>
			        </if>
			        <if test="param.hireStartTime != null and param.hireStartTime !=''  ">
			             <![CDATA[  and u.hire_time >= #{param.hireStartTime} ]]>
			        </if>
			        <if test="param.hireEndTime != null and param.hireEndTime !=''">
			             <![CDATA[  and u.hire_time <= CONCAT(#{param.hireEndTime},' 23:59:59')   ]]>
			        </if>
			         <if test="param.resignationStartTime != null and param.resignationStartTime !=''">
			             <![CDATA[  and u.resignation_time >=  #{param.resignationStartTime} ]]>
			        </if>
			        <if test="param.resignationEndTime != null and param.resignationEndTime !=''">
			             <![CDATA[  and u.resignation_time <= CONCAT(#{param.resignationEndTime},' 23:59:59')  ]]>
			        </if>
					order by u.update_time desc
			     </script>
			    """)
	@DataPermission(columnNames = "u.id")
	IPage<OumUserInfoListDto> getOumUserPage(IPage<OumUserInfoPO> page, @Param("param") OumUserInfoQuery param);

	@Select("""
			<script>
			    SELECT distinct
			        u.id,
			        u.code,
			        u.`name` as name,
			        u.`status`,
			        u.type,
					u.phone_no as phoneNo,
			        u.gender,
			        u.email,
			        u.hire_time as hireTime,
			        u.report_user_id as reportUserId,
			        u.create_time as createTime,
			        u.update_time as updateTime,
			        GROUP_CONCAT(DISTINCT r.label SEPARATOR ',') as label

			    FROM
			        oum_user_info u
			        LEFT JOIN oum_role_user ru on u.id = ru.user_id
					LEFT JOIN oum_account a ON u.id = a.user_id
					LEFT JOIN oum_role_user ruu on u.id = ruu.user_id
					LEFT JOIN oum_role r on r.id = ruu.role_id
			    WHERE
			        u.is_deleted = 0
					<if test="param.code != null and param.code !=''"  >
			             and u.`code` like concat('%', #{param.code}, '%')
			        </if>
					<if test="param.accountStatus != null and param.accountStatus !=''">
			              and a.`status` = #{param.accountStatus}
			        </if>
			        <if test="param.status != null ">
			              and u.`status` = #{param.status}
			        </if>
			        <if test="param.email != null and param.email !=''">
			              and u.email like concat('%', #{param.email}, '%')
			        </if>
			        <if test="param.roleLabel != null and param.roleLabel !=''">
			              and FIND_IN_SET(#{param.roleLabel},r.label )
			        </if>
					<if test="param.userIds != null and param.userIds.size > 0 ">
			              and u.id in
						<foreach item="id" collection="param.userIds" open="(" separator="," close=")">
							#{id}
						</foreach>
			        </if>
					<if test="param.codes != null and param.codes.size > 0 ">
			              and u.code in
						<foreach item="code" collection="param.codes" open="(" separator="," close=")">
							#{code}
						</foreach>
			        </if>
					<if test="param.roleIds != null and param.roleIds.size > 0 ">
			              and ru.role_id in
						<foreach item="id" collection="param.roleIds" open="(" separator="," close=")">
							#{id}
						</foreach>
			        </if>
					group by
					u.id,
			        u.code,
			        u.`name`
			     </script>
			    """)
	List<OumUserSimpleListDto> getOumUserList(@Param("param") OumUserListQuery param);

	/**
	 * 用户列表（只返回id和直属领导id）
	 * @return OumUserInfoPO 只返回id和report_user_id
	 */
	default List<OumUserInfoPO> listReportUser() {
		return this.selectList(new LambdaQueryWrapper<OumUserInfoPO>().select(OumUserInfoPO::getId, OumUserInfoPO::getReportUserId));
	}

	@Select("""
			<script>
			    SELECT distinct
			        u.id,
			        u.code,
			        u.`name` as name,
			        u.`status`,
					u.phone_no as phoneNo,
			        u.gender,
			        u.email,
			        u.`type`,
			        u.`report_user_id` as reportUserId,
			        GROUP_CONCAT(DISTINCT ruu.role_id SEPARATOR ',') as roleIdStr,
			        GROUP_CONCAT(DISTINCT odl.name SEPARATOR ',') as deptNameStr,
			        GROUP_CONCAT(DISTINCT od.manager_id SEPARATOR ',') as deptLeaderIdStr,
			        GROUP_CONCAT(DISTINCT odu.department_id  ORDER BY odu.is_primary DESC SEPARATOR ',') as deptIdStr
			    FROM
			        oum_user_info u
			        LEFT JOIN oum_role_user ru on u.id = ru.user_id  and ru.is_deleted=0
					LEFT JOIN oum_role_user ruu on u.id = ruu.user_id and ruu.is_deleted=0
					LEFT JOIN oum_role r on r.id = ruu.role_id and r.is_deleted=0
					LEFT JOIN oum_department_user odu on odu.user_id = u.id and odu.is_deleted=0
					LEFT JOIN oum_department_language odl on odu.department_id = odl.department_id and odl.language=#{param.language}  and odl.is_deleted=0
					Left join oum_department od on od.id = odu.department_id and od.is_deleted=0
			  		LEFT JOIN oum_user_info du on du.id = od.manager_id and du.is_deleted=0
			    WHERE
			        u.is_deleted = 0
					<if test="param.userIds != null and param.userIds.size > 0 ">
			              and u.id in
						<foreach item="id" collection="param.userIds" open="(" separator="," close=")">
							#{id}
						</foreach>
			        </if>
					<if test="param.roleIds != null and param.roleIds.size > 0 ">
			              and ru.role_id in
						<foreach item="id" collection="param.roleIds" open="(" separator="," close=")">
							#{id}
						</foreach>
			        </if>
			        <if test="param.deptIdList != null and param.deptIdList.size > 0 ">
			              and odu.department_id in
						<foreach item="id" collection="param.deptIdList" open="(" separator="," close=")">
							#{id}
						</foreach>
			        </if>
			       GROUP BY
					u.id,
			        u.code,
			        u.`name` ,
			        u.`status`,
					u.phone_no ,
			        u.gender,
			        u.email,
			        u.`type`,
			        u.`report_user_id`
			     </script>
			    """)
	List<OumUserQueryListDto> getUserInfoList(@Param("param") OumUserListQuery query);

	@Select("""
			 <script>
			SELECT
				u.id,
				u.id as userId,
				u.report_user_id as reportUserId,
				uu.name AS reportUserName,
				uu.code AS reportUserCode,
				'0' as reportType
			FROM
				oum_user_info u
				LEFT JOIN oum_user_info uu ON uu.id = u.report_user_id and uu.is_deleted=0
			WHERE
				u.id = #{userId}
				</script>
			    """)
	OumUserBizReportSimple getUserReportTo(Integer userId);

	@Update("""
			<script>
					update oum_user_info

						<set>

							<if test="type != null and type != ''">
								type = #{type,jdbcType=VARCHAR},
							</if>

							<if test="name != null and name != ''">
								name = #{name,jdbcType=VARCHAR},
							</if>

							<if test="gender != null and gender != ''">
								gender = #{gender,jdbcType=INTEGER},
							</if>

							<if test="phoneNo != null and phoneNo != ''">
								phone_no = #{phoneNo,jdbcType=VARCHAR},
							</if>

							<if test="email != null and email != ''">
								email = #{email,jdbcType=VARCHAR},
							</if>

							<if test="corporationId != null and corporationId != ''">
								corporation_id = #{corporationId,jdbcType=INTEGER},
							</if>

							<if test="positionId != null and positionId != ''">
								position_id = #{positionId,jdbcType=INTEGER},
							</if>

							<if test="positionAddedTime != null and positionAddedTime != ''">
								position_added_time = #{positionAddedTime,jdbcType=TIMESTAMP},
							</if>

							<if test="reportUserId != null and reportUserId != ''">
								report_user_id = #{reportUserId,jdbcType=INTEGER},
							</if>

							<if test="contractId != null and contractId != ''">
								contract_id = #{contractId,jdbcType=INTEGER},
							</if>

							<if test="officeZone != null and officeZone != ''">
								office_zone = #{officeZone,jdbcType=VARCHAR},
							</if>

							<if test="hireTime != null and hireTime != ''">
								hire_time = #{hireTime,jdbcType=TIMESTAMP},
							</if>

							<if test="resignationTime != null and resignationTime != ''">
								resignation_time = #{resignationTime,jdbcType=TIMESTAMP},
							</if>

							<if test="socialInsuranceNo != null and socialInsuranceNo != ''">
								social_insurance_no = #{socialInsuranceNo,jdbcType=VARCHAR},
							</if>

							<if test="housingFundNo != null and housingFundNo != ''">
								housing_fund_no = #{housingFundNo,jdbcType=VARCHAR},
							</if>

							<if test="status != null and status != ''">
								status = #{status,jdbcType=INTEGER},
							</if>

							<if test="updateBy != null and updateBy != ''">
								update_by = #{updateBy,jdbcType=INTEGER},
							</if>

							<if test="updateTime != null and updateTime != ''">
								update_time = #{updateTime,jdbcType=TIMESTAMP},
							</if>

							<if test="isDeleted != null and isDeleted != ''">
								is_deleted = #{isDeleted,jdbcType=INTEGER},
							</if>

							<if test="costcenterId != null and costcenterId != ''">
								costcenter_id = #{costcenterId,jdbcType=INTEGER},
							</if>

						</set>
						where id = #{id,jdbcType=INTEGER}
					</script>
					""")
	/**
	 * 更新用户数据非空即更新
	 * @param po po
	 * @return ok
	 */
	int updateUserInfo(OumUserInfoPO po);

	@Update("""
			<script>
					update oum_user_info

						<set>

								type = #{type,jdbcType=VARCHAR},

								name = #{name,jdbcType=VARCHAR},

								gender = #{gender,jdbcType=INTEGER},

								phone_no = #{phoneNo,jdbcType=VARCHAR},

								email = #{email,jdbcType=VARCHAR},

								corporation_id = #{corporationId,jdbcType=INTEGER},

								position_id = #{positionId,jdbcType=INTEGER},

								position_added_time = #{positionAddedTime,jdbcType=TIMESTAMP},

								report_user_id = #{reportUserId,jdbcType=INTEGER},

								contract_id = #{contractId,jdbcType=INTEGER},

								office_zone = #{officeZone,jdbcType=VARCHAR},

								hire_time = #{hireTime,jdbcType=TIMESTAMP},

								resignation_time = #{resignationTime,jdbcType=TIMESTAMP},

								social_insurance_no = #{socialInsuranceNo,jdbcType=VARCHAR},

								housing_fund_no = #{housingFundNo,jdbcType=VARCHAR},

								status = #{status,jdbcType=INTEGER},

								update_by = #{updateBy,jdbcType=INTEGER},

								update_time = #{updateTime,jdbcType=TIMESTAMP},

								is_deleted = #{isDeleted,jdbcType=INTEGER},

								costcenter_id = #{costcenterId,jdbcType=INTEGER}

						</set>
						where id = #{id,jdbcType=INTEGER}
					</script>
					""")
	/**
	 * 更新用户数据非空即更新
	 * @param po po
	 * @return ok
	 */
	int updateUserInfoIncNull(OumUserInfoPO po);

	/**
	 * 获取相应状态的用户id集合
	 * @param userIdSet 用户id集合
	 * @param status 用户状态，1 在职，0 离职
	 * @return 用户id集合
	 */
	default Set<Integer> getIdByUserIdSetAndStatus(Set<Integer> userIdSet, int status) {
		List<OumUserInfoPO> userInfoList = this.selectList(new LambdaQueryWrapper<OumUserInfoPO>().select(OumUserInfoPO::getId)
			.in(OumUserInfoPO::getId, userIdSet)
			.eq(OumUserInfoPO::getStatus, status));
		return userInfoList.stream().map(OumUserInfoPO::getId).collect(Collectors.toSet());
	}

	/**
	 * 查询用户信息
	 * @param emails 邮箱集
	 * @param excludeEmails 需要排除的邮箱集
	 * @return List
	 */
	default List<OumUserInfoPO> listByEmailAndDept(Set<String> emails, Set<String> excludeEmails) {
		return this.selectList(
				new LambdaQueryWrapper<OumUserInfoPO>().select(OumUserInfoPO::getId, OumUserInfoPO::getName, OumUserInfoPO::getEmail)
					.in(CollectionUtils.isNotEmpty(emails), OumUserInfoPO::getEmail, emails)
					.notIn(CollectionUtils.isNotEmpty(excludeEmails), OumUserInfoPO::getEmail, excludeEmails));
	}
}
