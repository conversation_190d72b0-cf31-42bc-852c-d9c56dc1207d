package com.renpho.erp.smc.infrastructure.persistence.dto.message;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 系统消息表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MessageVo implements Serializable {

	/**
	 * 自增ID
	 */
	private Integer id;

	/**
	 * 消息ID
	 */
	private String messageId;

	/**
	 * 消息接收人用户ID
	 */
	private Integer userId;

	private String createByName;

	private String createByCode;

	/**
	 * 消息类型 TEXT（文本）
	 */
	private String type;

	/**
	 * 消息标题
	 */
	private String title;

	/**
	 * 消息内容
	 */
	private String content;

	/**
	 * 系统名称
	 */
	private String service;

	/**
	 * 功能模块
	 */
	private String module;

	/**
	 * 已读状态 0未读 1已读
	 */
	private Integer isRead;

	/**
	 * 创建人
	 */
	private Integer createBy;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新人
	 */
	private Integer updateBy;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 0未删除，1已经删除
	 */
	private Integer isDeleted;

	@Serial
	private static final long serialVersionUID = 1L;

}