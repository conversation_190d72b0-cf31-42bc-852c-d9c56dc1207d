package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.AlterationPO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@TableName("oum_department_language")
public class DepartmentLanguagePO extends AlterationPO<Integer, Integer, LocalDateTime, Integer> {

	/**
	 * 部门ID
	 */
	private Integer departmentId;

	/**
	 * 语言
	 */
	private String language;

	/**
	 * 名称
	 */
	private String name;

}
