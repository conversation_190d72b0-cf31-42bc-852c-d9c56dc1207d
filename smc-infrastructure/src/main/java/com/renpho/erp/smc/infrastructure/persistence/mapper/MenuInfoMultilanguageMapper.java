package com.renpho.erp.smc.infrastructure.persistence.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.MenuInfoMultilanguagePO;

import java.util.List;
import java.util.Set;

/**
 * 参数配置 Mapper.
 *
 * <AUTHOR>
 */
public interface MenuInfoMultilanguageMapper extends BaseMapper<MenuInfoMultilanguagePO> {

	void updateLanguage(MenuInfoMultilanguagePO multilanguagePO);

	void deleteByMenuId(Integer id);

	/**
	 * 菜单多语言列表.
	 * @param menuIdSet 菜单id列表
	 * @param language 语言
	 * @return List
	 */
	default List<MenuInfoMultilanguagePO> listByMenuIdSetAndLanguage(Set<Integer> menuIdSet, String language) {
		return this.selectList(new LambdaQueryWrapper<MenuInfoMultilanguagePO>().in(MenuInfoMultilanguagePO::getMenuId, menuIdSet)
			.eq(MenuInfoMultilanguagePO::getLanguage, language));
	}

}
