package com.renpho.erp.smc.infrastructure.persistence.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.renpho.erp.data.permission.aop.annotation.DataPermission;
import com.renpho.erp.smc.infrastructure.persistence.po.NoticePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 系统公告Mapper
 *
 * <AUTHOR>
 * @date 2025/03/25
 */
@Mapper
public interface NoticeMapper extends BaseMapper<NoticePO> {

	@SuppressWarnings("all")
	@DataPermission
	default IPage<NoticePO> permissionPage(IPage<NoticePO> page, @Param(Constants.WRAPPER) Wrapper<NoticePO> queryWrapper) {
		return this.selectPage(page, queryWrapper);
	}

}