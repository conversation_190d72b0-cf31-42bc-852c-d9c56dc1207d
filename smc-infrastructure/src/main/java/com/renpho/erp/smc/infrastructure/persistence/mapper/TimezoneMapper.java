package com.renpho.erp.smc.infrastructure.persistence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.TimezonePO;

public interface TimezoneMapper extends BaseMapper<TimezonePO> {

	int deleteByPrimaryKey(Integer id);

	@Override
	int insert(TimezonePO record);

	int insertSelective(TimezonePO record);

	TimezonePO selectByPrimaryKey(Integer id);

	int updateByPrimaryKeySelective(TimezonePO record);

	int updateByPrimaryKey(TimezonePO record);

}