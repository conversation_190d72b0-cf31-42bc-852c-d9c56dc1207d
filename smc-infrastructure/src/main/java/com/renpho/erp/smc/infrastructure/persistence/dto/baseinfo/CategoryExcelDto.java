package com.renpho.erp.smc.infrastructure.persistence.dto.baseinfo;

import java.io.Serializable;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;

import lombok.Data;

@Data
public class CategoryExcelDto implements VO, Serializable {

	private Integer id;

	@Trans(type = TransType.DICTIONARY, key = "status_dict", ref = "statusName")
	private Integer status;

	private String level1;

	private String level2;

	private String level3;

	private String statusName;

}
