package com.renpho.erp.smc.infrastructure.persistence.dto.index;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 系统公告表
 *
 * <AUTHOR>
 * @date 2025/03/25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class NoticeUserIdNameVo implements Serializable {

	/**
	 * 修改人
	 */
	private Integer updateBy;

	/**
	 * 修改人工号
	 */
	private String updateByCode;

	/**
	 * 修改人名称
	 */
	private String updateByName;

	@Serial
	private static final long serialVersionUID = 1L;

}