package com.renpho.erp.smc.infrastructure.persistence.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.renpho.erp.smc.domain.systemsetting.MultiLanguage;
import com.renpho.erp.smc.domain.systemsetting.dict.DictItem;
import com.renpho.erp.smc.infrastructure.persistence.po.DictItemPO;

/**
 * 字典类型 Mapper.
 *
 * <AUTHOR>
 */
public interface DictItemMapper extends BaseMapper<DictItemPO> {

	/**
	 * 查询字典类型列表.
	 * @param item 语种
	 * @return DictItemPO List
	 */
	List<DictItemPO> selectDictItemList(@Param("item") DictItem item);

	/**
	 * 根据名称模糊查询字典数据列表.
	 * @param item 语种
	 * @return DictItemPO List
	 */
	List<DictItemPO> selectDictItemListByName(@Param("item") DictItem item);

	/**
	 * 查询字典类型数量.
	 * @param names 名称
	 * @return boolean
	 */
	boolean selectDictItemNameCount(@Param("names") List<MultiLanguage> names);

	/**
	 * 查询字典类型详情.
	 * @param id 字典类型id
	 * @return DictItemPO
	 */
	List<DictItemPO> selectDictItemDetailById(Integer id);

	DictItemPO selectDetailById(@Param("id") Integer id);

	List<DictItemPO> selectAllActived();

	List<DictItemPO> selectActivedByType(Integer dictId);

	/**
	 * 更新字典类型.
	 * @param dictItemPO 字典类型
	 */
	void updateDictItem(DictItemPO dictItemPO);

	/**
	 * 查询字典类型数量.
	 * @param item 名称
	 * @return boolean
	 */
	long selectDictItemCount(@Param("item") DictItem item);

	List<DictItemPO> selectActiveDictList();

	@Select("""
			<script>
				select  di.id,
				     dl.language,
				     dl.name,
				     di.dict_id,
				     di.dict_key,
				     di.dict_type,
				     di.remark,
				     di.sort,
				     di.status,
				     di.create_time,
				     di.create_by,
				     di.update_by,
				     di.update_time,
				     di.is_deleted
				     from sys_dict_item di
				     join sys_dict_item_language dl on di.id = dl.dict_item_id
				     where di.is_deleted = 0
						<if test="dictType != null and dictType != ''">
							and di.dict_type = #{dictType}
						</if>
						<if test="language != null and language != ''">
							and dl.language=#{language}
						</if>
						<if test="name != null and name != ''">
							and dl.name=#{name}
						</if>
					</script>
				""")
	List<DictItemPO> selectDictListByType(@Param("dictType") String dictType, @Param("language") String language,
			@Param("name") String name);

	@Select("""
			<script>
				select
				     dl.language,
				     dl.name,
				     di.dict_id,
				     di.dict_key,
				     di.dict_type,
				     di.remark,
				     di.sort,
				     di.status,
				     di.create_time,
				     di.create_by,
				     di.update_by,
				     di.update_time,
				     di.is_deleted
				     from sys_dict_item di
				     join sys_dict_item_language dl on di.id = dl.dict_item_id
				     where di.is_deleted = 0
							and di.dict_type = #{dictType}
							and dl.language=#{language}
							and dl.name=#{name}
						limit 1
					</script>
				""")
	DictItemPO selectDictByName(@Param("dictType") String dictType, @Param("language") String language, @Param("name") String name);

	@Select("""
			<script>
				select
				     dl.language,
				     dl.name,
				     di.dict_id,
				     di.dict_key,
				     di.dict_type,
				     di.remark,
				     di.sort,
				     di.status,
				     di.create_time,
				     di.create_by,
				     di.update_by,
				     di.update_time,
				     di.is_deleted
				     from sys_dict_item di
				     join sys_dict_item_language dl on di.id = dl.dict_item_id
				     where di.is_deleted = 0
							and di.dict_type = #{dictType}
							and di.dict_key = #{dictKey}
							and dl.language=#{language}
						limit 1
					</script>
				""")
	DictItemPO selectDictByKey(@Param("dictType") String dictType, @Param("language") String language, @Param("dictKey") String dictKey);

	/**
	 * 查询字典类型数量.
	 * @param multiLanguage 多语言
	 * @return boolean
	 */
	boolean selectDictItemLanguageCount(@Param("item") MultiLanguage multiLanguage, @Param("dictType") String dictType);

}
