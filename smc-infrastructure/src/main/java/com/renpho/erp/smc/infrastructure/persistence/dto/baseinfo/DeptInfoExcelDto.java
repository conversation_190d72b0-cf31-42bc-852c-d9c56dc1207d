package com.renpho.erp.smc.infrastructure.persistence.dto.baseinfo;

import java.io.Serializable;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;

import lombok.Data;

@Data
public class DeptInfoExcelDto implements VO, Serializable {

	private Integer id;

	private String name;

	private String code;

	private String managerName;

	@Trans(type = TransType.DICTIONARY, key = "status_dict", ref = "statusName")
	private Integer status;

	private String statusName;

	private String parentName;

	private String parentCode;

	private String userCode;

}
