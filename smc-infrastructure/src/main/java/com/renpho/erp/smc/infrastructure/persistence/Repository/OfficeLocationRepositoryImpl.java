package com.renpho.erp.smc.infrastructure.persistence.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.smc.domain.organizationmanagement.office.OfficeLocation;
import com.renpho.erp.smc.domain.organizationmanagement.office.OfficeLocationRepository;
import com.renpho.erp.smc.domain.systemsetting.MultiLanguage;
import com.renpho.erp.smc.exception.BizErrorCode;
import com.renpho.erp.smc.infrastructure.persistence.mapper.OfficeLocationLanguageMapper;
import com.renpho.erp.smc.infrastructure.persistence.mapper.OfficeLocationMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.OfficeLocationLanguagePO;
import com.renpho.erp.smc.infrastructure.persistence.po.OfficeLocationPO;
import com.renpho.erp.smc.infrastructure.persistence.po.transformer.OfficeLocationTransformer;
import com.renpho.karma.exception.ErrorCodeException;

import lombok.RequiredArgsConstructor;

@Repository
@RequiredArgsConstructor
public class OfficeLocationRepositoryImpl extends ServiceImpl<OfficeLocationMapper, OfficeLocationPO> implements OfficeLocationRepository {

	private final OfficeLocationLanguageMapper officeLocationLanguageMapper;

	@Override
	public Integer saveOfficeLocation(OfficeLocation officeLocation) {

		OfficeLocationPO officeLocationPO = OfficeLocationTransformer.INSTANCE.toOfficeLocationPO(officeLocation);
		this.baseMapper.insert(officeLocationPO);
		Integer officeLocationId = officeLocationPO.getId();
		List<OfficeLocationLanguagePO> officeLocationLanguagePOList = OfficeLocationTransformer.INSTANCE
			.toOfficeLocationLanguagePOList(officeLocation.getNames(), officeLocationId);
		for (OfficeLocationLanguagePO officeLocationLanguagePO : officeLocationLanguagePOList) {
			this.officeLocationLanguageMapper.insert(officeLocationLanguagePO);
		}
		return officeLocationId;

	}

	@Override
	public OfficeLocation findById(Integer id) {
		OfficeLocationPO officeLocationPO = this.getBaseMapper().selectById(id);
		if (Objects.isNull(officeLocationPO)) {
			throw new ErrorCodeException(BizErrorCode.OFFICE_LOCATION_NOT_FOUND);
		}
		return OfficeLocationTransformer.INSTANCE.toOfficeLocation(officeLocationPO, Collections.emptyList());
	}

	@Override
	public void updateOfficeLocation(OfficeLocation officeLocation) {

		OfficeLocationPO officeLocationPO = OfficeLocationTransformer.INSTANCE.toOfficeLocationPO(officeLocation);
		this.getBaseMapper().updateById(officeLocationPO);
		List<MultiLanguage> names = officeLocation.getNames();
		for (MultiLanguage multiLanguage : names) {
			OfficeLocationLanguagePO officeLocationLanguagePO = OfficeLocationTransformer.INSTANCE.toOfficeLocationLanguagePO(multiLanguage,
					officeLocationPO.getId());
			List<OfficeLocationLanguagePO> officeLocationLanguagePOS = this.officeLocationLanguageMapper
				.selectList(Wrappers.<OfficeLocationLanguagePO> lambdaQuery()
					.eq(OfficeLocationLanguagePO::getOfficeLocationId, officeLocationPO.getId())
					.eq(OfficeLocationLanguagePO::getLanguage, multiLanguage.getLanguage()));

			if (Objects.isNull(officeLocationLanguagePOS)) {
				this.officeLocationLanguageMapper.insert(officeLocationLanguagePO);
			}
			else {
				officeLocationLanguagePO.setId(officeLocationLanguagePOS.get(0).getId());
				this.officeLocationLanguageMapper.updateById(officeLocationLanguagePO);
			}
		}
		List<String> languageExistList = names.stream().map(MultiLanguage::getLanguage).collect(Collectors.toList());
		LambdaQueryWrapper<OfficeLocationLanguagePO> deleteQueryWrapper = Wrappers.lambdaQuery(OfficeLocationLanguagePO.class)
			.eq(OfficeLocationLanguagePO::getOfficeLocationId, officeLocationPO.getId())
			.notIn(OfficeLocationLanguagePO::getLanguage, languageExistList);
		this.officeLocationLanguageMapper.delete(deleteQueryWrapper);
	}

	@Override
	public void checkDuplication(OfficeLocation officeLocation) {
		for (MultiLanguage multiLanguage : officeLocation.getNames()) {
			String lang = multiLanguage.getLanguage();
			String name = multiLanguage.getName();
			LambdaQueryWrapper<OfficeLocationLanguagePO> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(OfficeLocationLanguagePO::getLanguage, lang).eq(OfficeLocationLanguagePO::getName, name);

			Long count = this.officeLocationLanguageMapper.selectCount(queryWrapper);
			if (count > 0) {
				throw new ErrorCodeException(BizErrorCode.OFFICE_LOCATION_FOUND, multiLanguage.getName());
			}
		}
	}

	@Override
	public void checkUpdateDuplication(OfficeLocation officeLocation) {
		for (MultiLanguage multiLanguage : officeLocation.getNames()) {
			String lang = multiLanguage.getLanguage();
			String name = multiLanguage.getName();
			LambdaQueryWrapper<OfficeLocationLanguagePO> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(OfficeLocationLanguagePO::getLanguage, lang)
				.eq(OfficeLocationLanguagePO::getName, name)
				.ne(OfficeLocationLanguagePO::getOfficeLocationId, officeLocation.getId().getId());

			Long count = this.officeLocationLanguageMapper.selectCount(queryWrapper);
			if (count > 0) {
				throw new ErrorCodeException(BizErrorCode.OFFICE_LOCATION_FOUND, multiLanguage.getName());
			}
		}
	}

}
