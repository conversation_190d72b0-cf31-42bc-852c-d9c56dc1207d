package com.renpho.erp.smc.infrastructure.persistence.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.renpho.erp.smc.domain.systemsetting.MultiLanguage;
import com.renpho.erp.smc.domain.systemsetting.menu.Menu;
import com.renpho.erp.smc.domain.systemsetting.menu.MenuRepository;
import com.renpho.erp.smc.exception.BizErrorCode;
import com.renpho.erp.smc.infrastructure.constants.UserConstants;
import com.renpho.erp.smc.infrastructure.persistence.mapper.MenuInfoMapper;
import com.renpho.erp.smc.infrastructure.persistence.mapper.MenuInfoMultilanguageMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.MenuInfoMultilanguagePO;
import com.renpho.erp.smc.infrastructure.persistence.po.MenuInfoPO;
import com.renpho.erp.smc.infrastructure.persistence.po.transformer.MenuTransformer;
import com.renpho.karma.exception.ErrorCodeException;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;

/**
 * 菜单仓储实现.
 *
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class MenuRepositoryImpl implements MenuRepository {

	private final MenuInfoMapper menuInfoMapper;

	private final MenuInfoMultilanguageMapper menuInfoMultilanguageMapper;

	@Override
	public Integer save(Menu menu) {
		MenuInfoPO menuInfoPO = MenuTransformer.INSTANCE.toMenuInfoPO(menu);
		menuInfoMapper.insert(menuInfoPO);

		this.saveMultilanguage(menu, menuInfoPO);

		return menuInfoPO.getId();
	}

	private void saveMultilanguage(Menu menu, MenuInfoPO menuInfoPO) {

		if (CollUtil.isEmpty(menu.getNames())) {
			return;
		}

		for (MultiLanguage multiLanguage : menu.getNames()) {
			MenuInfoMultilanguagePO menuInfoMultilanguagePO = new MenuInfoMultilanguagePO();
			menuInfoMultilanguagePO.setMenuId(menuInfoPO.getId());

			menuInfoMultilanguagePO.setLanguage(multiLanguage.getLanguage());
			menuInfoMultilanguagePO.setName(multiLanguage.getName());
			menuInfoMultilanguageMapper.insert(menuInfoMultilanguagePO);
		}
	}

	@Override
	public void validateAddMenu(Menu menu) {
		if (CollUtil.isNotEmpty(menu.getNames())) {
			for (MultiLanguage multiLanguage : menu.getNames()) {
				if (menuInfoMapper.selectMenuLanguageCount(multiLanguage)) {
					throw new ErrorCodeException(BizErrorCode.MENU_FOUND, multiLanguage.getLanguage());
				}
			}
		}
	}

	@Override
	public Menu findById(Menu.MenuID id) {
		if (id == null) {
			return null;
		}
		List<MenuInfoPO> menuInfoPOS = menuInfoMapper.selectMenuById(id.getId());
		if (CollUtil.isEmpty(menuInfoPOS)) {
			throw new ErrorCodeException(BizErrorCode.MENU_NOT_FOUND, id.getId());
		}

		return MenuTransformer.INSTANCE.toMenu(menuInfoPOS.get(0));
	}

	@Override
	public void validateLevel(Menu menu) {
		if ("Catalogue".equals(menu.getMenuType()) || "Menu".equals(menu.getMenuType())) {
			if (menu.getParentId() == 0) {
				return;
			}
			MenuInfoPO menuInfoPO1 = menuInfoMapper.selectMenuByParentId(menu.getParentId());
			if (menuInfoPO1 != null) {
				if (menuInfoPO1.getParentId() == 0) {
					return;
				}
				MenuInfoPO menuInfoPO2 = menuInfoMapper.selectMenuByParentId(menuInfoPO1.getParentId());
				if (menuInfoPO2 != null) {
					MenuInfoPO menuInfoPO3 = menuInfoMapper.selectMenuByParentId(menuInfoPO2.getParentId());
					if (menuInfoPO3 != null) {
						throw new ErrorCodeException(BizErrorCode.MENU_LEVEL_OVER_THREE, menu.getNames());
					}
				}
			}
		}
	}

	@Override
	public void update(Menu menu) {
		menuInfoMapper.updateMenu(MenuTransformer.INSTANCE.toMenuInfoPO(menu));
		if (CollUtil.isNotEmpty(menu.getNames())) {
			menu.getNames().forEach(e -> updateMultilanguage(menu, e));
		}
	}

	private void updateMultilanguage(Menu menu, MultiLanguage multiLanguage) {
		MenuInfoMultilanguagePO multilanguagePO = new MenuInfoMultilanguagePO();
		multilanguagePO.setMenuId(menu.getId().getId());
		multilanguagePO.setLanguage(multiLanguage.getLanguage());
		multilanguagePO.setName(multiLanguage.getName());
		multilanguagePO.setIsDeleted(menu.getIsDeleted());

		menuInfoMultilanguageMapper.updateLanguage(multilanguagePO);
	}

	@Override
	public void deleteMenuLanguage(Integer id) {
		menuInfoMultilanguageMapper.deleteByMenuId(id);
	}

	@Override
	public void deleteMenu(Integer id) {
		menuInfoMapper.deleteById(id);
	}

	@Override
	public void validatePerms(Menu menu) {
		// 如果是按钮，权限标识符重复校验
		if (!UserConstants.TYPE_BUTTON.equals(menu.getMenuType())) {
			return;
		}
		MenuInfoPO menuInfo = menuInfoMapper.getOneByPermsAndMenuType(UserConstants.TYPE_BUTTON, menu.getPerms());
		if (Objects.isNull(menuInfo)) {
			return;
		}
		Menu.MenuID menuId = menu.getId();
		Integer id = menuInfo.getId();
		if (Objects.isNull(menuId)) {
			// 新增时，名称重复
			throw new ErrorCodeException(BizErrorCode.MENU_PERMS_FOUND, menuInfo.getPerms());
		}
		else {
			// 修改时，名称重复
			if (Objects.nonNull(id) && !id.equals(menuId.getId())) {
				throw new ErrorCodeException(BizErrorCode.MENU_PERMS_FOUND, menuInfo.getPerms());
			}
		}
	}

	@Override
	public List<Integer> getChildMenuIds(Integer menuId, Integer status) {
		List<Integer> childIds = new ArrayList<>();
		this.collectChildMenuIds(menuId, status, childIds);
		return childIds;
	}

	private void collectChildMenuIds(Integer menuId, Integer status, List<Integer> childIds) {
		LambdaQueryWrapper<MenuInfoPO> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(MenuInfoPO::getParentId, menuId);
		if (status != null) {
			queryWrapper.eq(MenuInfoPO::getStatus, status);
		}
		List<MenuInfoPO> children = this.menuInfoMapper.selectList(queryWrapper);
		for (MenuInfoPO child : children) {
			if (!childIds.contains(child.getId())) {
				childIds.add(child.getId());
				this.collectChildMenuIds(child.getId(), status, childIds);
			}
		}
	}

}
