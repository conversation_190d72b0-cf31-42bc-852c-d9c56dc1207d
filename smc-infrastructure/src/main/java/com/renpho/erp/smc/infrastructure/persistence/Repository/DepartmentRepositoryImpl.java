package com.renpho.erp.smc.infrastructure.persistence.Repository;

import java.util.*;
import java.util.stream.Collectors;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.renpho.erp.smc.domain.organizationmanagement.Department.Department;
import com.renpho.erp.smc.domain.organizationmanagement.Department.DepartmentRepository;
import com.renpho.erp.smc.domain.organizationmanagement.Department.DepartmentStatus;
import com.renpho.erp.smc.domain.systemsetting.MultiLanguage;
import com.renpho.erp.smc.exception.BizErrorCode;
import com.renpho.erp.smc.infrastructure.persistence.mapper.DepartmentLanguageMapper;
import com.renpho.erp.smc.infrastructure.persistence.mapper.DepartmentMapper;
import com.renpho.erp.smc.infrastructure.persistence.mapper.DepartmentUserMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.DepartmentLanguagePO;
import com.renpho.erp.smc.infrastructure.persistence.po.DepartmentPO;
import com.renpho.erp.smc.infrastructure.persistence.po.DepartmentUserPO;
import com.renpho.erp.smc.infrastructure.persistence.po.transformer.DepartmentTransformer;
import com.renpho.karma.exception.ErrorCodeException;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/09/19 17:05
 */
@Repository
@RequiredArgsConstructor
public class DepartmentRepositoryImpl implements DepartmentRepository {

	private final DepartmentMapper departmentMapper;

	private final DepartmentLanguageMapper departmentLanguageMapper;

	private final DepartmentUserMapper departmentUserMapper;

	@Override
	public void checkDuplication(Department department) {

		for (MultiLanguage multiLanguage : department.getNames()) {
			String lang = multiLanguage.getLanguage();
			String name = multiLanguage.getName();
			LambdaQueryWrapper<DepartmentLanguagePO> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(DepartmentLanguagePO::getLanguage, lang).eq(DepartmentLanguagePO::getName, name);

			Long count = this.departmentLanguageMapper.selectCount(queryWrapper);
			if (count > 0) {
				throw new ErrorCodeException(BizErrorCode.DEPARTMENT_FOUND, multiLanguage.getName());
			}
		}
	}

	@Override
	public void checkDuplicationExcludeById(Department department) {
		for (MultiLanguage multiLanguage : department.getNames()) {
			String lang = multiLanguage.getLanguage();
			String name = multiLanguage.getName();
			Long count = this.departmentLanguageMapper.selectCount(Wrappers.<DepartmentLanguagePO> lambdaQuery()
				.eq(DepartmentLanguagePO::getLanguage, lang)
				.eq(DepartmentLanguagePO::getName, name)
				.ne(DepartmentLanguagePO::getDepartmentId, department.getId().getId()));
			if (count > 0) {
				throw new ErrorCodeException(BizErrorCode.DEPARTMENT_FOUND, multiLanguage.getName());
			}
		}
	}

	private String generateCode(Integer id) {
		return "D" + String.format("%06d", id);
	}

	@Override
	public Integer saveDepartment(Department department) {
		DepartmentPO departmentPO = DepartmentTransformer.INSTANCE.toDepartmentPO(department);
		this.departmentMapper.insert(departmentPO);
		Integer departmentId = departmentPO.getId();
		List<DepartmentLanguagePO> departmentLanguagePOList = DepartmentTransformer.INSTANCE
			.toDepartmentLanguagePOList(department.getNames(), departmentId);
		departmentPO.setCode(this.generateCode(departmentId));
		this.departmentMapper.updateById(departmentPO);
		for (DepartmentLanguagePO departmentLanguagePO : departmentLanguagePOList) {
			this.departmentLanguageMapper.insert(departmentLanguagePO);
		}
		return departmentId;
	}

	@Override
	public Department findById(Integer id) {
		DepartmentPO departmentPO = this.departmentMapper.selectById(id);
		if (Objects.isNull(departmentPO)) {
			throw new ErrorCodeException(BizErrorCode.DEPARTMENT_NOT_FOUND);
		}
		/*
		 * List<DepartmentLanguagePO> departmentLanguagePOList =
		 * departmentLanguageMapper.selectList(Wrappers.<DepartmentLanguagePO>lambdaQuery(
		 * ) .eq(DepartmentLanguagePO::getDepartmentId, id));
		 */
		return DepartmentTransformer.INSTANCE.toDepartment(departmentPO, Collections.emptyList());
	}

	@Override
	public void updateDepartment(Department department) {
		DepartmentPO departmentPO = DepartmentTransformer.INSTANCE.toDepartmentPO(department);
		this.departmentMapper.update(departmentPO,
				new UpdateWrapper<DepartmentPO>().lambda()
					.eq(DepartmentPO::getId, departmentPO.getId())
					.set(DepartmentPO::getLevelLabel, departmentPO.getLevelLabel())
					.set(DepartmentPO::getManagerId, departmentPO.getManagerId()));
		List<MultiLanguage> names = department.getNames();
		for (MultiLanguage multiLanguage : names) {
			DepartmentLanguagePO departmentLanguagePO = DepartmentTransformer.INSTANCE.toDepartmentLanguagePO(multiLanguage,
					departmentPO.getId());
			DepartmentLanguagePO currentPO = this.departmentLanguageMapper.selectOne(Wrappers.<DepartmentLanguagePO> lambdaQuery()
				.eq(DepartmentLanguagePO::getDepartmentId, departmentPO.getId())
				.eq(DepartmentLanguagePO::getLanguage, multiLanguage.getLanguage()));
			if (Objects.isNull(currentPO)) {
				this.departmentLanguageMapper.insert(departmentLanguagePO);
			}
			else {
				departmentLanguagePO.setId(currentPO.getId());
				this.departmentLanguageMapper.updateById(departmentLanguagePO);
			}
		}
		List<String> languageExistList = names.stream().map(MultiLanguage::getLanguage).collect(Collectors.toList());
		LambdaQueryWrapper<DepartmentLanguagePO> deleteQueryWrapper = Wrappers.lambdaQuery(DepartmentLanguagePO.class)
			.eq(DepartmentLanguagePO::getDepartmentId, departmentPO.getId())
			.notIn(DepartmentLanguagePO::getLanguage, languageExistList);
		this.departmentLanguageMapper.delete(deleteQueryWrapper);
	}

	@Override
	public void changeStatusDepartment(Integer id, Integer status) {
		if (status.equals(DepartmentStatus.ACTIVE.getValue())) {
			// 启用只启用一个部门
			DepartmentPO po = new DepartmentPO();
			po.setStatus(status);
			po.setId(id);
			this.departmentMapper.updateById(po);
		}
		else if (status.equals(DepartmentStatus.INACTIVE.getValue())) {
			// 禁用把子级禁用

			List<Integer> childDepartmentIds = this.getChildDepartmentIds(id, status);
			childDepartmentIds.add(id);
			for (Integer childDepartmentId : childDepartmentIds) {
				DepartmentPO departmentPO = new DepartmentPO();
				departmentPO.setStatus(status);
				departmentPO.setId(childDepartmentId);
				this.departmentMapper.updateById(departmentPO);
			}
		}

	}

	@Override
	public List<Integer> getChildDepartmentIds(Integer id, Integer status) {
		List<Integer> childIds = new ArrayList<>();
		this.collectChildDepartmentIds(id, status, childIds);
		return childIds;
	}

	@Override
	public Integer findUserCountByDepartmentId(List<Integer> departmentIds, Integer userStatus) {
		return this.departmentUserMapper.findUserCountByDepartmentId(departmentIds, userStatus);
	}

	@Override
	public void deleteDepartment(Integer childDepartmentId) {
		LambdaQueryWrapper<DepartmentPO> deleteQueryWrapper = Wrappers.lambdaQuery(DepartmentPO.class)
			.eq(DepartmentPO::getId, childDepartmentId);
		this.departmentMapper.delete(deleteQueryWrapper);
		this.departmentLanguageMapper
			.delete(Wrappers.<DepartmentLanguagePO> lambdaQuery().eq(DepartmentLanguagePO::getDepartmentId, childDepartmentId));
	}

	@Override
	public Map<String, String> checkDuplicationByName(Integer id, List<MultiLanguage> names) {

		Boolean isDiff = Boolean.TRUE;
		Map<String, String> map = new HashMap<>();
		for (MultiLanguage multiLanguage : names) {
			String lang = multiLanguage.getLanguage();
			String name = multiLanguage.getName();
			LambdaQueryWrapper<DepartmentLanguagePO> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(DepartmentLanguagePO::getLanguage, lang).eq(DepartmentLanguagePO::getName, name);
			if (null != id) {
				queryWrapper.ne(DepartmentLanguagePO::getDepartmentId, id);
			}
			List<DepartmentLanguagePO> languagePOS = this.departmentLanguageMapper.selectList(queryWrapper);
			if (languagePOS.size() > 0) {
				isDiff = Boolean.FALSE;
				map.put("name", languagePOS.get(0).getName());
				break;
			}
			else {
				isDiff = Boolean.TRUE;
			}
		}
		map.put("checkPass", isDiff.toString());
		return map;
	}

	private void insertDepartmentUser(DepartmentUserPO po) {
		this.departmentUserMapper.insert(po);
	}

	@Override
	public void checkLevelLable(Department department) {
		if (department.getLevelLabel() == null) {
			return;
		}
		// 新增
		// 若标签为一级，判断是否有父部门有层级标签，若有都不允许
		// 若标签为二级，允许
		if (Objects.equals(department.getLevelLabel(), 1)) {
			this.checkParentDepartmentExistLevel(department.getParentId());
		}
	}

	@Override
	public void checkUpdateLevelLable(Department department) {
		if (department.getLevelLabel() == null) {
			return;
		}
		// 编辑
		// 若标签为一级，判断是否有父部门有层级标签，或是否有子部门有一级标签，若有则不允许
		// 若标签为二级，判断是否有子部门有一级标签，若有则不允许
		if (Objects.equals(department.getLevelLabel(), 1)) {
			this.checkParentDepartmentExistLevel(department.getParentId());
			this.checkChildDepartmentFirstLevel(department.getId().getId());
		}
		else if (Objects.equals(department.getLevelLabel(), 2)) {
			this.checkChildDepartmentFirstLevel(department.getId().getId());
		}
	}

	@Override
	public void cancelUserPrimaryDept(Integer userId, Integer departmentId) {
		List<DepartmentUserPO> eq = departmentUserMapper
			.selectList(Wrappers.<DepartmentUserPO> lambdaQuery().eq(DepartmentUserPO::getUserId, userId));
		// this.departmentUserMapper.update(Wrappers.<DepartmentUserPO>
		// lambdaUpdate().eq(DepartmentUserPO::getUserId,
		// userId).set(DepartmentUserPO::getIsPrimary, Boolean.FALSE));
		this.departmentUserMapper.update(new UpdateWrapper<DepartmentUserPO>().lambda()
			.eq(DepartmentUserPO::getUserId, userId)
			.eq(DepartmentUserPO::getIsPrimary, Boolean.TRUE)
			.set(DepartmentUserPO::getIsPrimary, Boolean.FALSE));
	}

	@Override
	public Integer insertDepartmentUser(Integer userId, Integer adjustDepartmentId) {
		DepartmentUserPO departmentUserPO = new DepartmentUserPO();
		departmentUserPO.setUserId(userId);
		departmentUserPO.setDepartmentId(adjustDepartmentId);
		departmentUserPO.setIsPrimary(Boolean.FALSE);
		this.departmentUserMapper.insert(departmentUserPO);
		return departmentUserPO.getId();
	}

	@Override
	public void deleteDeptUser(Integer id) {
		this.departmentUserMapper.delete(Wrappers.<DepartmentUserPO> lambdaQuery().eq(DepartmentUserPO::getId, id));
	}

	@Override
	public void setUserPrimaryDept(Integer userId, Integer departmentId) {
		this.departmentUserMapper.update(Wrappers.<DepartmentUserPO> lambdaUpdate()
			.eq(DepartmentUserPO::getDepartmentId, departmentId)
			.eq(DepartmentUserPO::getUserId, userId)
			.set(DepartmentUserPO::getIsPrimary, Boolean.TRUE));
	}

	private void checkParentDepartmentExistLevel(Integer parentId) {
		LambdaQueryWrapper<DepartmentPO> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(DepartmentPO::getId, parentId);
		DepartmentPO parentPo = this.departmentMapper.selectOne(queryWrapper);
		if (Objects.nonNull(parentPo.getLevelLabel())) {
			throw new ErrorCodeException(BizErrorCode.DEPARTMENT_LEVEN_ERROR);
		}
		if (parentPo.getParentId() != 0) {
			this.checkParentDepartmentExistLevel(parentPo.getParentId());
		}
	}

	private void checkChildDepartmentFirstLevel(Integer id) {
		LambdaQueryWrapper<DepartmentPO> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(DepartmentPO::getParentId, id);
		List<DepartmentPO> children = this.departmentMapper.selectList(queryWrapper);
		for (DepartmentPO child : children) {
			if (Objects.equals(child.getLevelLabel(), 1)) {
				throw new ErrorCodeException(BizErrorCode.DEPARTMENT_LEVEN_ERROR);
			}
			this.checkChildDepartmentFirstLevel(child.getId());
		}
	}

	private void collectChildDepartmentIds(Integer id, Integer status, List<Integer> childIds) {
		LambdaQueryWrapper<DepartmentPO> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(DepartmentPO::getParentId, id);
		if (status != null) {
			queryWrapper.eq(DepartmentPO::getStatus, status);
		}

		List<DepartmentPO> children = this.departmentMapper.selectList(queryWrapper);
		for (DepartmentPO child : children) {
			if (!childIds.contains(child.getId())) {
				childIds.add(child.getId());
				this.collectChildDepartmentIds(child.getId(), status, childIds);
			}
		}
	}

}
