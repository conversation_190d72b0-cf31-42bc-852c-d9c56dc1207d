package com.renpho.erp.smc.infrastructure.persistence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.OumDingTalkUser;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 参数配置 Mapper.
 *
 * <AUTHOR>
 */
public interface OumDingTalkUserMapper extends BaseMapper<OumDingTalkUser> {

	@Update("""
			UPDATE oum_dingtalk_user SET sys_user_id = #{userId} WHERE userid = #{dingUserid}
			""")
	void updateDingUserId(@Param("dingUserid") String dingUserid, @Param("userId") long userId);

	@Update("""
			UPDATE oum_dingtalk_user SET sys_user_id = NULL WHERE sys_user_id = #{uid}
			 """)
	void resetDingUid(long uid);

	/**
	 * 查询没有被占用的钉钉列表
	 * @return list ok
	 */
	List<OumDingTalkUser> selectNotUserId(@Param("thirdUserId") String thirdUserId);

}
