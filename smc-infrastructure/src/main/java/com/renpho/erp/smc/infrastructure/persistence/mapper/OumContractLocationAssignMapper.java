package com.renpho.erp.smc.infrastructure.persistence.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.OumContractLocationAssignPO;
import com.renpho.erp.smc.infrastructure.persistence.po.OumContractLocationSimple;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 合同地址分配 Mapper.
 */
public interface OumContractLocationAssignMapper extends BaseMapper<OumContractLocationAssignPO> {

	@Select("""
			 select ls.id, ls.user_id, l.`status`, l.id as contractId,
			 JSON_UNQUOTE(JSON_EXTRACT( l.LANGUAGE, '$."${language}".name' )) AS location
			 from oum_contract_location_assign ls
			    left join oum_contract_location l
			    on ls.contract_location_id = l.id
			    where ls.is_deleted = 0
			    and ls.user_id = #{uid}
			""")
	List<OumContractLocationSimple> getUserContractList(@Param("uid") int uid, @Param("language") String language);

	@Delete("""
			delete from oum_contract_location_assign where id = #{id}
			""")
	void delById(int id);

}
