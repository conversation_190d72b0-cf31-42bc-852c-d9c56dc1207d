package com.renpho.erp.smc.infrastructure.persistence.dto.message;

import com.renpho.karma.dto.PageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
public class MessagePageQuery extends PageQuery implements Serializable {

	/**
	 * 消息ID
	 */
	private String messageId;

	/**
	 * 消息接收人用户ID
	 */
	private Integer userId;

	/**
	 * 消息类型 TEXT（文本）
	 */
	private String type;

	/**
	 * 消息标题
	 */
	private String title;

	/**
	 * 消息内容
	 */
	private String content;

	/**
	 * 系统名称
	 */
	private String service;

	/**
	 * 功能模块
	 */
	private String module;

	/**
	 * 已读状态 0未读 1已读
	 */
	private Integer isRead;

	private LocalDateTime createTimeStart;

	private LocalDateTime createTimeEnd;

	@Serial
	private static final long serialVersionUID = 1L;

}
