package com.renpho.erp.smc.infrastructure.persistence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.OumRoleUserPO;

import org.apache.ibatis.annotations.Delete;

/**
 * 参数配置 Mapper.
 *
 * <AUTHOR>
 */

public interface OumRoleUserMapper extends BaseMapper<OumRoleUserPO> {

	@Delete("""
			DELETE from oum_role_user where user_id = #{uid}
			""")
	void deleteByUid(int uid);

}
