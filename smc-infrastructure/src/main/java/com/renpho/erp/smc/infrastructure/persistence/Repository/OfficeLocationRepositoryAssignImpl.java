package com.renpho.erp.smc.infrastructure.persistence.Repository;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.renpho.erp.smc.domain.organizationmanagement.office.OfficeLocationAssign;
import com.renpho.erp.smc.domain.organizationmanagement.office.OfficeLocationAssignRepository;
import com.renpho.erp.smc.exception.BizErrorCode;
import com.renpho.erp.smc.infrastructure.persistence.mapper.OumOfficeLocationAssignMapper;
import com.renpho.erp.smc.infrastructure.persistence.mapper.OumUserInfoMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.OumOfficeLocationAssignPO;
import com.renpho.erp.smc.infrastructure.persistence.po.OumUserInfoPO;
import com.renpho.erp.smc.infrastructure.persistence.po.transformer.OfficeLocationAssignTransformer;
import com.renpho.karma.exception.ErrorCodeException;

import lombok.RequiredArgsConstructor;

@Repository
@RequiredArgsConstructor
public class OfficeLocationRepositoryAssignImpl implements OfficeLocationAssignRepository {

	private final OumOfficeLocationAssignMapper oumOfficeLocationAssignMapper;

	private final OumUserInfoMapper oumUserInfoMapper;

	@Override
	public OfficeLocationAssign findById(Integer id) {
		OumOfficeLocationAssignPO officeLocationAssignPO = this.oumOfficeLocationAssignMapper.selectById(id);
		if (Objects.isNull(officeLocationAssignPO)) {
			throw new ErrorCodeException(BizErrorCode.OFFICE_ASSIGN_NOT_FOUND);
		}
		return OfficeLocationAssignTransformer.INSTANCE.toOfficeLocationAssign(officeLocationAssignPO);
	}

	@Override
	public void updateOfficeLocationAssign(OfficeLocationAssign officeLocationAssignR) {
		OumOfficeLocationAssignPO officeLocationAssignPO = OfficeLocationAssignTransformer.INSTANCE
			.toOfficeLocationAssignPO(officeLocationAssignR);
		this.oumOfficeLocationAssignMapper.updateById(officeLocationAssignPO);
	}

	@Override
	public void checkDuplication(Integer locationId, String effectiveDate, List<Integer> userIds) {
		List<OumOfficeLocationAssignPO> assignPoList = this.oumOfficeLocationAssignMapper
			.selectList(Wrappers.<OumOfficeLocationAssignPO> lambdaQuery()
				.eq(OumOfficeLocationAssignPO::getEffectiveDate, Timestamp.valueOf(effectiveDate + " 00:00:00"))
				.in(OumOfficeLocationAssignPO::getUserId, userIds));
		if (assignPoList.size() > 0) {
			List<Integer> userList = assignPoList.stream().map(OumOfficeLocationAssignPO::getUserId).collect(Collectors.toList());
			List<String> userNameList = this.oumUserInfoMapper
				.selectList(Wrappers.<OumUserInfoPO> lambdaQuery().in(OumUserInfoPO::getId, userList))
				.stream()
				.map(OumUserInfoPO::getName)
				.collect(Collectors.toList());
			String userNameStr = String.join(",", userNameList);
			throw new ErrorCodeException(BizErrorCode.COST_CENTER_ASSIGN_FOUND, userNameStr);
		}
	}

	@Override
	public void checkUpdateDuplication(OfficeLocationAssign officeLocationAssign) {
		OumOfficeLocationAssignPO currentPO = this.oumOfficeLocationAssignMapper.selectById(officeLocationAssign.getId().getId());
		List<OumOfficeLocationAssignPO> oumOfficeLocationAssignPOPOList = this.oumOfficeLocationAssignMapper
			.selectList(Wrappers.<OumOfficeLocationAssignPO> lambdaQuery()
				.eq(OumOfficeLocationAssignPO::getEffectiveDate, Timestamp.valueOf(officeLocationAssign.getEffectiveDate()))
				.eq(OumOfficeLocationAssignPO::getUserId, currentPO.getUserId())
				.ne(OumOfficeLocationAssignPO::getId, officeLocationAssign.getId().getId()));
		if (oumOfficeLocationAssignPOPOList.size() > 0) {
			List<Integer> userList = oumOfficeLocationAssignPOPOList.stream()
				.map(OumOfficeLocationAssignPO::getUserId)
				.collect(Collectors.toList());
			List<String> userNameList = this.oumUserInfoMapper
				.selectList(Wrappers.<OumUserInfoPO> lambdaQuery().in(OumUserInfoPO::getId, userList))
				.stream()
				.map(OumUserInfoPO::getName)
				.collect(Collectors.toList());
			String userNameStr = String.join(",", userNameList);
			throw new ErrorCodeException(BizErrorCode.OFFICE_ASSIGN_FOUND, userNameStr);
		}
	}

	@Override
	public List<Integer> saveOfficeLocationAssignList(List<OfficeLocationAssign> officeLocationAssignList) {
		List<Integer> ids = new ArrayList<>();
		List<OumOfficeLocationAssignPO> poList = OfficeLocationAssignTransformer.INSTANCE
			.toOfficeLocationAssignPOList(officeLocationAssignList);
		for (OumOfficeLocationAssignPO po : poList) {
			this.oumOfficeLocationAssignMapper.insert(po);
			ids.add(po.getId());
		}
		return ids;
	}

	@Override
	public Integer saveOfficeLocationAssign(OfficeLocationAssign officeLocationAssign) {
		OumOfficeLocationAssignPO po = OfficeLocationAssignTransformer.INSTANCE.toOfficeLocationAssignPO(officeLocationAssign);
		this.oumOfficeLocationAssignMapper.insert(po);
		return po.getId();
	}

}
