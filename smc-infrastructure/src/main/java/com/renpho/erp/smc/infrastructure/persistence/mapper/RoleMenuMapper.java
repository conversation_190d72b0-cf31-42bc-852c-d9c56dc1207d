package com.renpho.erp.smc.infrastructure.persistence.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.RoleMenuPO;

import java.util.List;
import java.util.Set;

/**
 * 角色菜单 Mapper.
 *
 * <AUTHOR>
 */
public interface RoleMenuMapper extends BaseMapper<RoleMenuPO> {

	/**
	 * 角色菜单列表
	 * @param roleId 角色id
	 * @return List
	 */
	default List<RoleMenuPO> listByRoleId(Integer roleId) {
		return selectList(new LambdaQueryWrapper<RoleMenuPO>().eq(RoleMenuPO::getRoleId, roleId));
	}

	/**
	 * 角色菜单列表
	 * @param roleIdSet 角色id
	 * @return List
	 */
	default List<RoleMenuPO> listByRoleIdSet(Set<Integer> roleIdSet) {
		return selectList(new LambdaQueryWrapper<RoleMenuPO>().in(RoleMenuPO::getRoleId, roleIdSet));
	}

}
