package com.renpho.erp.smc.infrastructure.persistence.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.renpho.erp.smc.infrastructure.persistence.po.CostCenterAssignPO;

public interface CostCenterAssignMapper extends BaseMapper<CostCenterAssignPO> {

	int deleteByPrimaryKey(Integer id);

	@Override
	int insert(CostCenterAssignPO record);

	int insertSelective(CostCenterAssignPO record);

	// CostCenterAssignPO selectByPrimaryKey(Integer id);

	int updateByPrimaryKeySelective(CostCenterAssignPO record);

	int updateByPrimaryKey(CostCenterAssignPO record);

	IPage<CostCenterAssignPO> pageCostCenterAssign(IPage<CostCenterAssignPO> page, @Param("param") CostCenterAssignPO costCenterAssignPO,
			String language);

	List<CostCenterAssignPO> selectExistUser(@Param("userIds") List<Integer> userIds, @Param("id") Integer id,
			@Param("userId") Integer userId);

	CostCenterAssignPO getCostCenterAssignDetail(Integer id);

}