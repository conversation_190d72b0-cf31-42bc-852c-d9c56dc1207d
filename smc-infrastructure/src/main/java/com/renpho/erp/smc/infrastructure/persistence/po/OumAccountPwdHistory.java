package com.renpho.erp.smc.infrastructure.persistence.po;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * @description: 账号密码历史表
 * @author: doctor
 * @date: 2024/09/23
 * @version: 1.0.0
 */
@Data
@TableName("oum_account_pwd_history")
public class OumAccountPwdHistory implements Serializable {

	/** 多少天没修改 */
	public static final String CHANGE_DATE = "CHANGE_DATE";

	private static final long serialVersionUID = 1L;

	/** 创建人ID **/
	private Integer createBy;

	/** 创建时间 **/
	private LocalDateTime createTime;

	/** 主键 **/
	private Long id;

	/** 是否删除：0 否、1 是；默认 0 **/
	private Integer isDeleted;

	/** 密码 **/
	private String pwd;

	/** 用户ID **/
	private Integer userId;

}
