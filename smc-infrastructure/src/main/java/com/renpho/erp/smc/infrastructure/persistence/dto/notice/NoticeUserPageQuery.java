package com.renpho.erp.smc.infrastructure.persistence.dto.notice;

import com.renpho.karma.dto.PageQuery;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 系统公告表
 *
 * <AUTHOR>
 * @date 2025/03/25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NoticeUserPageQuery extends PageQuery implements Serializable {

	/**
	 * 标题
	 */
	private String title;

	/**
	 * 发布时间 开始
	 */
	private LocalDateTime publishedTimeBegin;

	/**
	 * 发布时间 结束
	 */
	private LocalDateTime publishedTimeEnd;

	@Serial
	private static final long serialVersionUID = 1L;

}