package com.renpho.erp.smc.infrastructure.persistence.mapper;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.renpho.erp.smc.infrastructure.persistence.po.RolePO;

import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 角色 Mapper.
 *
 * <AUTHOR>
 */
public interface RoleMapper extends BaseMapper<RolePO> {

	/**
	 * 通过角色名称查询条数
	 * @param name 角色名称
	 * @return 条数
	 */
	default RolePO getOneByName(String name) {
		return this.selectOne(Wrappers.<RolePO> lambdaQuery().eq(RolePO::getName, name));
	}

	@Select("""
			select u.id, orl.name as name, u.status
			    from oum_role u
				 left join oum_role_language orl on orl.role_id = u.id and orl.is_deleted=0 and orl.language=#{language}
				  where u.is_deleted = 0
				  and u.status = 1
			""")
	@MapKey("name")
	Map<String, RolePO> getMapRoleName(@Param("language") String language);

	/**
	 * 通过角色名称模糊查询
	 * @param name 角色名称
	 * @return List
	 */
	default List<RolePO> listByNameLike(String name) {
		LambdaQueryWrapper<RolePO> wrapper = Wrappers.<RolePO> lambdaQuery().like(StringUtils.isNotBlank(name), RolePO::getName, name);
		return this.selectList(wrapper);
	}

	@Select("""
			 <script>
			select u.id, orl.name as name, u.status,u.label,
			 CONCAT('[', GROUP_CONCAT('{"name":"',orll.name,'","language":"' ,orll.language,'"}'),']') as nameStr
			    from oum_role u
			    left join oum_role_language orl on orl.role_id = u.id and orl.is_deleted=0 and orl.language=#{language}
			    left join oum_role_language orll on orll.role_id =u.id and orll.is_deleted=0
				  where u.is_deleted = 0
				  <if test="param != null and param!=''">
			               and orl.name  LIKE CONCAT('%', #{param}, '%')
			           </if>
			           group by  u.id, orl.name, u.status
				   </script>
			""")
	List<RolePO> selectNameList(@Param("param") String name, @Param("language") String language);

}
