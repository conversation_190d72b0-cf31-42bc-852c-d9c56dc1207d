package com.renpho.erp.smc.infrastructure.persistence.Repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.smc.domain.organizationmanagement.office.OfficeLocationLanguageRepository;
import com.renpho.erp.smc.infrastructure.persistence.mapper.OfficeLocationLanguageMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.OfficeLocationLanguagePO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class OfficeLocationLanguageRepositoryImpl extends ServiceImpl<OfficeLocationLanguageMapper, OfficeLocationLanguagePO>
		implements OfficeLocationLanguageRepository {

}
