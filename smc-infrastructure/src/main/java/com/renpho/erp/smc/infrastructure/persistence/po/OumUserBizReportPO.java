package com.renpho.erp.smc.infrastructure.persistence.po;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.StatusPO;

import lombok.Data;

/**
 * @description: 用户业务线汇报表
 * @author: doctor
 * @date: 2024/09/18
 * @version: 1.0.0
 */
@Data
@TableName("oum_user_biz_report")
public class OumUserBizReportPO extends StatusPO<Long, Integer, Integer, LocalDateTime, Integer> {

	private static final long serialVersionUID = 1L;

	/** 用户ID **/
	private Integer userId;

	/** 汇报类型 **/
	private String reportType;

	/** 业务编号 **/
	private Integer bizValue;

	/** 业务名称 */
	private String bizName;

	/** 汇报用户ID **/
	private Integer reportUserId;

}
