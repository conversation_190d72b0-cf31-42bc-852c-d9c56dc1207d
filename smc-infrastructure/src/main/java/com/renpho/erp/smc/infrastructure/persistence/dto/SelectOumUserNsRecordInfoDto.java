package com.renpho.erp.smc.infrastructure.persistence.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@TableName(autoResultMap = true)
@Data
public class SelectOumUserNsRecordInfoDto {

	private String id;

	/**
	 *
	 */
	@NotNull(message = "用户id为空")
	private Integer userId;

	/**
	 *
	 */
	private String nsId;

	/**
	 *
	 */
	// @NotEmpty(message = "工号为空")
	private String code;

	/**
	 *
	 */
	// @NotEmpty(message = "手机号为空")
	private String phoneNo;

	/**
	 *
	 */
	@NotEmpty(message = "email为空")
	private String email;

	/**
	 *
	 */
	@NotEmpty(message = "ns用户名称为空")
	private String nsUserName;

	/**
	 *
	 */
	@NotEmpty(message = "ns公司名称为空")
	private String nsCorporationName;

	/**
	 *
	 */
	private Integer isDefaulted;

	/**
	 *
	 */
	private Integer status;

	/**
	 *
	 */
	private String reason;

	@NotNull(message = "部门id为空")
	private Integer departmentId;

	/**
	 *
	 */
	// @NotEmpty(message = "主部门名称为空")
	private String mainDepartmentName;

	/**
	 * 部门全名称
	 */
	// @NotEmpty(message = "部门全名称为空")
	private String allDpartmentName;

	/**
	 *
	 */
	@NotEmpty(message = "岗位名称为空")
	private String positionName;

	@NotNull(message = "公司id为空")
	private Integer corporationId;

	private String dingDingUserId;

}