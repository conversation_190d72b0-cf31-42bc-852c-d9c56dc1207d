package com.renpho.erp.smc.infrastructure.persistence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.ParamConfigPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 参数配置 Mapper.
 *
 * <AUTHOR>
 */
public interface ParamConfigMapper extends BaseMapper<ParamConfigPO> {

	List<ParamConfigPO> selectParamConfigList(String name, String key, Integer status);

	ParamConfigPO getDetailById(Integer id);

	ParamConfigPO getDetailByKey(@Param("key") String key);

}
