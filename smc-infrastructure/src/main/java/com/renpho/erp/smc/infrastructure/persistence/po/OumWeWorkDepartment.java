package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.StatusPO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * @description: 企业微信部门表
 * @author: y<PERSON><PERSON>
 * @date: 2024/09/13
 * @version: 1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("oum_wework_department")
public class OumWeWorkDepartment extends StatusPO<Long, Integer, Integer, LocalDateTime, Integer> {

	@Serial
	private static final long serialVersionUID = 1L;

	/** 名称 **/
	private String name;

	/** 父ID **/
	private Long parentId;

	/**
	 * ERP系统部门id
	 */
	private Integer sysDepartmentId;

}
