package com.renpho.erp.smc.infrastructure.persistence.mapper;

import java.util.Map;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.OumUserFinancialPO;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Select;

/**
 * 参数配置 Mapper.
 *
 * <AUTHOR>
 */

public interface OumUserFinancialMapper extends BaseMapper<OumUserFinancialPO> {

	@Delete("""
			DELETE from oum_user_financial where user_id = #{uid}
			""")
	void deleteByUid(int uid);

	@Select("""
			select * from oum_user_financial where user_id = #{uid}
			""")
	@MapKey("id")
	Map<Integer, OumUserFinancialPO> selectByUid(int uid);

}
