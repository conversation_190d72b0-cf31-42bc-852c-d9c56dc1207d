package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.AlterationPO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@TableName("sys_menu_language")
public class MenuInfoMultilanguagePO extends AlterationPO<Integer, Integer, LocalDateTime, Integer> {

	/**
	 * ` `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键', `menu_id` int unsigned
	 * NOT NULL COMMENT '菜单ID', `language` varchar(15) COLLATE utf8mb4_general_ci NOT NULL
	 * COMMENT '语言', `name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
	 * `create_by` int unsigned NOT NULL COMMENT '创建人ID', `create_time` datetime NOT NULL
	 * COMMENT '创建时间', `update_by` int unsigned NOT NULL COMMENT '更新人ID', `update_time`
	 * datetime NOT NULL COMMENT '更新时间', `is_deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT
	 * '是否删除：0 否、1 是；默认 0',
	 */

	private Integer menuId;

	private String language;

	private String name;

	private Integer isDeleted;

}
