package com.renpho.erp.smc.infrastructure.persistence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.DictTypePO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 字典类型 Mapper.
 *
 * <AUTHOR>
 */
public interface DictTypeMapper extends BaseMapper<DictTypePO> {

	/**
	 * 查询字典类型列表.
	 * @param language 语种
	 * @param name 名称
	 * @return DictTypePO List
	 */
	List<DictTypePO> selectListByName(@Param("language") String language, @Param("name") String name);

	/**
	 * 查询字典类型列表.
	 * @param language 语种
	 * @param type 类型
	 * @return DictTypePO List
	 */
	List<DictTypePO> selectListByType(@Param("language") String language, @Param("type") String type);

	/**
	 * 查询全部字典类型列表.
	 * @param language 语种
	 * @return DictTypePO List
	 */
	List<DictTypePO> selectAllList(@Param("language") String language);

	/**
	 * 查询全部字典类型名称列表.
	 * @param language 语种
	 * @return DictTypePO List
	 */
	List<DictTypePO> selectAllNameList(@Param("language") String language);

	/**
	 * 查询单个字典类型详情.
	 * @param id 字典id
	 * @return DictTypePO List
	 */
	DictTypePO getDetailById(@Param("id") Integer id);

	/**
	 * 查询特定语言的名称.
	 * @param language 字典语言
	 * @return value List
	 */
	DictTypePO selectDictByName(String language, String name);

	/**
	 * 查询字典类型.
	 * @param type 字典类型
	 * @return DictTypePO po
	 */
	DictTypePO selectDictByType(String type);

	/**
	 * 排除id查询字典类型.
	 * @param type 字典类型
	 * @param id 字典id
	 * @return DictTypePO po
	 */
	DictTypePO selectDictByTypeExcludeId(String type, Integer id);

	/**
	 * 排除id查询字典名称.
	 * @param language 字典语言
	 * @param name 字典名称
	 * @param id 字典id
	 * @return DictTypePO po
	 */
	DictTypePO selectDictByNameExcludeId(String language, String name, Integer id);

}
