package com.renpho.erp.smc.infrastructure.persistence.dto.index;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户时钟配置表
 *
 * <AUTHOR>
 * @date 2025/03/26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserClockSettingVo implements Serializable {

	/**
	 * 主键
	 */
	private Integer id;

	/**
	 * 用户ID
	 */
	private Integer userId;

	/**
	 * 时区ID
	 */
	private Integer timezoneId;

	/**
	 * 创建人ID
	 */
	private Integer createBy;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新人ID
	 */
	private Integer updateBy;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 是否删除：0 否、1 是；默认 0
	 */
	private Integer isDeleted;

	/**
	 * 当前时间
	 */
	private LocalDateTime nowDate;

	/**
	 * 时区
	 */
	private String timeZoneNo;

	/**
	 * 时区名
	 */
	private String timeZoneNm;

	/**
	 * 城市名英文
	 */
	private String cityEn;

	/**
	 * 城市名中文
	 */
	private String cityCn;

	@Serial
	private static final long serialVersionUID = 1L;

}