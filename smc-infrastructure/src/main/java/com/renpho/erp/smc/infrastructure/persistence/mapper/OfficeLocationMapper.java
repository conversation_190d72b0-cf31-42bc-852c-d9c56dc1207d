package com.renpho.erp.smc.infrastructure.persistence.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.renpho.erp.smc.infrastructure.persistence.po.OfficeLocationPO;

/**
 * 参数配置 Mapper.
 *
 * <AUTHOR>
 */
public interface OfficeLocationMapper extends BaseMapper<OfficeLocationPO> {

	@Delete("""
			DELETE from oum_office_location where user_id = #{uid}
			""")
	void deleteByUid(int uid);

	IPage<OfficeLocationPO> pageOfficeLocation(IPage<OfficeLocationPO> page, @Param("param") OfficeLocationPO po,
			@Param("language") String language);

	List<OfficeLocationPO> listOfficeLocation(@Param("param") OfficeLocationPO officeLocationPO, @Param("language") String language);

}
