package com.renpho.erp.smc.infrastructure.persistence.mapper;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.RoleDataPermissionPO;

/**
 * 角色数据权限 Mapper.
 *
 * <AUTHOR>
 */
public interface RoleDataPermissionMapper extends BaseMapper<RoleDataPermissionPO> {

	/**
	 * 角色数据权限列表
	 * @param roleId 角色id
	 * @return List
	 */
	default List<RoleDataPermissionPO> listByRoleId(Integer roleId) {
		return selectList(new LambdaQueryWrapper<RoleDataPermissionPO>().eq(RoleDataPermissionPO::getRoleId, roleId));
	}

	/**
	 * 角色数据权限列表
	 * @param roleIdSet 角色id
	 * @return List
	 */
	default List<RoleDataPermissionPO> listByRoleIdSet(Set<Integer> roleIdSet) {
		return selectList(new LambdaQueryWrapper<RoleDataPermissionPO>().in(RoleDataPermissionPO::getRoleId, roleIdSet));
	}

	/**
	 * 获取角色id集合
	 * @param scopeType 数据范围类型，1 本人及下级、2 自定义
	 * @param setType 设置类型，0 无区分、1 按部门、2 按员工，若scope_type=1，set_type=0
	 * @param bizValueSet 业务编码集合，来源 department_id、user_id，若scope_type=1，biz_value=0
	 * @return 角色id集合
	 */
	default Set<Integer> getRoleIdSetByTypeAndBizValue(Integer scopeType, Integer setType, Set<Integer> bizValueSet) {
		List<RoleDataPermissionPO> roleDataPermissionList = selectList(
				new LambdaQueryWrapper<RoleDataPermissionPO>().eq(RoleDataPermissionPO::getScopeType, scopeType)
					.eq(RoleDataPermissionPO::getSetType, setType)
					.in(RoleDataPermissionPO::getBizValue, bizValueSet)
					.select(RoleDataPermissionPO::getRoleId));
		return roleDataPermissionList.stream().map(RoleDataPermissionPO::getRoleId).collect(Collectors.toSet());
	}

}
