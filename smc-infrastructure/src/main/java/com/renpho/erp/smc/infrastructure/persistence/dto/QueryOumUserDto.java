package com.renpho.erp.smc.infrastructure.persistence.dto;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class QueryOumUserDto {

	private Integer userId;

	/** 用户的名称 */
	private String name;

	private String language;

	private Integer status;

	private String roleLabel;

	/**
	 * 部门ID
	 */
	private String departmentId;

	/**
	 * 用户ID集合
	 */
	private List<Integer> userIdList;

}
