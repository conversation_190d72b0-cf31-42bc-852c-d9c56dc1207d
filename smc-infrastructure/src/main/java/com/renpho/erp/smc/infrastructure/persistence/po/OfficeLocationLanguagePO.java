package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.AlterationPO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@TableName("oum_office_location_language")
public class OfficeLocationLanguagePO extends AlterationPO<Integer, Integer, LocalDateTime, Integer> implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	// @TableId(type = IdType.AUTO)
	// private Integer id;

	/**
	 * 办公地点ID
	 */
	private Integer officeLocationId;

	/**
	 * 语言
	 */
	private String language;

	/**
	 * 名称
	 */
	private String name;

	/**
	 * 创建人ID
	 */
	// private Integer createBy;

	/**
	 * 创建时间
	 */
	// private LocalDateTime createTime;

	/**
	 * 更新人
	 */
	// private Integer updateBy;

	/**
	 * 更新时间
	 */
	// private LocalDateTime updateTime;

	/** 是否删除：0 否、1 是；默认 0 **/
	// private Integer isDeleted;

}
