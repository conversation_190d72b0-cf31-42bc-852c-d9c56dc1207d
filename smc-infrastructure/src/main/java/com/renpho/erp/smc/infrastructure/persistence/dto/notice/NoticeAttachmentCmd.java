package com.renpho.erp.smc.infrastructure.persistence.dto.notice;

import com.renpho.karma.validation.ValidateGroup;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.validation.annotation.Validated;

import java.io.Serial;
import java.io.Serializable;

/**
 * 系统公告附件表
 *
 * <AUTHOR>
 * @date 2025/03/26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Validated
public class NoticeAttachmentCmd implements Serializable {

	/**
	 * 附件名称
	 */
	@NotBlank(message = "{attachment.name.not.blank}", groups = { ValidateGroup.Insert.class, ValidateGroup.Update.class })
	private String name;

	/**
	 * 附件URL
	 */
	@NotBlank(message = "{attachment.url.not.blank}", groups = { ValidateGroup.Insert.class, ValidateGroup.Update.class })
	private String url;

	@Serial
	private static final long serialVersionUID = 1L;

}