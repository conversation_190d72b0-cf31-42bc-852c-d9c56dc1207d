package com.renpho.erp.smc.infrastructure.dingtalk;

import lombok.Getter;

/**
 * 通知模板枚举
 *
 * <AUTHOR>
 * @date 2025/06/30
 */
@Getter
public enum NotificationTemplateEnum {

	/** 删除DingTalk帐户 */
	DELETE_DINGTALK_ACCOUNT("删除钉钉账号失败",
			"用户姓名：{name}({code}) <br>" + "钉钉用户ID：{thirdBizId} <br>" + "操作时间（北京）：{optTime} <br>" + "预警时间（北京）：{warningTime} <br>"
					+ "删除结果：失败 <br>" + "失败原因：{failedReason}",
			"Fail To Delete Dingtalk Account",
			"User Name：{name}({code}) <br>" + "Dingtalk User ID：{thirdBizId} <br>" + "Opt Time（Beijing Time）：{optTime} <br>"
					+ "Warning time（Beijing Time）：{warningTime} <br>" + "Result：Failed <br>" + "Failed Reason：{failedReason}",
			TypeEnum.User),

	/** 删除WECOM帐户 */
	DELETE_WECOM_ACCOUNT("删除企业微信账号失败",
			"用户姓名：{name}({code}) <br>企业微信ID：{thirdBizId} <br>操作时间（北京）：{optTime} <br>预警时间（北京）：{warningTime} <br>删除结果：失败 <br>失败原因：{failedReason}",
			"Fail To Delete Wecom Account",
			"User Name：{name}({code}) <br>Wecom ID：{thirdBizId} <br>Opt Time（Beijing Time）：{optTime} <br>Warning time（Beijing Time）：{warningTime} <br>Result：Failed <br>Failed Reason：{failedReason}",
			TypeEnum.User),

	/** 更新DingTalk帐户 */
	UPDATE_DINGTALK_ACCOUNT("更新钉钉账号失败",
			"用户姓名：{name}({code}) <br>钉钉用户ID：{thirdBizId} <br>操作时间（北京）：{optTime} <br>预警时间（北京）：{warningTime} <br>更新结果：失败 <br>失败原因：{failedReason}",
			"Failed to Update Dingtalk Account",
			"User Name：{name}({code}) <br>Dingtalk User ID：{thirdBizId} <br>Opt Time（Beijing Time）：{optTime} <br>Warning time（Beijing Time）：{warningTime} <br>Result：Failed <br>Failed Reason：{failedReason}",
			TypeEnum.User),

	/** 更新DingTalk部门 */
	UPDATE_DINGTALK_DEPT("更新钉钉部门失败",
			"部门：{name}({code}) <br>钉钉部门ID：{thirdBizId} <br>操作时间（北京）：{optTime} <br>预警时间（北京）：{warningTime} <br>更新结果：失败 <br>失败原因：{failedReason}",
			"Fail To Update Dingtalk Dept.",
			"Dept：{name}({code}) <br>Dingtalk Dept ID：{thirdBizId} <br>Opt Time（Beijing Time）：{optTime} <br>Warning time（Beijing Time）：{warningTime} <br>Result：Failed <br>Failed Reason：{failedReason}",
			TypeEnum.Depart),

	/** 删除钉钉部门 */
	DELETE_DINGTALK_DEPT("删除钉钉部门失败",
			"部门：{name}({code}) <br>钉钉部门ID：{thirdBizId} <br>操作时间（北京）：{optTime} <br>预警时间（北京）：{warningTime} <br>结果：失败 <br>失败原因：{failedReason}",
			"Fail To Delete Dingtalk Dept.",
			"Dept：{name}({code}) <br>Dingtalk Dept ID：{thirdBizId} <br>Opt Time（Beijing Time）：{optTime} <br>Warning time（Beijing Time）：{warningTime} <br>Result：Failed <br>Failed Reason：{failedReason}",
			TypeEnum.Depart),

	/** 新增钉钉部门 */
	ADD_DINGTALK_DEPT("新增钉钉部门失败",
			"部门：{name}({code}) <br>钉钉部门ID：{thirdBizId} <br>操作时间（北京）：{optTime} <br>预警时间（北京）：{warningTime} <br>结果：失败 <br>失败原因：{failedReason}",
			"Fail To Add Dingtalk Dept.",
			"Dept：{name}({code}) <br>Dingtalk Dept ID：{thirdBizId} <br>Opt Time（Beijing Time）：{optTime} <br>Warning time（Beijing Time）：{warningTime} <br>Result：Failed <br>Failed Reason：{failedReason}",
			TypeEnum.Depart);

	/** ZH标题 */
	private final String zhTitle;

	/** ZH内容 */
	private final String zhContent;

	/** en标题 */
	private final String enTitle;

	/** EN内容 */
	private final String enContent;

	/** 通知类型 */
	private final TypeEnum typeEnum; // 新增 type 字段

	public enum TypeEnum {

		/** 离开 */
		Depart,
		/** 用户 */
		User;

	}

	/**
	 * 通知模板枚举
	 * @param zhTitle ZH标题
	 * @param zhContent ZH内容
	 * @param enTitle en标题
	 * @param enContent EN内容
	 * @param type 通知类型
	 */
	NotificationTemplateEnum(String zhTitle, String zhContent, String enTitle, String enContent, TypeEnum type) {
		this.zhTitle = zhTitle;
		this.zhContent = zhContent;
		this.enTitle = enTitle;
		this.enContent = enContent;
		this.typeEnum = type; // 初始化 type 字段
	}

	/**
	 * 获取标题
	 * @param language 语言
	 * @return {@link String }
	 */
	public String getTitle(String language) {
		if ("en-US".equalsIgnoreCase(language)) {
			return enTitle;
		}
		return zhTitle; // 默认返回中文
	}

	/**
	 * 获取内容
	 * @param language 语言
	 * @return {@link String }
	 */
	public String getContent(String language) {
		if ("en-US".equalsIgnoreCase(language)) {
			return enContent;
		}
		return zhContent; // 默认返回中文
	}

	/**
	 * 获取填充内容
	 * @param language 语言
	 * @param dto 参数
	 * @return {@link String }
	 */
	public String getFilledContent(String language, NotificationTemplateDto dto) {
		String template = getContent(language);
		template = template.replace("{name}", dto.getName());
		template = template.replace("{code}", dto.getCode());
		template = template.replace("{thirdBizId}", dto.getThirdBizId());
		template = template.replace("{optTime}", dto.getOptTime());
		template = template.replace("{warningTime}", dto.getWarningTime());
		template = template.replace("{failedReason}", dto.getFailedReason());
		return template;
	}

	/**
	 * 主要
	 * @param args args
	 */ // 测试示例
	public static void main(String[] args) {
		NotificationTemplateDto params = new NotificationTemplateDto();
		params.setName("Merry");
		params.setCode("FT100097");
		params.setThirdBizId("*****************");
		params.setOptTime("2025-05-12 10:12:25");
		params.setWarningTime("2025-05-12 10:18:25");
		params.setFailedReason("钉钉用户ID不存在");

		// 获取中文通知内容
		String zhTitle = NotificationTemplateEnum.DELETE_DINGTALK_ACCOUNT.getTitle("zh-CN");
		String zhContent = NotificationTemplateEnum.DELETE_DINGTALK_ACCOUNT.getFilledContent("zh-CN", params);
		System.out.println("中文title： <br>" + zhTitle);
		System.out.println("中文通知内容： <br>" + zhContent);

		// 获取英文通知内容
		String zhTitle2 = NotificationTemplateEnum.DELETE_DINGTALK_ACCOUNT.getTitle("en-US");
		String zhContent2 = NotificationTemplateEnum.DELETE_DINGTALK_ACCOUNT.getFilledContent("en-US", params);
		System.out.println("中文title： <br>" + zhTitle2);
		System.out.println("中文通知内容： <br>" + zhContent2);

	}

}