package com.renpho.erp.smc.infrastructure.persistence.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.smc.infrastructure.persistence.mapper.RoleDataPermissionMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.RoleDataPermissionPO;
import org.springframework.stereotype.Service;

/**
 * 角色数据权限持久层Service 场景：使用Mybatis-Plus的IService批量方法，例：IService#saveBatch()方法.
 *
 * <AUTHOR>
 */
@Service
public class RoleDataPermissionServiceImpl extends ServiceImpl<RoleDataPermissionMapper, RoleDataPermissionPO> {

}
