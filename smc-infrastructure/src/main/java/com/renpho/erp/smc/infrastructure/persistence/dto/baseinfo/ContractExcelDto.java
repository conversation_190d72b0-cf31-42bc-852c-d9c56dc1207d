package com.renpho.erp.smc.infrastructure.persistence.dto.baseinfo;

import java.io.Serializable;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;

import lombok.Data;

@Data
public class ContractExcelDto implements VO, Serializable {

	private Integer id;

	@Trans(type = TransType.DICTIONARY, key = "status_dict", ref = "statusName")
	private Integer status;

	private String contractName;

	private String statusName;

}
