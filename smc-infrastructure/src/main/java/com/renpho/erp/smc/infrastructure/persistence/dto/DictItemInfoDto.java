package com.renpho.erp.smc.infrastructure.persistence.dto;

import java.util.List;

import com.renpho.erp.smc.infrastructure.persistence.po.DictItemLanguagePO;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class DictItemInfoDto {

	private Integer dictId;

	private String dictType;

	private String dictKey;

	private String remark;

	private Integer sort;

	private Integer isDeleted;

	private String name;

	private List<DictItemLanguagePO> names;

}
