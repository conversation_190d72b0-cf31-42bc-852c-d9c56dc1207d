package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.AlterationPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户时钟配置表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_user_clock_setting")
public class UserClockSettingPO extends AlterationPO<Integer, Integer, LocalDateTime, Integer> implements Serializable {

	/**
	 * 用户ID
	 */
	@TableField(value = "user_id")
	private Integer userId;

	/**
	 * 时区ID
	 */
	@TableField(value = "timezone_id")
	private Integer timezoneId;

	@Serial
	private static final long serialVersionUID = 1L;

}