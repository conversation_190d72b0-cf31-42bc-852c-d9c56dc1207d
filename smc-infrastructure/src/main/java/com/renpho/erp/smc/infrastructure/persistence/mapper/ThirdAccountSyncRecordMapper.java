package com.renpho.erp.smc.infrastructure.persistence.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.renpho.erp.data.permission.aop.annotation.DataPermission;
import com.renpho.erp.smc.infrastructure.persistence.po.ThirdAccountSyncRecordPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * 第三方账号同步记录Mapper
 *
 * <AUTHOR>
 * @Date 2025/6/26 11:46
 **/
public interface ThirdAccountSyncRecordMapper extends BaseMapper<ThirdAccountSyncRecordPo> {

	@Override
	@DataPermission
	List<ThirdAccountSyncRecordPo> selectList(IPage<ThirdAccountSyncRecordPo> page,
			@Param("ew") Wrapper<ThirdAccountSyncRecordPo> queryWrapper);

}
