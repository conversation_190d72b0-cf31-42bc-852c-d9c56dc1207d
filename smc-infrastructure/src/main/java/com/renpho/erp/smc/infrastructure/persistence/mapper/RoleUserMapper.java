package com.renpho.erp.smc.infrastructure.persistence.mapper;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.renpho.erp.smc.infrastructure.persistence.dto.RoleNameDto;
import com.renpho.erp.smc.infrastructure.persistence.po.RoleUserPO;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 角色用户 Mapper.
 *
 * <AUTHOR>
 */
public interface RoleUserMapper extends BaseMapper<RoleUserPO> {

	/**
	 * 查询角色用户列表
	 * @param roleId 角色id
	 * @return List
	 */
	default List<RoleUserPO> listByRoleId(Integer roleId) {
		return this.selectList(Wrappers.<RoleUserPO> lambdaQuery().eq(RoleUserPO::getRoleId, roleId));
	}

	/**
	 * 角色用户分页
	 * @param page 分页参数
	 * @param roleUser 查询条件
	 * @return IPage
	 */
	IPage<RoleUserPO> page(IPage<RoleUserPO> page, @Param("param") RoleUserPO roleUser);

	/**
	 * 查询角色用户列表
	 * @param userId 用户id
	 * @return List
	 */
	default List<RoleUserPO> listByUserId(Integer userId) {
		return this.selectList(Wrappers.<RoleUserPO> lambdaQuery().eq(RoleUserPO::getUserId, userId));
	}

	/**
	 * 查询角色用户列表
	 * @param userIdSet 用户id集合
	 * @return List
	 */
	default List<RoleUserPO> listByUserIdSet(Set<Integer> userIdSet) {
		return this.selectList(Wrappers.<RoleUserPO> lambdaQuery().in(RoleUserPO::getUserId, userIdSet));
	}

	/**
	 * 获取用户id集合
	 * @param roleIdSet 角色id集合
	 * @return Set 用户id集合
	 */
	default Set<Integer> getByRoleIdSet(Set<Integer> roleIdSet) {
		List<RoleUserPO> roleUserList = this
			.selectList(Wrappers.<RoleUserPO> lambdaQuery().select(RoleUserPO::getUserId).in(RoleUserPO::getRoleId, roleIdSet));
		return roleUserList.stream().filter(Objects::nonNull).map(RoleUserPO::getUserId).collect(Collectors.toSet());
	}

	/**
	 * 角色用户列表
	 * @param roleId 角色id
	 * @param userIdSet 用户id集合
	 * @return List
	 */
	default List<RoleUserPO> listRoleUser(Integer roleId, Set<Integer> userIdSet) {
		return this.selectList(Wrappers.<RoleUserPO> lambdaQuery().eq(RoleUserPO::getRoleId, roleId).in(RoleUserPO::getUserId, userIdSet));
	}

	@Select("""
			SELECT
				rl.name as roleName,
				r.id as roleId,
				ru.user_id
			FROM
				oum_role r
				LEFT JOIN oum_role_user ru ON r.id = ru.role_id
				LEFT JOIN oum_role_language rl ON r.id = rl.role_id
			WHERE
			 			r.is_deleted = 0
				and ru.is_deleted = 0
				and rl.is_deleted = 0
				and ru.user_id = #{userId}
				and rl.language = #{language}
			""")
	List<RoleNameDto> getUserRoleNameList(int userId, String language);

	/**
	 * 角色用户列表
	 * @param roleIdSet 角色id集合
	 * @return List
	 */
	default List<RoleUserPO> listByRoleIdSet(Set<Integer> roleIdSet) {
		return this.selectList(Wrappers.<RoleUserPO> lambdaQuery().in(RoleUserPO::getRoleId, roleIdSet));
	}

}
