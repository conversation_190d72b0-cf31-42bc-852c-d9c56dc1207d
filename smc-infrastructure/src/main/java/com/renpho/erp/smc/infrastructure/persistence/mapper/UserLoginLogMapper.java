package com.renpho.erp.smc.infrastructure.persistence.mapper;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.renpho.erp.smc.infrastructure.persistence.dto.UserLogQuery;
import com.renpho.erp.smc.infrastructure.persistence.po.UserLoginLogPO;

import org.apache.ibatis.annotations.Select;

/**
 * 参数配置 Mapper.
 *
 * <AUTHOR>
 */
public interface UserLoginLogMapper extends BaseMapper<UserLoginLogPO> {

	@Select("""
			<script>
				SELECT
					ull.id,
					ull.user_id AS userId,
					ull.login_ip,
					ull.login_time,
					ull.login_addr,
					ull.browser_info,
					ull.sysos_info,
					ull.login_status,
					ull.create_by,
					ull.create_time,
					ull.update_by,
					ull.update_time,
					ull.login_info,
					IFNULL( JSON_UNQUOTE( JSON_EXTRACT( ull.browser_info, CONCAT( '$.system' ))), JSON_UNQUOTE( JSON_EXTRACT( ull.browser_info, CONCAT( '$.System' )))) AS browserSystem,
					JSON_UNQUOTE( JSON_EXTRACT( ull.browser_info, CONCAT( '$.Fingerprint' ))) AS browserFingerprint,
					JSON_UNQUOTE( JSON_EXTRACT( ull.sysos_info, CONCAT( '$.System' ))) AS operatingSystem,
					JSON_UNQUOTE( JSON_EXTRACT( ull.sysos_info, CONCAT( '$.Resolution' ))) AS screenResolution,
					su.NAME AS employeeName,
					su.CODE AS employeeID
				FROM
					oum_user_login_log ull
					LEFT JOIN oum_user_info su ON ull.user_id = su.id
				<where>
					<if test="query.userId != null and query.userId > 0">
						ull.user_id = #{query.userId}
					</if>
					<if test="query.name != null and query.name !='' ">
						and  su.name like concat('%', #{query.name}, '%')
					</if>
					<if test="query.code != null and query.code !='' ">
						and  su.code like concat('%', #{query.code}, '%')
					</if>
					<if test="query.loginStatus != null">
						and ull.login_status = #{query.loginStatus}
					</if>
					<if test="query.loginStartTime!=null and query.loginStartTime!=''">
						and ull.login_time &gt;= CONCAT(#{query.loginStartTime},' 00:00:00')
					</if>
					<if test="query.loginEndTime!=null and query.loginEndTime!=''">
						and ull.login_time &lt;= concat(#{query.loginEndTime},' 23:59:59')
					</if>
					</where>
					order by ull.login_time desc
				</script>
			""")
	Page<UserLoginLogPO> selectListByPage(Page<UserLoginLogPO> page, UserLogQuery query);

	List<UserLoginLogPO> selectListByIds(Integer pageNum, Integer pageSize, List<Integer> userIds, String loginStatus,
			String loginStartTime, String loginEndTime);

	Long selectCountByIds(List<Integer> userIds, String loginStatus, String loginStartTime, String loginEndTime);

	/**
	 * 判断是否新设备
	 * @param uid uid
	 * @param devname uid
	 * @return int
	 */
	@Select("""
			select count(0) from oum_user_login_log where browser_info ->'$.Fingerprint' = #{devname} and user_id = #{uid}
				and login_status = 'success'
			""")
	int getNewDeivce(int uid, String devname);

	/**
	 * 最近密码错误多少次
	 * @param uid uid
	 * @param lastTime ls
	 * @return int
	 */
	@Select("""
			select count(0) from (
				select * from oum_user_login_log  where user_id  = #{uid}
					and (login_error = 'bad_credentials' or login_status = 'success' )
					and login_time > #{lastTime}
					ORDER BY id desc limit #{times}
			) t where t.login_error = 'bad_credentials'
					""")
	int getLast5PwdError(int uid, int times, Date lastTime);

}
