package com.renpho.erp.smc.infrastructure.persistence.mapper;

import java.util.List;
import java.util.Set;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.RoleFieldPermissionPO;

/**
 * 角色字段脱敏权限 Mapper.
 *
 * <AUTHOR>
 */
public interface RoleFieldPermissionMapper extends BaseMapper<RoleFieldPermissionPO> {

	/**
	 * 角色字段脱敏权限列表.
	 * @param roleId 角色id
	 * @return List
	 */
	default List<RoleFieldPermissionPO> listByRoleId(Integer roleId) {
		return selectList(new LambdaQueryWrapper<RoleFieldPermissionPO>().eq(RoleFieldPermissionPO::getRoleId, roleId));
	}

	/**
	 * 角色字段脱敏权限列表.
	 * @param roleIdSet 角色id集合
	 * @return List
	 */
	default List<RoleFieldPermissionPO> listByRoleIdSet(Set<Integer> roleIdSet) {
		return selectList(new LambdaQueryWrapper<RoleFieldPermissionPO>().in(RoleFieldPermissionPO::getRoleId, roleIdSet));
	}

}
