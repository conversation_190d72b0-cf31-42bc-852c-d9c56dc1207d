package com.renpho.erp.smc.infrastructure.persistence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.renpho.erp.smc.infrastructure.persistence.po.DepartmentLanguagePO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 部门多语言 Mapper.
 */
public interface DepartmentLanguageMapper extends BaseMapper<DepartmentLanguagePO> {

	int deleteByPrimaryKey(Long id);

	// int insert(DepartmentLanguagePO record);

	int insertSelective(DepartmentLanguagePO record);

	DepartmentLanguagePO selectByPrimaryKey(Long id);

	int updateByPrimaryKeySelective(DepartmentLanguagePO record);

	int updateByPrimaryKey(DepartmentLanguagePO record);

	default Map<Integer, String> getDepartmentPathStr(List<Integer> idList, String language) {
		// 获取当前部门及其所有上级部门的信息
		// 构建部门路径字符串
		if (CollectionUtils.isEmpty(idList)) {
			return Collections.emptyMap();
		}
		return selectList(Wrappers.<DepartmentLanguagePO> lambdaQuery()
			.select(DepartmentLanguagePO::getDepartmentId, DepartmentLanguagePO::getName)
			.in(DepartmentLanguagePO::getDepartmentId, idList)
			.eq(DepartmentLanguagePO::getLanguage, language)).stream()
			.collect(Collectors.toMap(DepartmentLanguagePO::getDepartmentId, DepartmentLanguagePO::getName, (v1, v2) -> v1));

	}

}
