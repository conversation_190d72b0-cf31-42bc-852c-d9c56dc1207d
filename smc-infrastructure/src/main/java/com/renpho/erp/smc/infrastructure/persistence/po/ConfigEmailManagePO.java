package com.renpho.erp.smc.infrastructure.persistence.po;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 邮箱管理表.
 *
 * <AUTHOR>
 * @since 2022-12-13
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("config_email_manage")
public class ConfigEmailManagePO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 帐号
	 */
	private String account;

	/**
	 * 名字
	 */
	private String name;

	/**
	 * 授权码
	 */
	private String authorization;

	/**
	 * 邮箱签名ID
	 */
	@TableField(value = "signature_id", updateStrategy = FieldStrategy.IGNORED)
	private Long signatureId;

	/**
	 * 收件服务器端口号
	 */
	private Integer implPort;

	/**
	 * 收件服务器地址
	 */
	private String implHost;

	/**
	 * 是否启用ssl加密 禁用0启用 1
	 */
	private Boolean implIsSsl;

	/**
	 * 帐号是否启用 禁用0启用 1
	 */
	private Boolean status;

	/**
	 * 1:qq邮箱 2:outlook邮箱 3:gmail邮箱 4:amazon邮箱 5:163邮箱
	 */
	private Integer emailType;

	/**
	 * 发件服务器端口
	 */
	private Integer smtpPort;

	/**
	 * 发件服务器地址
	 */
	private String smtpHost;

	/**
	 * 邮箱配置
	 */
	private String propJson;

	/**
	 * 是否是站内信邮箱
	 */
	private Boolean isAmazonMessages;

	/**
	 * 邮件回复方式 TRUE 自动回复
	 */
	private Boolean enableMailAutoResponder;

	/**
	 * APP反馈邮箱(只能存在一个)
	 */
	private Boolean isAppEmail;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time", fill = FieldFill.INSERT)
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "last_update_time", fill = FieldFill.INSERT_UPDATE)
	private Date lastUpdateTime;

	/**
	 * 创建者
	 */
	@TableField(value = "create_by", fill = FieldFill.INSERT)
	private Long createBy;

	/**
	 * 最后更新者
	 */
	@TableField(value = "last_update_by", fill = FieldFill.INSERT_UPDATE)
	private Long lastUpdateBy;

	@Override
	public String toString() {
		return "ConfigEmailManage{" + "id=" + id + ", account='" + account + '\'' + ", name='" + name + '\'' + ", authorization='"
				+ authorization + '\'' + ", signatureId=" + signatureId + ", implPort=" + implPort + ", implHost='" + implHost + '\''
				+ ", implIsSsl=" + implIsSsl + ", status=" + status + ", emailType=" + emailType + ", stmpPort=" + smtpPort + ", stmpHost='"
				+ smtpHost + '\'' + ", isAmazonMessages=" + isAmazonMessages + ", enableMailAutoResponder=" + enableMailAutoResponder
				+ ", isAppEmail=" + isAppEmail + ", createTime=" + createTime + ", lastUpdateTime=" + lastUpdateTime + ", createBy="
				+ createBy + ", lastUpdateBy=" + lastUpdateBy + '}';
	}

}
