package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.DefaultPO;

import lombok.Data;

/**
 * 角色字段脱敏权限.
 *
 * <AUTHOR>
 */
@Data
@TableName("oum_role_field_permission")
public class RoleFieldPermissionPO extends DefaultPO {

	/**
	 * 角色ID
	 */
	private Integer roleId;

	/**
	 * 字段标识符
	 */
	private String name;

}
