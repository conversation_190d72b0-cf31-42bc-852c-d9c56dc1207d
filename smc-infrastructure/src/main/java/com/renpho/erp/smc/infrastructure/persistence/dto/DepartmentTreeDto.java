package com.renpho.erp.smc.infrastructure.persistence.dto;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class DepartmentTreeDto {

	private Integer id;

	private String code;

	private Integer status;

	/**
	 * 上级部门
	 */
	private Integer parentId;

	/**
	 * 排序，默认 0
	 */
	private Integer sort;

	private String name;

	private List<DepartmentTreeDto> children;

	private List<DepartmentUserTreeDto> users;

	private Integer deleted;

}
