package com.renpho.erp.smc.infrastructure.dingtalk;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @description:
 * @author: doctor
 * @date: 2023/04/27
 * @version: 1.0.0
 */
@Data
public class OumDingTalkUserDto implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/** 系统用户的id **/
	private Long sysUserId;

	/**  **/
	private Long deptId;

	/** 钉钉用户id **/
	private String userid;

	/** 第三方平台id **/
	private String unionid;

	/** 第三方平台openId **/
	private String openId;

	/** 手机号 **/
	private String mobile;

	/** 邮箱 **/
	private String email;

	/** 第三方平台：dingtalk **/
	private String platform;

	private String username;

	/** 第三方平台的用户名称 **/
	private String name;

	/** 第三方平台头像 **/
	private String avatarUrl;

	/** 刷新token **/
	private String refreshToken;

	/** 扩展字段 */
	private String jobNumber;

}
