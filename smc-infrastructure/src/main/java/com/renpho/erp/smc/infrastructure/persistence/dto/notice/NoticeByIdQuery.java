package com.renpho.erp.smc.infrastructure.persistence.dto.notice;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 系统公告表
 *
 * <AUTHOR>
 * @date 2025/03/25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NoticeByIdQuery implements Serializable {

	@NotNull
	private Integer id;

	@Serial
	private static final long serialVersionUID = 1L;

}