package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.AlterationPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 系统公告用户记录表
 *
 * <AUTHOR>
 * @date 2025/03/26
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_notice_user_record")
public class NoticeUserRecordPO extends AlterationPO<Integer, Integer, LocalDateTime, Integer> implements Serializable {

	/**
	 * 公告ID
	 */
	@TableField(value = "notice_id")
	private Integer noticeId;

	/**
	 * 用户ID
	 */
	@TableField(value = "user_id")
	private Integer userId;

	/**
	 * 阅读时间
	 */
	@TableField(value = "read_time")
	private LocalDateTime readTime;

	@Serial
	private static final long serialVersionUID = 1L;

}