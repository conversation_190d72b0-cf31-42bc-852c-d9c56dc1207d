package com.renpho.erp.smc.infrastructure.persistence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.DictTypeLanguagePO;
import org.apache.ibatis.annotations.Param;

/**
 * 字典类型 Mapper.
 *
 * <AUTHOR>
 */
public interface DictTypeLanguageMapper extends BaseMapper<DictTypeLanguagePO> {

	DictTypeLanguagePO selectListByTypeId(@Param("id") Integer id, @Param("language") String language);

}
