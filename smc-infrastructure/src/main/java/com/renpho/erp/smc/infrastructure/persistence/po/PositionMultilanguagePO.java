package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.AlterationPO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/09/12 14:19
 */
@Data
@TableName("oum_position_language")
public class PositionMultilanguagePO extends AlterationPO<Integer, Integer, LocalDateTime, Integer> {

	/**
	 * 类型id
	 */

	private Integer positionId;

	/**
	 * 多语言
	 */
	private String language;

	/**
	 * 多语言名称
	 */
	private String name;

}
