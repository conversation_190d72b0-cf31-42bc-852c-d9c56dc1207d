package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.AlterationPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 系统公告可见范围表
 *
 * <AUTHOR>
 * @date 2025/03/26
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_notice_visibility")
public class NoticeVisibilityPO extends AlterationPO<Integer, Integer, LocalDateTime, Integer> implements Serializable {

	/**
	 * 公告ID
	 */
	@TableField(value = "notice_id")
	private Integer noticeId;

	/**
	 * 可见类型：1 部门、2 用户 VisibilityTypeEnum
	 */
	@TableField(value = "`type`")
	private Integer type;

	/**
	 * 可见对象ID
	 */
	@TableField(value = "visibility_id")
	private Integer visibilityId;

	@Serial
	private static final long serialVersionUID = 1L;

}