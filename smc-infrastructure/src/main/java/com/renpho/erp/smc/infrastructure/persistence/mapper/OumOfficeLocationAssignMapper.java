package com.renpho.erp.smc.infrastructure.persistence.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.renpho.erp.smc.infrastructure.persistence.po.OumOfficeLocationAssignPO;

/**
 * 参数配置 Mapper.
 *
 * <AUTHOR>
 */
public interface OumOfficeLocationAssignMapper extends BaseMapper<OumOfficeLocationAssignPO> {

	@Delete("""
			DELETE from oum_office_location_assign where user_id = #{uid}
			""")
	void deleteByUid(int uid);

	IPage<OumOfficeLocationAssignPO> pageOfficeLocationAssign(IPage<OumOfficeLocationAssignPO> page,
			@Param("param") OumOfficeLocationAssignPO officeLocationAssignPO, @Param("language") String language);

	List<OumOfficeLocationAssignPO> selectExistUser(@Param("userIds") List<Integer> userIds, @Param("id") Integer id,
			@Param("userId") Integer userId);

}
