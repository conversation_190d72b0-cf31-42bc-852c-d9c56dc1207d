package com.renpho.erp.smc.infrastructure.persistence.dto.index;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 用户菜单配置表
 *
 * <AUTHOR>
 * @date 2025/03/26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserMenuSettingCmd implements Serializable {

	/**
	 * 菜单ID
	 */
	@NotBlank
	private Integer menuId;

	/**
	 * 菜单路径
	 */
	@NotNull
	private String path;

	/**
	 * 菜单状态，0启用 1禁用
	 */
	@NotNull
	private Integer menuStatus;

	@Serial
	private static final long serialVersionUID = 1L;

}