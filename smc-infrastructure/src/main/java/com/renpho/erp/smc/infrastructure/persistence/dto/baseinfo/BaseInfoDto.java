package com.renpho.erp.smc.infrastructure.persistence.dto.baseinfo;

import java.util.List;

import lombok.Data;

@Data
public class BaseInfoDto {

	private List<BrandExcelDto> brandExcelDtos;

	private List<CategoryExcelDto> categoryExcelDtos;

	private List<ContractExcelDto> contractExcelDtos;

	private List<CoporationInfoExcelDto> coporationInfoExcelDtos;

	private List<CostCenterExcelDto> centerExcelDtos;

	private List<DeptInfoExcelDto> deptInfoExcelDtos;

	private List<PositionExcelDto> positionExcelDtos;

	private List<RoleExcelDto> roleExcelDtos;

	private List<SalesExcelDto> salesExcelDtos;

}
