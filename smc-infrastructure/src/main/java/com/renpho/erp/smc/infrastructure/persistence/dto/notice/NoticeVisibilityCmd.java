package com.renpho.erp.smc.infrastructure.persistence.dto.notice;

import com.renpho.karma.validation.ValidateGroup;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.validation.annotation.Validated;

import java.io.Serial;
import java.io.Serializable;

/**
 * 系统公告可见范围表
 *
 * <AUTHOR>
 * @date 2025/03/26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Validated
public class NoticeVisibilityCmd implements Serializable {

	/**
	 * 可见类型：1 部门、2 用户 VisibilityTypeEnum
	 */
	@NotNull(message = "{visibility.type.not.null}", groups = { ValidateGroup.Insert.class, ValidateGroup.Update.class })
	private Integer type;

	/**
	 * 可见对象ID
	 */
	@NotNull(message = "{visibility.visibilityId.not.null}", groups = { ValidateGroup.Insert.class, ValidateGroup.Update.class })
	private Integer visibilityId;

	@Serial
	private static final long serialVersionUID = 1L;

}