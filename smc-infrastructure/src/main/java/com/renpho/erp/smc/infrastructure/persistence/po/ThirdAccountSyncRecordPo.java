package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.erp.smc.infrastructure.dingtalk.NotificationTemplateEnum;
import com.renpho.karma.cloud.mybatisplus.po.AlterationPO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 第三方账号同步记录表
 *
 * <AUTHOR>
 * @Date 2025/6/26 11:41
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("oum_third_account_sync_record")
public class ThirdAccountSyncRecordPo extends AlterationPO<Integer, Integer, LocalDateTime, Integer> {

	/**
	 * 账号类型 DingTalk（钉钉）/WeCom（企业微信）
	 */
	private String accountType;

	/**
	 * 变更类型 USER（用户）/DEPT（部门）
	 */
	private String changeType;

	/**
	 * 变更值 变更类型为USER时，为用户ID，变更类型为部门时，为部门ID
	 */
	private String changeTypeValue;

	/**
	 * 变更前值
	 */
	private String changeBeforeValue;

	/**
	 * 变更后值
	 */
	private String changeAfterValue;

	/**
	 * 同步状态 IN（同步中）/SUCCESS（成功）/FAILED（失败）
	 */
	private String syncStatus;

	/**
	 * 操作类型 ADD（新增）/UPDATE（更新）/DELETE（删除）
	 */
	private String operationType;

	/**
	 * 重试次数
	 */
	private Integer retryNum;

	/**
	 * 错误信息
	 */
	private String errorMsg;

	@TableField(exist = false)
	private NotificationTemplateEnum notificationTemplateEnum;

}
