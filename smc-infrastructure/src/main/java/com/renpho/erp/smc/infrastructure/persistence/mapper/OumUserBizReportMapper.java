package com.renpho.erp.smc.infrastructure.persistence.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.OumUserBizReportPO;
import com.renpho.erp.smc.infrastructure.persistence.po.OumUserBizReportSimple;

/**
 * 参数配置 Mapper.
 *
 * <AUTHOR>
 */

public interface OumUserBizReportMapper extends BaseMapper<OumUserBizReportPO> {

	@Delete("""
			DELETE from oum_user_biz_report where user_id = #{uid}
			""")
	void deleteByUid(int uid);

	@Select("""
			SELECT
				br.id,
				br.user_id,
				br.report_user_id,
				u.NAME AS reportUserName,
				u.code AS reportUserCode,
				br.report_type,
				br.biz_name,
				br.biz_value
			FROM
				oum_user_biz_report br
				LEFT JOIN oum_user_info u ON u.id = br.report_user_id
			WHERE
				br.user_id = #{uid}
			""")
	List<OumUserBizReportSimple> getListByUid(int uid);

	@Select("""
			      <script>
			SELECT
				br.id,
				br.user_id,
				br.report_user_id,
				u.NAME AS reportUserName,
				u.code AS reportUserCode,
				br.report_type,
				br.biz_name,
				br.biz_value
			FROM
				oum_user_biz_report br
				LEFT JOIN oum_user_info u ON u.id = br.report_user_id
			WHERE
				br.user_id = #{userId}
				<if test="reportType != null and reportType !='' ">
					and br.report_type = #{reportType}
				</if>
				</script>
			""")
	List<OumUserBizReportSimple> selectBizUserByType(Integer userId, String reportType);

}
