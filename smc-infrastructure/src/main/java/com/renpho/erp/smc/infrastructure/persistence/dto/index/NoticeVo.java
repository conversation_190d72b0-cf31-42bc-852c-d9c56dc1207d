package com.renpho.erp.smc.infrastructure.persistence.dto.index;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.renpho.erp.smc.infrastructure.persistence.dto.notice.NoticeAttachmentVo;
import com.renpho.erp.smc.infrastructure.persistence.dto.notice.NoticeVisibilityVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统公告表
 *
 * <AUTHOR>
 * @date 2025/03/25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NoticeVo implements Serializable, VO {

	/**
	 * 主键
	 */
	private Integer id;

	/**
	 * ：类型：1-通知公告、2-集团要闻', NoticeTypeEnum
	 */

	@Trans(type = TransType.DICTIONARY, key = "notice_type", ref = "typeName")
	private Integer type;

	private String typeName;

	/**
	 * 标题
	 */
	private String title;

	/**
	 * 封面图
	 */
	private String coverImageUrl;

	/**
	 * 是否置顶，0 否、1 是 NoticeTypeEnum
	 */
	@Trans(type = TransType.DICTIONARY, key = "is_top", ref = "isTopName")
	private Integer isTop;

	private String isTopName;

	/**
	 * 状态：0 草稿、1 已发布 StatusEnum
	 */
	@Trans(type = TransType.DICTIONARY, key = "notice_status", ref = "statusName")
	private Integer status;

	private String statusName;

	/**
	 * 发布时间
	 */
	private LocalDateTime publishedTime;

	/**
	 * 创建人ID
	 */
	private Integer createBy;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新人ID
	 */
	private Integer updateBy;

	private String updateByName;

	/**
	 * 通过代码更新
	 */
	private String updateByCode;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 是否删除：0 否、1 是；默认 0
	 */
	private Integer isDeleted;

	private List<NoticeAttachmentVo> fileList;

	private List<NoticeVisibilityVo> visibleRangeList;

	private String content;

	@Serial
	private static final long serialVersionUID = 1L;

}