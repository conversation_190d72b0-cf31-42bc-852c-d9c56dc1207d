package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.DefaultPO;

import lombok.Data;

/**
 * @description:
 * @author: doctor
 * @date: 2024/09/13
 * @version: 1.0.0
 */
@Data
@TableName("oum_user_financial")
public class OumUserFinancialPO extends DefaultPO {

	private static final long serialVersionUID = 1L;

	/** 类型 字典key */
	public static final String TYPE_DICT = "method_type";

	/** STATUS */
	public static final String STATUS_DICT = "COMMON_STATUS";

	/** 数据项 **/
	private String content;

	/** 是否默认，0 否、1 是 **/
	private Integer isDefaulted;

	/** 交易方式 **/
	private String method;

	/** 区域 **/
	private String region;

	/** 用户ID **/
	private Integer userId;

}
