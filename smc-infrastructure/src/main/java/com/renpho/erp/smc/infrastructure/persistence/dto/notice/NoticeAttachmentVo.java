package com.renpho.erp.smc.infrastructure.persistence.dto.notice;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 系统公告附件表
 *
 * <AUTHOR>
 * @date 2025/03/26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NoticeAttachmentVo implements Serializable {

	/**
	 * 附件名称
	 */
	private String name;

	/**
	 * 附件URL
	 */
	private String url;

	@Serial
	private static final long serialVersionUID = 1L;

}