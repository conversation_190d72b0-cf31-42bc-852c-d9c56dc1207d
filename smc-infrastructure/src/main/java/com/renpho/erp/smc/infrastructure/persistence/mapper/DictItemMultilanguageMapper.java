package com.renpho.erp.smc.infrastructure.persistence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.DictItemLanguagePO;

/**
 * 字典数据多语言 Mapper.
 *
 * <AUTHOR>
 */
public interface DictItemMultilanguageMapper extends BaseMapper<DictItemLanguagePO> {

	void deleteByDictItemId(Integer id);

	void updateLanguage(DictItemLanguagePO updatePo);

}
