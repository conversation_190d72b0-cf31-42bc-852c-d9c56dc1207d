package com.renpho.erp.smc.infrastructure.persistence.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.OumAccountPwdHistory;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 参数配置 Mapper.
 *
 * <AUTHOR>
 */
public interface OumAccountPwdHistoryMapper extends BaseMapper<OumAccountPwdHistory> {

	@Select("""
			select * from oum_account_pwd_history where user_id = #{userid} order by id desc limit #{limitNum}
			       """)
	List<OumAccountPwdHistory> getLast3Pwd(@Param("userid") int userid, int limitNum);

	@Select("""
			select * from oum_account_pwd_history where user_id = #{userid}
				and create_time > NOW() - INTERVAL #{lastTime} HOUR
				order by id desc limit 1
			       """)
	OumAccountPwdHistory getLastChangePwd(@Param("userid") int userid, @Param("lastTime") int lastTime);

}
