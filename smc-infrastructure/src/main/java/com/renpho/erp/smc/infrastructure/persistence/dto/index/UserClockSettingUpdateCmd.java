package com.renpho.erp.smc.infrastructure.persistence.dto.index;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 用户时钟配置表
 *
 * <AUTHOR>
 * @date 2025/03/26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserClockSettingUpdateCmd implements Serializable {

	/**
	 * 时区ID
	 */
	@NotNull
	private Integer timezoneId;

	@Serial
	private static final long serialVersionUID = 1L;

}