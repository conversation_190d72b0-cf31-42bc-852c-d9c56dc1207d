package com.renpho.erp.smc.infrastructure.persistence.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.smc.infrastructure.persistence.mapper.RoleMenuMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.RoleMenuPO;
import org.springframework.stereotype.Service;

/**
 * 角色菜单持久层Service 场景：使用Mybatis-Plus的IService批量方法，例：IService#saveBatch()方法.
 *
 * <AUTHOR>
 */
@Service
public class RoleMenuServiceImpl extends ServiceImpl<RoleMenuMapper, RoleMenuPO> {

}
