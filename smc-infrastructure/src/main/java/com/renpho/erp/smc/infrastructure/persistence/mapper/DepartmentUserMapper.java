package com.renpho.erp.smc.infrastructure.persistence.mapper;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.renpho.erp.smc.infrastructure.persistence.po.DepartmentUserPO;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

/**
 * 部门用户 Mapper.
 */
public interface DepartmentUserMapper extends BaseMapper<DepartmentUserPO> {

	@Delete("""
			delete from oum_department_user where user_id = #{uid} and department_id = #{deptId}
			""")
	int deleteUserDept(Integer uid, int deptId);

	int deleteByPrimaryKey(Integer id);

	// int insert(DepartmentUserPO record);

	// int insertSelective(DepartmentUserPO record);

	int updateByPrimaryKeySelective(DepartmentUserPO record);

	int updateByPrimaryKey(DepartmentUserPO record);

	/**
	 * 部门用户列表
	 * @param departmentIdSet 部门id集合
	 * @return List
	 */
	default List<DepartmentUserPO> listByDepartmentIdSet(Set<Integer> departmentIdSet) {
		if (CollectionUtils.isEmpty(departmentIdSet)) {
			return new ArrayList<>();
		}
		return this.selectList(new LambdaQueryWrapper<DepartmentUserPO>().in(DepartmentUserPO::getDepartmentId, departmentIdSet));
	}

	/**
	 * 部门用户列表
	 * @param userIdSet 用户id集合
	 * @return List
	 */
	default List<DepartmentUserPO> listByUserIdSet(Set<Integer> userIdSet) {
		if (CollectionUtils.isEmpty(userIdSet)) {
			return new ArrayList<>();
		}
		return this.selectList(new LambdaQueryWrapper<DepartmentUserPO>().in(DepartmentUserPO::getUserId, userIdSet));
	}

	/**
	 * 部门用户数
	 * @param departmentIds 部门id集合
	 * @return List
	 */
	Integer findUserCountByDepartmentId(List<Integer> departmentIds, Integer userStatus);

	/**
	 * 部门用户数
	 * @param page 分页
	 * @return List
	 */
	IPage<DepartmentUserPO> pageDepartmentUser(IPage<DepartmentUserPO> page, @Param("param") DepartmentUserPO po,
			@Param("language") String language);

	void insertDepartmentUser(@Param("param") DepartmentUserPO po);

	List<DepartmentUserPO> listDepartmentUser(String toLanguageTag);

	List<DepartmentUserPO> exportDepartmentUserList(Integer value, String language);

}
