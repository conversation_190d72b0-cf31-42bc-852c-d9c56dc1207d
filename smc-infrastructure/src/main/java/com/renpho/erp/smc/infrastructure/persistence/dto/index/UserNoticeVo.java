package com.renpho.erp.smc.infrastructure.persistence.dto.index;

import com.fhs.core.trans.vo.VO;
import com.renpho.erp.smc.infrastructure.persistence.dto.notice.NoticeAttachmentVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统公告表
 *
 * <AUTHOR>
 * @date 2025/03/25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserNoticeVo implements Serializable, VO {

	/**
	 * 主键
	 */
	private Integer id;

	/**
	 * 标题
	 */
	private String title;

	/**
	 * 封面图
	 */
	private String coverImageUrl;

	/**
	 * 内容
	 */
	private String content;

	/**
	 * 发布时间
	 */
	private LocalDateTime publishedTime;

	/**
	 * 已读时间，为空就要加※号，表示未读
	 */
	private LocalDateTime readTime;

	/**
	 * 附件列表
	 */
	private List<NoticeAttachmentVo> fileList;

	@Serial
	private static final long serialVersionUID = 1L;

}