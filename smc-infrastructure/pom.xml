<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.renpho.erp</groupId>
        <artifactId>smc-parent</artifactId>
        <version>${revision}</version>
    </parent>
    
    <artifactId>smc-infrastructure</artifactId>
    <name>${project.artifactId}</name>
    <description>基础设施层、模块</description>
    
    <dependencies>
        <!-- ==================================== -->
        <!-- the 3rd part -->
        <!-- ==================================== -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-json</artifactId>
        </dependency>
        
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>
        
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dingtalk</artifactId>
            <version>2.2.25</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibaba-dingtalk-service-sdk</artifactId>
            <version>2.0.0</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.microsoft.graph</groupId>-->
        <!--            <artifactId>microsoft-graph</artifactId>-->
        <!--            <version>6.39.0</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.azure</groupId>-->
        <!--            <artifactId>azure-identity</artifactId>-->
        <!--            <version>1.11.0</version>-->
        <!--        </dependency>-->
        
        <dependency>
            <groupId>cn.felord</groupId>
            <artifactId>wecom-sdk</artifactId>
            <version>1.3.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-email</artifactId>
            <version>1.5</version>
        </dependency>
        
        <dependency>
            <groupId>com.fhs-opensource</groupId>
            <artifactId>easy-trans-anno</artifactId>
        </dependency>
        
        <!-- ==================================== -->
        <!-- 框架依赖 -->
        <!-- ==================================== -->
        <dependency>
            <groupId>com.renpho.karma</groupId>
            <artifactId>karma-boot</artifactId>
        </dependency>
        <dependency>
            <groupId>com.renpho.karma</groupId>
            <artifactId>karma-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>com.renpho.karma</groupId>
            <artifactId>karma-lock</artifactId>
        </dependency>
        <dependency>
            <groupId>com.renpho.karma</groupId>
            <artifactId>karma-xxljob</artifactId>
        </dependency>
        
        <!-- ==================================== -->
        <!-- 内部依赖 -->
        <!-- ==================================== -->
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>smc-domain</artifactId>
        </dependency>
        
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>erp-security</artifactId>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>erp-data-permission</artifactId>
        </dependency>
        <dependency>
            <groupId>com.renpho.erp</groupId>
            <artifactId>erp-data-sensitive</artifactId>
        </dependency>
        <dependency>
            <groupId>com.renpho.erp</groupId>
            <artifactId>erp-operator-log</artifactId>
            <version>1.1.0-SNAPSHOT</version>
        </dependency>
        
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>ftm-client</artifactId>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>pds-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.renpho.erp</groupId>
            <artifactId>bpm-api</artifactId>
            <version>1.1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.renpho.erp</groupId>
            <artifactId>mdm-client</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
    </dependencies>
    
    <build>
        <plugins>
            <plugin>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
