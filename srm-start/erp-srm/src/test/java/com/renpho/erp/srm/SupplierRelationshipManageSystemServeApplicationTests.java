package com.renpho.erp.srm;

import com.renpho.erp.srm.enums.NsSyncStatus;
import com.renpho.erp.srm.enums.SupplierDataStatusEnum;
import com.renpho.erp.srm.enums.SupplierTypeEnum;
import com.renpho.erp.srm.infrastructure.facade.service.NsSupplierService;
import com.renpho.erp.srm.infrastructure.feignclient.RemoteFileFeign;
import com.renpho.erp.srm.infrastructure.persistence.mapper.supplier.purchase.PurchaseSupplierMapper;
import com.renpho.erp.srm.infrastructure.persistence.po.supplier.purchase.PurchaseSupplierPO;
import com.renpho.erp.srm.supplier.ns.bo.SupplierNsRequestRecordBaseBO;
import com.renpho.erp.srm.supplier.ns.request.SupplierRequest;
import com.renpho.erp.srm.supplier.purchase.repository.PurchaseSupplierNsRequestRecordRepo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Slf4j
@SpringBootTest
class SupplierRelationshipManageSystemServeApplicationTests {

	@Resource
	private RemoteFileFeign remoteFileService;

	@Resource
	private PurchaseSupplierMapper baseMapper;

	@Resource
	private NsSupplierService nsSupplierService;

	@Resource
	private PurchaseSupplierNsRequestRecordRepo purchaseSupplierNsRequestRecordRepo;

	@Test
	void getFileInfoTest() throws Exception {
		// R<FileDetailResponse> r = remoteFileService.getFileInfo("1838429835582316545");
		// FileDetailResponse data = r.getData();
		// log.info("{}", data);
	}

	@Test
	void contextLoads() {
		List<PurchaseSupplierPO> notPushToNsList = baseMapper.selectNotPushToNsList(null);
		log.info("查询出{}条未同步NS的采购供应商数据", notPushToNsList.size());
		if (CollectionUtils.isNotEmpty(notPushToNsList)) {
			notPushToNsList.forEach(po -> {
				SupplierRequest supplierRequest = new SupplierRequest(po.getName(),
						Objects.equals(po.getStatus(), SupplierDataStatusEnum.INACTIVE.getValue()),
						po.getSupplierCode(), SupplierTypeEnum.PURCHASE_SUPPLIER.getValue(), po.getNsSupplierId());
				SupplierNsRequestRecordBaseBO recordBaseBO = nsSupplierService.pushSupplierToNs(supplierRequest);

				boolean nsPushStatus = "true".equalsIgnoreCase(recordBaseBO.getNsPushStatus());
				int nsRecordId = purchaseSupplierNsRequestRecordRepo.insertNsRecord(recordBaseBO);
				po.setNsRecordId(nsRecordId);
				po.setNsSupplierId(recordBaseBO.getNsId());
				po.setUpdateTime(LocalDateTime.now());
				po.setNsPushStatus(nsPushStatus ? NsSyncStatus.SUCCESS.getCode() : NsSyncStatus.FAILED.getCode());
				baseMapper.updateById(po);
			});
		}
	}

}
