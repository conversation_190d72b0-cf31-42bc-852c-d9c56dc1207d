package com.renpho.erp.srm.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import java.util.HashMap;
import java.util.Map;

/**
 * 专门处理 IllegalArgumentException 异常处理器
 *
 * <AUTHOR>
 * @since 2024.12.04
 */
@ControllerAdvice
@Order(1) // 优先级最高，数字越小优先级越高
public class HighPriorityExceptionHandler {

	private static final Logger logger = LoggerFactory.getLogger(HighPriorityExceptionHandler.class);

	/**
	 * 专门处理 IllegalArgumentException
	 */
	@ExceptionHandler(IllegalArgumentException.class)
	public ResponseEntity<Map<String, Object>> handleIllegalArgumentException(IllegalArgumentException ex) {
		logger.error("IllegalArgumentException caught: {}", ex.getMessage(), ex);
		Map<String, Object> response = new HashMap<>();
		response.put("message", ex.getMessage());
		response.put("success", "false");
		response.put("code", "10-999");
		response.put("data", null);
		response.put("attached", null);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}

}
