package com.renpho.erp.srm;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.module.paramnames.ParameterNamesModule;
import com.renpho.erp.security.annotation.EnablePlatformResourceServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.TimeZone;

/**
 * 服务启动器.
 *
 * <AUTHOR>
 */
@Slf4j
@SpringBootApplication
@EnableFeignClients
@EnablePlatformResourceServer
public class SupplierRelationshipManageSystemServeApplication {

	public static void main(String[] args) {
		// 默认设置UTC 时间
		TimeZone.setDefault(TimeZone.getTimeZone("GMT"));
		SpringApplication.run(SupplierRelationshipManageSystemServeApplication.class, args);
		log.info("\nERP-SRM供应商关系管理启动成功！");
	}

}
