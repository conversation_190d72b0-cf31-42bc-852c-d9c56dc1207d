server:
  port: 9085

spring:
  cloud:
    nacos:
      discovery:
        server-addr: nacos-headless.renpho-erp-common.svc.cluster.local:8848
        namespace: f26b0449-ae0d-4915-88d5-17e09293bcb3
        port: ${server.port}
        username: erp-nacos-prod-develop
        password: 95bSJNkcPYnqor
      config:
        group: DEFAULT_GROUP
        namespace: ${spring.cloud.nacos.discovery.namespace}
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        file-extension: yml
        username: ${spring.cloud.nacos.discovery.username}
        password: ${spring.cloud.nacos.discovery.password}
        shared-configs:
          - dataId: application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
            group: DEFAULT_GROUP
            refresh: true
