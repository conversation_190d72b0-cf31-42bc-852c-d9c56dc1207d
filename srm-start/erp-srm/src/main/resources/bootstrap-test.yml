server:
  port: 9085

spring:
  cloud:
    nacos:
      discovery:
        server-addr: nacos-headless.renpho-erp-common.svc.cluster.local:8848
        namespace: 41fd7307-09cf-420d-a5fb-79eb2e7006a9
        port: ${server.port}
        username: renpho-nacos-test
        password: renphonacostest
      config:
        group: DEFAULT_GROUP
        namespace: ${spring.cloud.nacos.discovery.namespace}
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        file-extension: yml
        username: ${spring.cloud.nacos.discovery.username}
        password: ${spring.cloud.nacos.discovery.password}
        shared-configs:
          - dataId: application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
            group: DEFAULT_GROUP
            refresh: true
