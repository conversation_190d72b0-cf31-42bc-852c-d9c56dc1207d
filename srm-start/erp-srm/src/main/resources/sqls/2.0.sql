CREATE TABLE `pds_product_manager_basic`  (
                                              `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
                                              `country_region_id` int NULL DEFAULT NULL COMMENT '国家/地区ID',
                                              `product_cover_image_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品封面图片',
                                              `product_model_id` int NULL DEFAULT NULL COMMENT '产品型号ID',
                                              `brand_id` int NULL DEFAULT NULL COMMENT '品牌ID(老系统不一定有，所以可以为空)',
                                              `category_id` int NULL DEFAULT NULL COMMENT '品类ID(老系统不一定有，所以可以为空)',
                                              `color_id` int NULL DEFAULT NULL COMMENT '颜色ID(老系统不一定有，所以可以为空)',
                                              `sales_channel_id` int NULL DEFAULT NULL COMMENT '销售渠道ID(老系统不一定有，所以可以为空)',
                                              `attribute_encoding` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性编码',
                                              `version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '版本编号',
                                              `purchase_sku` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '采购SKU',
                                              `product_type` int NULL DEFAULT NULL COMMENT '产品类型,详情见字典',
                                              `market_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '市场名称',
                                              `product_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '产品描述，text格式',
                                              `product_selling_points` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '产品卖点，text格式',
                                              `product_manager_id` int NULL DEFAULT NULL COMMENT '产品负责人ID',
                                              `business_manager_id` int NULL DEFAULT NULL COMMENT '商务负责人ID',
                                              `procurement_manager_id` int NULL DEFAULT NULL COMMENT '采购负责人ID',
                                              `planning_manager_id` int NULL DEFAULT NULL COMMENT '计划负责人ID',
                                              `compliance_manager_id` int NULL DEFAULT NULL COMMENT '合规负责人ID',
                                              `product_status` int NULL DEFAULT 0 COMMENT '产品状态:0,正常; 1,正在补货;2,淘汰',
                                              `review_status` int NULL DEFAULT 0 COMMENT '审核状态:0,草稿; 1,待审核;2,审核通过;3,审核不通过',
                                              `authorize_status` int UNSIGNED NULL DEFAULT 0 COMMENT '授权状态: 0待授权、1待确认、2已授权',
                                              `adapter_included` int NULL DEFAULT 0 COMMENT '配件信息: 是否含适配器 (0: 否, 1: 是)',
                                              `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
                                              `is_deleted` int NULL DEFAULT 0 COMMENT '是否删除，0=未删除，1=删除',
                                              `update_by` int NULL DEFAULT NULL COMMENT '更新的用户ID',
                                              `create_by` int NULL DEFAULT NULL COMMENT '创建的用户ID',
                                              `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                              `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                              `submit_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
                                              `review_time` datetime NULL DEFAULT NULL COMMENT '审核完成时间',
                                              `chinese_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '中文名称',
                                              `english_name` varchar(110) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '英文名称',
                                              `status` int UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态，0=InActive，1=Active',
                                              PRIMARY KEY (`id`) USING BTREE,
                                              UNIQUE INDEX `uk_purchase_sku`(`purchase_sku` ASC) USING BTREE,
                                              INDEX `idx_brand_id`(`brand_id` ASC) USING BTREE,
                                              INDEX `idx_category_id`(`category_id` ASC) USING BTREE,
                                              INDEX `idx_country_region_id`(`country_region_id` ASC) USING BTREE,
                                              INDEX `idx_color_id`(`color_id` ASC) USING BTREE,
                                              INDEX `idx_sales_channel_id`(`sales_channel_id` ASC) USING BTREE,
                                              INDEX `idx_product_manager_id`(`product_manager_id` ASC) USING BTREE,
                                              INDEX `idx_business_manager_id`(`business_manager_id` ASC) USING BTREE,
                                              INDEX `idx_procurement_manager_id`(`procurement_manager_id` ASC) USING BTREE,
                                              INDEX `idx_planning_manager_id`(`planning_manager_id` ASC) USING BTREE,
                                              INDEX `idx_compliance_manager_id`(`compliance_manager_id` ASC) USING BTREE,
                                              INDEX `idx_product_model_id`(`product_model_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 190 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '产品管理基础表' ROW_FORMAT = DYNAMIC;

CREATE TABLE `srm_product_business_manager`  (
                                                 `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                 `product_manager_id` int NOT NULL COMMENT '产品管理主键ID',
                                                 `supplier_id` int NULL DEFAULT NULL COMMENT '采购供应商ID',
                                                 `supplier_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商编码',
                                                 `supplier_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商型号',
                                                 `spare_parts_rate` int NULL DEFAULT NULL COMMENT '备品率(‰)',
                                                 `moq` int NULL DEFAULT NULL COMMENT 'MOQ(pcs)',
                                                 `lt_days` int NULL DEFAULT NULL COMMENT 'LT(天）',
                                                 `replenishment_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '补货状态',
                                                 `review_status` int NULL DEFAULT NULL COMMENT '审核状态:0,草稿; 1,待审核;2,审核通过;3,审核不通过',
                                                 `submit_time` datetime NULL DEFAULT NULL COMMENT '提交时间',
                                                 `review_time` datetime NULL DEFAULT NULL COMMENT '审核完成时间',
                                                 `validity_date_start` date NULL DEFAULT NULL COMMENT '合同模板有效期开始时间',
                                                 `validity_date_end` date NULL DEFAULT NULL COMMENT '合同模板有效期结束时间',
                                                 `contract_template_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同模版ID',
                                                 `remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
                                                 `status` int UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态，0=InActive，1=Active',
                                                 `is_deleted` int NULL DEFAULT 0 COMMENT '是否删除，0=未删除，1=删除',
                                                 `create_by` int NULL DEFAULT NULL COMMENT '创建的用户ID',
                                                 `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                 `update_by` int NULL DEFAULT NULL COMMENT '更新的用户ID',
                                                 `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                                 PRIMARY KEY (`id`) USING BTREE,
                                                 UNIQUE INDEX `uk_product_manager_id`(`product_manager_id` ASC) USING BTREE COMMENT '唯一约束： 产品管理主键ID'
) ENGINE = InnoDB AUTO_INCREMENT = 47 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '产品商务管理表' ROW_FORMAT = Dynamic;