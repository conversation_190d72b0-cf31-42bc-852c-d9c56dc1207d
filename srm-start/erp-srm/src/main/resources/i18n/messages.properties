SUCCESS=Success
excel.error_msg=Error message
SYSTEM_EXCEPTION=System Exception
PARAM_NAME_FOUND=ParamName:{0},exists
PARAM_FOUND=Param:{0},exists
PARAM_NOT_FOUND=ParamNameNotFound
IMPORT_NONNULL=Import data is empty
IMPORT_VERIFY_FAIL=Import data verification failed
INVALID_REQUEST_CONTENT=Invalid Request Content
INVALID_REQUEST_CONTENT_PLACEHOLDER=Invalid Request Content:{0}
SUPPLIER_CODE_FOUND=Supplier code:{0},exists
SUPPLIER_NAME_FOUND=Supplier name:{0},exists
SUPPLIER_EN_NAME_FOUND=Supplier English name:{0},exists
DATA_NOT_FOUND_CODE=The data does not exist
SUPPLIER_STATUS_ERROR=The supplier is not in draft status and cannot be deleted
SUPPLIER_PUSH_NS_ERROR=The supplier push NS error: {0}
REMOTE_INVOKE_ERROR=Remote call error: {0}
ASSIGN_TYPE_ERROR=Wrong allocation type: {0}
# Product Business Manager
PRODUCT_MANAGER_ID_NOT_NULL=Product Manager ID is not null
PRODUCT_BUSINESS_ID_NOT_NULL=Product Business ID is not null
SUPPLIER_ID_NOT_NULL=Supplier Code is not null
SUPPLIER_TYPE_MAX_LENGTH=Supplier type must be at most 10 characters long
SUPPLIER_TYPE_PATTERN=Supplier type can only include Chinese, English, numbers, and ?-?
SPARE_PARTS_RATE_NOT_NULL=Spare parts rate is required
SPARE_PARTS_RATE_MIN_VALUE=Spare parts rate must be at least 0
SPARE_PARTS_RATE_MAX_VALUE=Spare parts rate must not exceed 9999
MOQ_NOT_NULL=MOQ is required
MOQ_MIN_VALUE=MOQ must be at least 0
MOQ_MAX_VALUE=MOQ must not exceed 99999
LT_NOT_NULL=LT is required
LT_MIN_VALUE=LT must be at least 0
LT_MAX_VALUE=LT must not exceed 999
REPLENISHMENT_STATUS_NOT_NULL=Replenishment status is required
REMARK_MAX_LENGTH=Remark must be at most 255 characters long
SUPPLIER_AND_PRODUCT_MANAGER_UK_EXISTS=One piece of product management master data can only be bound to one piece of supplier data.
# Product Business Management Import
excel.psku=PSKU
excel.supplier_id=供应商Code
excel.supplier_level_value=供应商等级
excel.supplier_type=供应商型号
excel.spare_parts_rate=备品率(‰)
excel.moq=MOQ(pcs)
excel.lt_days=LT(天)
excel.product_status=补货状态
PRODUCT_MANAGER_DATA_NOT_EXISTS=The Product Manager data does not exist
PRODUCT_MANAGER_NOT_APPROVED=The Product Manager is not approved
SUPPLIER_DATA_NOT_EXISTS=The Supplier data does not exist
# Product Business Management Export
excel.purchase_sku=PKSU
excel.model_no=Product Model
excel.product_name=Product Name
excel.category_name=Category Name
excel.product_type_name=Product Type
excel.supplier_name=Supplier Name
excel.supplier_level_name=Supplier Level
excel.product_status_name=Replenishment Status
excel.contract_template_id=Contract Template
excel.validity_date_start=Begin of validity
excel.validity_date_end=End of validity
excel.business_manager_name=Business Manager
excel.product_manager_name=Product Manager
excel.create_time=Creation Time
excel.submit_time=Submit Time
excel.review_time=Review Time
excel.update_time=Update Time
excel.export.supplier_id=Supplier Code
excel.export.spare_parts_rate=Spare parts rate
excel.export.moq=MOQ
REVIEW_STATUS_NOT_EXISTS=The review status does not exist

# Product Price Manager
excel.currency_type =Currency
excel.tax_rate=Tax Rate (%)
excel.inclusive_tax_price=Inclusive Tax Price
excel.exchange_rate_start_point=Exchange Rate Start Point
excel.exchange_rate_end_point=Exchange Rate End Point
excel.quantity_start_point=Quantity Start Point
excel.quantity_end_point=Quantity End Point
excel.price_list_number= Price List Number
excel.product_model=Product Model
excel.business_manager=Business Manager
excel.supplier_code=Supplier Code
excel.validity_period_start_time=Effective Start Date
excel.validity_period_end_time=Effective End Date
excel.price_price_status=Status
excel.file_number=File Number
excel.remark=Remark
excel.audit_time= Audit Time 
PURCHASE_SKU= sku Not Null
CURRENCY_TYPE=Currency Not Null
TAX_PRICE =Tax Price Not Null 
TAX_PRICE_PATTERN = The tax rate is filled in incorrectly
INCLUSIVE_TAX_PRICE= Inclusive Tax Price Not Null
INCLUSIVE_TAX_PRICE_PATTERN=The tax-included unit price is filled in incorrectly
EXCHANGE_RATE_START_POINT=Exchange Rate Start Point Not Null
EXCHANGE_RATE_START_POINT_PATTERN = The starting range or format of the exchange rate is incorrect
EXCHANGE_RATE_END_POINT=Exchange Rate End Point Not Null
EXCHANGE_RATE_END_POINT_PATTERN = The end range or format of the exchange rate is incorrect
QUANTITY_START_POINT= Quantity Start Point Not Null
QUANTITY_START_POINT_PATTERN=Quantity starting range or format error
QUANTITY_END_POINT= Quantity End Point Not Null
QUANTITY_END_POINT_PATTERN=Quantity end range or format error
VALIDITY_PERIOD_START_TIME = Validity Period Start Time Not Null
VALIDITY_PERIOD_START_TIME_PATTERN=The start date format is incorrect
VALIDITY_PERIOD_END_TIME = Validity Period End Time Not Null
VALIDITY_PERIOD_END_TIME_PATTERN=The end date format is incorrect
EXPORT_DATA_IS_NULL = export data is null
THE_PSKU_HAS_THE_SAME_DATA= the PSKU has the same data
PRODUCT_PRICE_HAVA_AUDIT_DATA=product psku hava audit data: {0}
PRODUCT_PSKU_DOES_NOT_EXIST_OR_HAS_NOT_APPROVED=The PSKU does not exist or has not been approved
THE_PSKU_HAS_NOT_BEEN_BOUND_TO_THE_SUPPLIER=The PSKU has not been bound to the supplier.
THE_PRODUCT_PRICE_NUMBER_INCOMPLETE_INFO={0} The price list number information is incomplete. Please complete the information before submitting.
DATA_NOT_FOUNT=The data does not exist
FILE_UPLOAD_MAX= The maximum number of files for upload has been hit
CONTRACT_DATA_NOT_FOUNT= The contract template data does not exist
DEFAULT_CONTRACT_NOT_EDIT=Generic templates disable editing
CONTRACT_NAME_IS_NULL=Template name cannot be empty
CONTRACT_DISABLE =Contract template has been disabled, please contact business personnel
FINANCIAL_INFO_NOT_EMPTY = New Vendor, Financial Information Required
SETTLEMENT_INFO_NOT_EMPTY = New Vendor, Billing Information Required 