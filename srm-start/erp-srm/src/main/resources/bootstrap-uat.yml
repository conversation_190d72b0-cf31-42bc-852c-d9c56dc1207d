server:
  port: 9085

spring:
  cloud:
    nacos:
      discovery:
        server-addr: nacos-headless.renpho-erp-common.svc.cluster.local:8848
        namespace: 616b47fb-d803-4a60-824d-c2fed4b3e3e2
        port: ${server.port}
        username: renpho-nacos-uat
        password: renphonacosuat
      config:
        group: DEFAULT_GROUP
        namespace: ${spring.cloud.nacos.discovery.namespace}
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        file-extension: yml
        username: ${spring.cloud.nacos.discovery.username}
        password: ${spring.cloud.nacos.discovery.password}
        shared-configs:
          - dataId: application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
            group: DEFAULT_GROUP
            refresh: true
