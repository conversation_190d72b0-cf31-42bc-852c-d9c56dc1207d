server:
  port: 9085

spring:
  cloud:
    nacos:
      discovery:
        server-addr: renpho.master.com:30848
        namespace: 5c21c56e-3331-44bd-975a-7961c8cf9c30
        port: ${server.port}
        username: renpho-nacos-local
        password: renphonacoslocal
      config:
        group: DEFAULT_GROUP
        namespace: ${spring.cloud.nacos.discovery.namespace}
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        file-extension: yml
        username: ${spring.cloud.nacos.discovery.username}
        password: ${spring.cloud.nacos.discovery.password}
        shared-configs:
          - dataId: application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
            group: DEFAULT_GROUP
            refresh: true


mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

logging:
  level:
    com.baomidou.mybatisplus: debug