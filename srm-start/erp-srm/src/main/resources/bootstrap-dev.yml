server:
  port: 9085

spring:
  cloud:
    nacos:
      discovery:
        server-addr: nacos-headless.renpho-erp-common.svc.cluster.local:8848
        namespace: e2d3f421-ee9e-4b20-9b20-80710bba1b83
        port: ${server.port}
        username: renpho-nacos-dev
        password: renphonacosdev
      config:
        group: DEFAULT_GROUP
        namespace: ${spring.cloud.nacos.discovery.namespace}
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        file-extension: yml
        username: ${spring.cloud.nacos.discovery.username}
        password: ${spring.cloud.nacos.discovery.password}
        shared-configs:
          - dataId: application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
            group: DEFAULT_GROUP
            refresh: true
