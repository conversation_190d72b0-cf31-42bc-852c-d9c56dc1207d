package com.renpho.erp.tms.infrastructure.persistence.transferorder.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.TransferOrderPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 调拨单 Mapper
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Mapper
public interface TransferOrderMapper extends BaseMapper<TransferOrderPO> {

    /**
     * 根据调拨单号查询
     *
     * @param tsNo 调拨单号
     * @return 调拨单PO
     */
    TransferOrderPO selectByTsNo(@Param("tsNo") String tsNo);

    /**
     * 根据OMS单号查询
     *
     * @param orderNo OMS单号
     * @return 调拨单PO列表
     */
    List<TransferOrderPO> selectByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 根据状态查询
     *
     * @param status 状态
     * @return 调拨单PO列表
     */
    List<TransferOrderPO> selectByStatus(@Param("status") Integer status);

    /**
     * 根据货主ID查询
     *
     * @param ownerId 货主ID
     * @return 调拨单PO列表
     */
    List<TransferOrderPO> selectByOwnerId(@Param("ownerId") Integer ownerId);

    /**
     * 根据店铺ID查询
     *
     * @param storeId 店铺ID
     * @return 调拨单PO列表
     */
    List<TransferOrderPO> selectByStoreId(@Param("storeId") Integer storeId);
}
