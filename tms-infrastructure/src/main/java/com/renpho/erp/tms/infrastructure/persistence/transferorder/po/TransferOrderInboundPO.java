package com.renpho.erp.tms.infrastructure.persistence.transferorder.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.DefaultPO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 调拨单入库表 PO
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Getter
@Setter
@TableName("tms_transfer_order_inbound")
public class TransferOrderInboundPO extends DefaultPO {

    @Serial
    private static final long serialVersionUID = -3462217093720694824L;

    /**
     * 调拨单ID
     */
    @TableField(value = "ts_id")
    private Integer tsId;

    /**
     * 调拨单号
     */
    @TableField(value = "ts_no")
    private String tsNo;

    /**
     * PSKU
     */
    @TableField(value = "psku")
    private String psku;

    /**
     * FNSKU
     */
    @TableField(value = "fnsku")
    private String fnsku;

    /**
     * 入库单号
     */
    @TableField(value = "inbound_no")
    private String inboundNo;

    /**
     * 数量
     */
    @TableField(value = "qty")
    private Integer qty;

    /**
     * 期望上架时间
     */
    @TableField(value = "expected_putaway_time")
    private LocalDateTime expectedPutawayTime;

    /**
     * 签收开始时间
     */
    @TableField(value = "received_start_time")
    private LocalDateTime receivedStartTime;

    /**
     * 签收结束时间
     */
    @TableField(value = "received_end_time")
    private LocalDateTime receivedEndTime;

    /**
     * 上架开始时间
     */
    @TableField(value = "putaway_start_time")
    private LocalDateTime putawayStartTime;

    /**
     * 上架结束时间
     */
    @TableField(value = "putaway_end_time")
    private LocalDateTime putawayEndTime;
}
