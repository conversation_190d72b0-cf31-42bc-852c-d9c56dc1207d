package com.renpho.erp.tms.infrastructure.persistence.transferorder.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.tms.domain.transferorder.TransferOrderOutbound;
import com.renpho.erp.tms.domain.transferorder.TransferOrderOutboundId;
import com.renpho.erp.tms.domain.transferorder.TransferOrderOutboundLookup;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.mapper.TransferOrderOutboundMapper;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.TransferOrderOutboundPO;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.converter.TransferOrderOutboundConverter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 调拨单出库查询实现
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Repository
@RequiredArgsConstructor
public class TransferOrderOutboundLookupImpl extends ServiceImpl<TransferOrderOutboundMapper, TransferOrderOutboundPO> implements TransferOrderOutboundLookup {

    private final TransferOrderOutboundConverter transferOrderOutboundConverter;

    @Override
    public Optional<TransferOrderOutbound> findById(TransferOrderOutboundId transferOrderOutboundId) {
        TransferOrderOutboundPO po = getById(transferOrderOutboundId.id());
        return Optional.ofNullable(po).map(transferOrderOutboundConverter::toDomain);
    }

    @Override
    public List<TransferOrderOutbound> findByTsId(Integer tsId) {
        List<TransferOrderOutboundPO> pos = baseMapper.selectByTsId(tsId);
        return transferOrderOutboundConverter.toDomains(pos);
    }

    @Override
    public List<TransferOrderOutbound> findByTsNo(String tsNo) {
        List<TransferOrderOutboundPO> pos = baseMapper.selectByTsNo(tsNo);
        return transferOrderOutboundConverter.toDomains(pos);
    }

    @Override
    public Optional<TransferOrderOutbound> findByOutboundNo(String outboundNo) {
        TransferOrderOutboundPO po = baseMapper.selectByOutboundNo(outboundNo);
        return Optional.ofNullable(po).map(transferOrderOutboundConverter::toDomain);
    }

    @Override
    public List<TransferOrderOutbound> findByPsku(String psku) {
        List<TransferOrderOutboundPO> pos = baseMapper.selectByPsku(psku);
        return transferOrderOutboundConverter.toDomains(pos);
    }

    @Override
    public List<TransferOrderOutbound> findByLogisticsModeId(Integer logisticsModeId) {
        List<TransferOrderOutboundPO> pos = baseMapper.selectByLogisticsModeId(logisticsModeId);
        return transferOrderOutboundConverter.toDomains(pos);
    }
}
