package com.renpho.erp.tms.infrastructure.persistence.transferorder.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.TransferOrderItemPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 调拨单商品 Mapper
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Mapper
public interface TransferOrderItemMapper extends BaseMapper<TransferOrderItemPO> {

    /**
     * 根据调拨单ID查询商品列表
     *
     * @param tsId 调拨单ID
     * @return 调拨单商品PO列表
     */
    List<TransferOrderItemPO> selectByTsId(@Param("tsId") Integer tsId);

    /**
     * 根据调拨单号查询商品列表
     *
     * @param tsNo 调拨单号
     * @return 调拨单商品PO列表
     */
    List<TransferOrderItemPO> selectByTsNo(@Param("tsNo") String tsNo);

    /**
     * 根据PSKU查询
     *
     * @param psku PSKU
     * @return 调拨单商品PO列表
     */
    List<TransferOrderItemPO> selectByPsku(@Param("psku") String psku);

    /**
     * 根据出库单号查询
     *
     * @param outboundNo 出库单号
     * @return 调拨单商品PO列表
     */
    List<TransferOrderItemPO> selectByOutboundNo(@Param("outboundNo") String outboundNo);
}
