package com.renpho.erp.tms.infrastructure.persistence.transferorder.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.tms.domain.transferorder.TransferOrderOutbound;
import com.renpho.erp.tms.domain.transferorder.TransferOrderOutboundId;
import com.renpho.erp.tms.domain.transferorder.TransferOrderOutboundRepository;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.mapper.TransferOrderOutboundMapper;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.TransferOrderOutboundPO;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.converter.TransferOrderOutboundConverter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 调拨单出库仓储实现
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Repository
@RequiredArgsConstructor
public class TransferOrderOutboundRepositoryImpl extends ServiceImpl<TransferOrderOutboundMapper, TransferOrderOutboundPO> implements TransferOrderOutboundRepository {

    private final TransferOrderOutboundConverter transferOrderOutboundConverter;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TransferOrderOutboundId save(TransferOrderOutbound transferOrderOutbound) {
        TransferOrderOutboundPO po = transferOrderOutboundConverter.toPO(transferOrderOutbound);
        save(po);
        return new TransferOrderOutboundId(po.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TransferOrderOutbound transferOrderOutbound) {
        TransferOrderOutboundPO po = transferOrderOutboundConverter.toPO(transferOrderOutbound);
        updateById(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSave(List<TransferOrderOutbound> transferOrderOutbounds) {
        List<TransferOrderOutboundPO> pos = transferOrderOutboundConverter.toPOs(transferOrderOutbounds);
        saveBatch(pos);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(TransferOrderOutboundId transferOrderOutboundId) {
        removeById(transferOrderOutboundId.id());
    }
}
