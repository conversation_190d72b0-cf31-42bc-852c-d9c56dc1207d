package com.renpho.erp.tms.infrastructure.persistence.transferorder.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.tms.domain.transferorder.TransferOrder;
import com.renpho.erp.tms.domain.transferorder.TransferOrderId;
import com.renpho.erp.tms.domain.transferorder.TransferOrderLookup;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.mapper.TransferOrderMapper;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.TransferOrderPO;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.converter.TransferOrderConverter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 调拨单查询实现
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Repository
@RequiredArgsConstructor
public class TransferOrderLookupImpl extends ServiceImpl<TransferOrderMapper, TransferOrderPO> implements TransferOrderLookup {

    private final TransferOrderConverter transferOrderConverter;

    @Override
    public Optional<TransferOrder> findById(TransferOrderId transferOrderId) {
        TransferOrderPO po = getById(transferOrderId.id());
        return Optional.ofNullable(po).map(transferOrderConverter::toDomain);
    }

    @Override
    public Optional<TransferOrder> findByTsNo(String tsNo) {
        TransferOrderPO po = baseMapper.selectByTsNo(tsNo);
        return Optional.ofNullable(po).map(transferOrderConverter::toDomain);
    }

    @Override
    public List<TransferOrder> findByOrderNo(String orderNo) {
        List<TransferOrderPO> pos = baseMapper.selectByOrderNo(orderNo);
        return transferOrderConverter.toDomains(pos);
    }

    @Override
    public List<TransferOrder> findByStatus(Integer status) {
        List<TransferOrderPO> pos = baseMapper.selectByStatus(status);
        return transferOrderConverter.toDomains(pos);
    }

    @Override
    public List<TransferOrder> findByOwnerId(Integer ownerId) {
        List<TransferOrderPO> pos = baseMapper.selectByOwnerId(ownerId);
        return transferOrderConverter.toDomains(pos);
    }

    @Override
    public List<TransferOrder> findByStoreId(Integer storeId) {
        List<TransferOrderPO> pos = baseMapper.selectByStoreId(storeId);
        return transferOrderConverter.toDomains(pos);
    }
}
