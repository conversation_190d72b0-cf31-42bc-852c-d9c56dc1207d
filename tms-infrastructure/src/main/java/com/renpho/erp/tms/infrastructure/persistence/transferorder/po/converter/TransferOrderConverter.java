package com.renpho.erp.tms.infrastructure.persistence.transferorder.po.converter;

import com.renpho.erp.tms.domain.transferorder.TransferOrder;
import com.renpho.erp.tms.domain.transferorder.TransferOrderId;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.TransferOrderPO;
import com.renpho.erp.tms.infrastructure.remote.user.converter.OperatorConverter;
import org.mapstruct.*;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * 调拨单转换器
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE,
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {OperatorConverter.class})
public interface TransferOrderConverter {

    @Mapping(target = "id.id", source = "id")
    @Mapping(target = "created.operatorId.id", source = "createBy")
    @Mapping(target = "created.operateTime", source = "createTime")
    @Mapping(target = "updated.operatorId.id", source = "updateBy")
    @Mapping(target = "updated.operateTime", source = "updateTime")
    TransferOrder toDomain(TransferOrderPO po);

    @IterableMapping(nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
    List<TransferOrder> toDomains(Collection<TransferOrderPO> pos);

    @Mapping(target = "id", source = "id.id")
    @Mapping(target = "createBy", source = "created.operatorId.id")
    @Mapping(target = "createTime", source = "created.operateTime")
    @Mapping(target = "updateBy", source = "updated.operatorId.id")
    @Mapping(target = "updateTime", source = "updated.operateTime")
    @InheritInverseConfiguration(name = "toDomain")
    TransferOrderPO toPO(TransferOrder domain);

    @IterableMapping(nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
    List<TransferOrderPO> toPOs(Collection<TransferOrder> domains);

    @Mapping(target = "id", source = "id")
    TransferOrderId toId(Integer id);

    default Integer toId(TransferOrderId id) {
        return Optional.ofNullable(id).map(TransferOrderId::id).orElse(null);
    }
}
