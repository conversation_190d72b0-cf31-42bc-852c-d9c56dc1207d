package com.renpho.erp.tms.infrastructure.persistence.transferorder.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.TransferOrderOutboundPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 调拨单出库 Mapper
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Mapper
public interface TransferOrderOutboundMapper extends BaseMapper<TransferOrderOutboundPO> {

    /**
     * 根据调拨单ID查询出库记录
     *
     * @param tsId 调拨单ID
     * @return 出库记录PO列表
     */
    List<TransferOrderOutboundPO> selectByTsId(@Param("tsId") Integer tsId);

    /**
     * 根据调拨单号查询出库记录
     *
     * @param tsNo 调拨单号
     * @return 出库记录PO列表
     */
    List<TransferOrderOutboundPO> selectByTsNo(@Param("tsNo") String tsNo);

    /**
     * 根据出库单号查询
     *
     * @param outboundNo 出库单号
     * @return 出库记录PO
     */
    TransferOrderOutboundPO selectByOutboundNo(@Param("outboundNo") String outboundNo);

    /**
     * 根据PSKU查询出库记录
     *
     * @param psku PSKU
     * @return 出库记录PO列表
     */
    List<TransferOrderOutboundPO> selectByPsku(@Param("psku") String psku);

    /**
     * 根据物流方式ID查询出库记录
     *
     * @param logisticsModeId 物流方式ID
     * @return 出库记录PO列表
     */
    List<TransferOrderOutboundPO> selectByLogisticsModeId(@Param("logisticsModeId") Integer logisticsModeId);
}
