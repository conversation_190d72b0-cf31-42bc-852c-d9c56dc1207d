package com.renpho.erp.tms.infrastructure.persistence.transferorder.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.tms.domain.transferorder.TransferOrderInbound;
import com.renpho.erp.tms.domain.transferorder.TransferOrderInboundId;
import com.renpho.erp.tms.domain.transferorder.TransferOrderInboundLookup;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.mapper.TransferOrderInboundMapper;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.TransferOrderInboundPO;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.converter.TransferOrderInboundConverter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 调拨单入库查询实现
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Repository
@RequiredArgsConstructor
public class TransferOrderInboundLookupImpl extends ServiceImpl<TransferOrderInboundMapper, TransferOrderInboundPO> implements TransferOrderInboundLookup {

    private final TransferOrderInboundConverter transferOrderInboundConverter;

    @Override
    public Optional<TransferOrderInbound> findById(TransferOrderInboundId transferOrderInboundId) {
        TransferOrderInboundPO po = getById(transferOrderInboundId.id());
        return Optional.ofNullable(po).map(transferOrderInboundConverter::toDomain);
    }

    @Override
    public List<TransferOrderInbound> findByTsId(Integer tsId) {
        List<TransferOrderInboundPO> pos = baseMapper.selectByTsId(tsId);
        return transferOrderInboundConverter.toDomains(pos);
    }

    @Override
    public List<TransferOrderInbound> findByTsNo(String tsNo) {
        List<TransferOrderInboundPO> pos = baseMapper.selectByTsNo(tsNo);
        return transferOrderInboundConverter.toDomains(pos);
    }

    @Override
    public Optional<TransferOrderInbound> findByInboundNo(String inboundNo) {
        TransferOrderInboundPO po = baseMapper.selectByInboundNo(inboundNo);
        return Optional.ofNullable(po).map(transferOrderInboundConverter::toDomain);
    }

    @Override
    public List<TransferOrderInbound> findByPsku(String psku) {
        List<TransferOrderInboundPO> pos = baseMapper.selectByPsku(psku);
        return transferOrderInboundConverter.toDomains(pos);
    }

    @Override
    public List<TransferOrderInbound> findByFnsku(String fnsku) {
        List<TransferOrderInboundPO> pos = baseMapper.selectByFnsku(fnsku);
        return transferOrderInboundConverter.toDomains(pos);
    }
}
