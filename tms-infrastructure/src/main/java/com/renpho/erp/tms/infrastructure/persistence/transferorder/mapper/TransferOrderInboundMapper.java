package com.renpho.erp.tms.infrastructure.persistence.transferorder.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.TransferOrderInboundPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 调拨单入库 Mapper
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Mapper
public interface TransferOrderInboundMapper extends BaseMapper<TransferOrderInboundPO> {

    /**
     * 根据调拨单ID查询入库记录
     *
     * @param tsId 调拨单ID
     * @return 入库记录PO列表
     */
    List<TransferOrderInboundPO> selectByTsId(@Param("tsId") Integer tsId);

    /**
     * 根据调拨单号查询入库记录
     *
     * @param tsNo 调拨单号
     * @return 入库记录PO列表
     */
    List<TransferOrderInboundPO> selectByTsNo(@Param("tsNo") String tsNo);

    /**
     * 根据入库单号查询
     *
     * @param inboundNo 入库单号
     * @return 入库记录PO
     */
    TransferOrderInboundPO selectByInboundNo(@Param("inboundNo") String inboundNo);

    /**
     * 根据PSKU查询入库记录
     *
     * @param psku PSKU
     * @return 入库记录PO列表
     */
    List<TransferOrderInboundPO> selectByPsku(@Param("psku") String psku);

    /**
     * 根据FNSKU查询入库记录
     *
     * @param fnsku FNSKU
     * @return 入库记录PO列表
     */
    List<TransferOrderInboundPO> selectByFnsku(@Param("fnsku") String fnsku);
}
