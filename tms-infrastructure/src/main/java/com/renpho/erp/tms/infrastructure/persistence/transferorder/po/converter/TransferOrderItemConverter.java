package com.renpho.erp.tms.infrastructure.persistence.transferorder.po.converter;

import com.renpho.erp.tms.domain.transferorder.TransferOrderItem;
import com.renpho.erp.tms.domain.transferorder.TransferOrderItemId;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.TransferOrderItemPO;
import com.renpho.erp.tms.infrastructure.remote.user.converter.OperatorConverter;
import org.mapstruct.*;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * 调拨单商品转换器
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE,
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {OperatorConverter.class})
public interface TransferOrderItemConverter {

    @Mapping(target = "id.id", source = "id")
    @Mapping(target = "created.operatorId.id", source = "createBy")
    @Mapping(target = "created.operateTime", source = "createTime")
    @Mapping(target = "updated.operatorId.id", source = "updateBy")
    @Mapping(target = "updated.operateTime", source = "updateTime")
    TransferOrderItem toDomain(TransferOrderItemPO po);

    @IterableMapping(nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
    List<TransferOrderItem> toDomains(Collection<TransferOrderItemPO> pos);

    @Mapping(target = "id", source = "id.id")
    @Mapping(target = "createBy", source = "created.operatorId.id")
    @Mapping(target = "createTime", source = "created.operateTime")
    @Mapping(target = "updateBy", source = "updated.operatorId.id")
    @Mapping(target = "updateTime", source = "updated.operateTime")
    @InheritInverseConfiguration(name = "toDomain")
    TransferOrderItemPO toPO(TransferOrderItem domain);

    @IterableMapping(nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
    List<TransferOrderItemPO> toPOs(Collection<TransferOrderItem> domains);

    @Mapping(target = "id", source = "id")
    TransferOrderItemId toId(Integer id);

    default Integer toId(TransferOrderItemId id) {
        return Optional.ofNullable(id).map(TransferOrderItemId::id).orElse(null);
    }
}
