package com.renpho.erp.tms.infrastructure.persistence.transferorder.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.DefaultPO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 调拨单出库表 PO
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Getter
@Setter
@TableName("tms_transfer_order_outbound")
public class TransferOrderOutboundPO extends DefaultPO {

    @Serial
    private static final long serialVersionUID = 1281713568786330208L;

    /**
     * 调拨单ID
     */
    @TableField(value = "ts_id")
    private Integer tsId;

    /**
     * 调拨单号
     */
    @TableField(value = "ts_no")
    private String tsNo;

    /**
     * PSKU
     */
    @TableField(value = "psku")
    private String psku;

    /**
     * FNSKU
     */
    @TableField(value = "fnsku")
    private String fnsku;

    /**
     * 出库单号
     */
    @TableField(value = "outbound_no")
    private String outboundNo;

    /**
     * 数量
     */
    @TableField(value = "qty")
    private Integer qty;

    /**
     * 发货时间
     */
    @TableField(value = "departure_time")
    private LocalDateTime departureTime;

    /**
     * 物流方式ID
     */
    @TableField(value = "logistics_mode_id")
    private Integer logisticsModeId;

    /**
     * 是否打托, 0-否, 1-是
     */
    @TableField(value = "is_palletized")
    private Boolean isPalletized;

    /**
     * 发货要求
     */
    @TableField(value = "delivery_requirement")
    private String deliveryRequirement;

    /**
     * 是否借货, 0-否, 1-是
     */
    @TableField(value = "is_borrowed")
    private Boolean isBorrowed;

    /**
     * 是否换标, 0-否, 1-是
     */
    @TableField(value = "is_relabel")
    private Boolean isRelabel;

}
