package com.renpho.erp.tms.infrastructure.persistence.transferorder.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.tms.domain.transferorder.TransferOrder;
import com.renpho.erp.tms.domain.transferorder.TransferOrderId;
import com.renpho.erp.tms.domain.transferorder.TransferOrderRepository;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.mapper.TransferOrderMapper;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.TransferOrderPO;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.converter.TransferOrderConverter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 调拨单仓储实现
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Repository
@RequiredArgsConstructor
public class TransferOrderRepositoryImpl extends ServiceImpl<TransferOrderMapper, TransferOrderPO> implements TransferOrderRepository {

    private final TransferOrderConverter transferOrderConverter;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TransferOrderId save(TransferOrder transferOrder) {
        TransferOrderPO po = transferOrderConverter.toPO(transferOrder);
        save(po);
        return new TransferOrderId(po.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TransferOrder transferOrder) {
        TransferOrderPO po = transferOrderConverter.toPO(transferOrder);
        updateById(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSave(List<TransferOrder> transferOrders) {
        List<TransferOrderPO> pos = transferOrderConverter.toPOs(transferOrders);
        saveBatch(pos);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(TransferOrderId transferOrderId) {
        removeById(transferOrderId.id());
    }
}
