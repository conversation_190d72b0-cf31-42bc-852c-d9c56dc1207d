package com.renpho.erp.tms.infrastructure.persistence.transferorder.po.converter;

import com.renpho.erp.tms.domain.transferorder.TransferOrderOutbound;
import com.renpho.erp.tms.domain.transferorder.TransferOrderOutboundId;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.TransferOrderOutboundPO;
import com.renpho.erp.tms.infrastructure.remote.user.converter.OperatorConverter;
import org.mapstruct.*;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * 调拨单出库转换器
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE,
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {OperatorConverter.class})
public interface TransferOrderOutboundConverter {

    @Mapping(target = "id.id", source = "id")
    @Mapping(target = "created.operatorId.id", source = "createBy")
    @Mapping(target = "created.operateTime", source = "createTime")
    @Mapping(target = "updated.operatorId.id", source = "updateBy")
    @Mapping(target = "updated.operateTime", source = "updateTime")
    TransferOrderOutbound toDomain(TransferOrderOutboundPO po);

    @IterableMapping(nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
    List<TransferOrderOutbound> toDomains(Collection<TransferOrderOutboundPO> pos);

    @Mapping(target = "id", source = "id.id")
    @Mapping(target = "createBy", source = "created.operatorId.id")
    @Mapping(target = "createTime", source = "created.operateTime")
    @Mapping(target = "updateBy", source = "updated.operatorId.id")
    @Mapping(target = "updateTime", source = "updated.operateTime")
    @InheritInverseConfiguration(name = "toDomain")
    TransferOrderOutboundPO toPO(TransferOrderOutbound domain);

    @IterableMapping(nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
    List<TransferOrderOutboundPO> toPOs(Collection<TransferOrderOutbound> domains);

    @Mapping(target = "id", source = "id")
    TransferOrderOutboundId toId(Integer id);

    default Integer toId(TransferOrderOutboundId id) {
        return Optional.ofNullable(id).map(TransferOrderOutboundId::id).orElse(null);
    }
}
