package com.renpho.erp.tms.infrastructure.persistence.transferorder.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.tms.domain.transferorder.TransferOrderInbound;
import com.renpho.erp.tms.domain.transferorder.TransferOrderInboundId;
import com.renpho.erp.tms.domain.transferorder.TransferOrderInboundRepository;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.mapper.TransferOrderInboundMapper;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.TransferOrderInboundPO;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.converter.TransferOrderInboundConverter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 调拨单入库仓储实现
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Repository
@RequiredArgsConstructor
public class TransferOrderInboundRepositoryImpl extends ServiceImpl<TransferOrderInboundMapper, TransferOrderInboundPO> implements TransferOrderInboundRepository {

    private final TransferOrderInboundConverter transferOrderInboundConverter;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TransferOrderInboundId save(TransferOrderInbound transferOrderInbound) {
        TransferOrderInboundPO po = transferOrderInboundConverter.toPO(transferOrderInbound);
        save(po);
        return new TransferOrderInboundId(po.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TransferOrderInbound transferOrderInbound) {
        TransferOrderInboundPO po = transferOrderInboundConverter.toPO(transferOrderInbound);
        updateById(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSave(List<TransferOrderInbound> transferOrderInbounds) {
        List<TransferOrderInboundPO> pos = transferOrderInboundConverter.toPOs(transferOrderInbounds);
        saveBatch(pos);
    }

    @Override
    public void delete(TransferOrderInboundId transferOrderInboundId) {
        removeById(transferOrderInboundId.id());
    }
}
